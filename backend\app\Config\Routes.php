<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */

// Frontend Assets - Serve static files
$routes->get('frontend/assets/(:any)', 'Frontend::assets/$1');
$routes->get('frontend/favicon.ico', function() {
    $frontendDistPath = ROOTPATH . '../frontend/dist/favicon.ico';
    if (file_exists($frontendDistPath)) {
        header('Content-Type: image/x-icon');
        readfile($frontendDistPath);
        exit;
    }
    throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
});

// Frontend Routes - Serve Vue.js application
$routes->get('/', 'Frontend::index');
$routes->get('login', 'Frontend::index');
$routes->get('dashboard', 'Frontend::index');
$routes->get('courses', 'Frontend::index');
$routes->get('courses/(:any)', 'Frontend::index');
$routes->get('cpmk', 'Frontend::index');
$routes->get('cpl', 'Frontend::index');
$routes->get('assessments', 'Frontend::index');
$routes->get('reports', 'Frontend::index');
$routes->get('users', 'Frontend::index');
$routes->get('faculties', 'Frontend::index');
$routes->get('study-programs', 'Frontend::index');
$routes->get('profile', 'Frontend::index');

// API Routes
$routes->group('api/v1', ['namespace' => 'App\Controllers\Api'], function($routes) {
    // Authentication routes
    $routes->post('auth/login', 'AuthController::login');
    $routes->post('auth/logout', 'AuthController::logout');
    $routes->post('auth/refresh', 'AuthController::refresh');
    $routes->get('auth/profile', 'AuthController::profile', ['filter' => 'auth']);

    // Protected routes
    $routes->group('', ['filter' => 'auth'], function($routes) {
        // User management
        $routes->resource('users', ['controller' => 'UserController']);

        // Master data
        $routes->resource('faculties', ['controller' => 'FacultyController']);
        $routes->resource('study-programs', ['controller' => 'StudyProgramController']);

        // Course management
        $routes->resource('courses', ['controller' => 'CourseController']);
        $routes->get('courses/(:num)/references', 'CourseController::getReferences/$1');
        $routes->post('courses/(:num)/references', 'CourseController::addReference/$1');
        $routes->get('courses/(:num)/topics', 'CourseController::getTopics/$1');
        $routes->post('courses/(:num)/topics', 'CourseController::addTopic/$1');

        // CPMK management
        $routes->resource('cpmk', ['controller' => 'CpmkController']);
        $routes->get('cpmk/(:num)/sub-cpmk', 'CpmkController::getSubCpmk/$1');
        $routes->post('cpmk/(:num)/sub-cpmk', 'CpmkController::addSubCpmk/$1');
        $routes->get('cpmk/(:num)/cpl-relations', 'CpmkController::getCplRelations/$1');
        $routes->post('cpmk/(:num)/cpl-relations', 'CpmkController::addCplRelation/$1');

        // CPL management
        $routes->resource('cpl', ['controller' => 'CplController']);

        // Assessment management
        $routes->resource('assessments/methods', ['controller' => 'AssessmentMethodController']);
        $routes->resource('assessments/plans', ['controller' => 'AssessmentPlanController']);

        // Reports and dashboard
        $routes->get('reports/cpmk-achievement', 'ReportController::cpmkAchievement');
        $routes->get('reports/cpl-mapping', 'ReportController::cplMapping');
        $routes->get('reports/dashboard', 'ReportController::dashboard');
        $routes->get('dashboard/stats', 'DashboardController::getStats');
    });
});

// Catch-all route for SPA (Single Page Application)
$routes->get('(:any)', 'Frontend::index');
