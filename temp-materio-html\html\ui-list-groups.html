<!doctype html>

<html
  lang="en"
  class="layout-menu-fixed layout-compact"
  data-assets-path="../assets/"
  data-template="vertical-menu-template-free">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <meta name="robots" content="noindex, nofollow" />

    <title>Demo: List groups - UI elements | Materio - Bootstrap Dashboard FREE</title>

    <meta name="description" content="" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../assets/img/favicon/favicon.ico" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&ampdisplay=swap"
      rel="stylesheet" />

    <link rel="stylesheet" href="../assets/vendor/fonts/iconify-icons.css" />

    <!-- Core CSS -->
    <!-- build:css assets/vendor/css/theme.css -->

    <link rel="stylesheet" href="../assets/vendor/libs/node-waves/node-waves.css" />

    <link rel="stylesheet" href="../assets/vendor/css/core.css" />
    <link rel="stylesheet" href="../assets/css/demo.css" />

    <!-- Vendors CSS -->

    <link rel="stylesheet" href="../assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.css" />

    <!-- endbuild -->

    <!-- Page CSS -->

    <!-- Helpers -->
    <script src="../assets/vendor/js/helpers.js"></script>
    <!--! Template customizer & Theme config files MUST be included after core stylesheets and helpers.js in the <head> section -->

    <!--? Config: Mandatory theme config file contain global vars & default theme options, Set your preferred theme option in this file. -->

    <script src="../assets/js/config.js"></script>
  </head>

  <body>
    <!-- Layout wrapper -->
    <div class="layout-wrapper layout-content-navbar">
      <div class="layout-container">
        <!-- Menu -->

        <aside id="layout-menu" class="layout-menu menu-vertical menu bg-menu-theme">
          <div class="app-brand demo">
            <a href="index.html" class="app-brand-link">
              <span class="app-brand-logo demo me-1">
                <span class="text-primary">
                  <svg width="30" height="24" viewBox="0 0 250 196" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M12.3002 1.25469L56.655 28.6432C59.0349 30.1128 60.4839 32.711 60.4839 35.5089V160.63C60.4839 163.468 58.9941 166.097 56.5603 167.553L12.2055 194.107C8.3836 196.395 3.43136 195.15 1.14435 191.327C0.395485 190.075 0 188.643 0 187.184V8.12039C0 3.66447 3.61061 0.0522461 8.06452 0.0522461C9.56056 0.0522461 11.0271 0.468577 12.3002 1.25469Z"
                      fill="currentColor" />
                    <path
                      opacity="0.077704"
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M0 65.2656L60.4839 99.9629V133.979L0 65.2656Z"
                      fill="black" />
                    <path
                      opacity="0.077704"
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M0 65.2656L60.4839 99.0795V119.859L0 65.2656Z"
                      fill="black" />
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M237.71 1.22393L193.355 28.5207C190.97 29.9889 189.516 32.5905 189.516 35.3927V160.631C189.516 163.469 191.006 166.098 193.44 167.555L237.794 194.108C241.616 196.396 246.569 195.151 248.856 191.328C249.605 190.076 250 188.644 250 187.185V8.09597C250 3.64006 246.389 0.027832 241.935 0.027832C240.444 0.027832 238.981 0.441882 237.71 1.22393Z"
                      fill="currentColor" />
                    <path
                      opacity="0.077704"
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M250 65.2656L189.516 99.8897V135.006L250 65.2656Z"
                      fill="black" />
                    <path
                      opacity="0.077704"
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M250 65.2656L189.516 99.0497V120.886L250 65.2656Z"
                      fill="black" />
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M12.2787 1.18923L125 70.3075V136.87L0 65.2465V8.06814C0 3.61223 3.61061 0 8.06452 0C9.552 0 11.0105 0.411583 12.2787 1.18923Z"
                      fill="currentColor" />
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M12.2787 1.18923L125 70.3075V136.87L0 65.2465V8.06814C0 3.61223 3.61061 0 8.06452 0C9.552 0 11.0105 0.411583 12.2787 1.18923Z"
                      fill="white"
                      fill-opacity="0.15" />
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M237.721 1.18923L125 70.3075V136.87L250 65.2465V8.06814C250 3.61223 246.389 0 241.935 0C240.448 0 238.99 0.411583 237.721 1.18923Z"
                      fill="currentColor" />
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M237.721 1.18923L125 70.3075V136.87L250 65.2465V8.06814C250 3.61223 246.389 0 241.935 0C240.448 0 238.99 0.411583 237.721 1.18923Z"
                      fill="white"
                      fill-opacity="0.3" />
                  </svg>
                </span>
              </span>
              <span class="app-brand-text demo menu-text fw-semibold ms-2">Materio</span>
            </a>

            <a href="javascript:void(0);" class="layout-menu-toggle menu-link text-large ms-auto">
              <i class="menu-toggle-icon d-xl-inline-block align-middle"></i>
            </a>
          </div>

          <div class="menu-inner-shadow"></div>

          <ul class="menu-inner py-1">
            <!-- Dashboards -->
            <li class="menu-item">
              <a href="javascript:void(0);" class="menu-link menu-toggle">
                <i class="menu-icon icon-base ri ri-home-smile-line"></i>
                <div data-i18n="Dashboards">Dashboards</div>
                <div class="badge text-bg-danger rounded-pill ms-auto">5</div>
              </a>
              <ul class="menu-sub">
                <li class="menu-item">
                  <a
                    href="https://demos.themeselection.com/materio-bootstrap-html-admin-template/html/vertical-menu-template/dashboards-crm.html"
                    target="_blank"
                    class="menu-link">
                    <div data-i18n="CRM">CRM</div>
                    <div class="badge rounded-pill bg-label-primary fs-tiny ms-auto">Pro</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="index.html" class="menu-link">
                    <div data-i18n="Analytics">Analytics</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a
                    href="https://demos.themeselection.com/materio-bootstrap-html-admin-template/html/vertical-menu-template/app-ecommerce-dashboard.html"
                    target="_blank"
                    class="menu-link">
                    <div data-i18n="eCommerce">eCommerce</div>
                    <div class="badge rounded-pill bg-label-primary fs-tiny ms-auto">Pro</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a
                    href="https://demos.themeselection.com/materio-bootstrap-html-admin-template/html/vertical-menu-template/app-logistics-dashboard.html"
                    target="_blank"
                    class="menu-link">
                    <div data-i18n="Logistics">Logistics</div>
                    <div class="badge rounded-pill bg-label-primary fs-tiny ms-auto">Pro</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a
                    href="https://demos.themeselection.com/materio-bootstrap-html-admin-template/html/vertical-menu-template/app-academy-dashboard.html"
                    target="_blank"
                    class="menu-link">
                    <div data-i18n="Academy">Academy</div>
                    <div class="badge rounded-pill bg-label-primary fs-tiny ms-auto">Pro</div>
                  </a>
                </li>
              </ul>
            </li>

            <!-- Layouts -->
            <li class="menu-item">
              <a href="javascript:void(0);" class="menu-link menu-toggle">
                <i class="menu-icon icon-base ri ri-layout-2-line"></i>
                <div data-i18n="Layouts">Layouts</div>
              </a>

              <ul class="menu-sub">
                <li class="menu-item">
                  <a href="layouts-without-menu.html" class="menu-link">
                    <div data-i18n="Without menu">Without menu</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="layouts-without-navbar.html" class="menu-link">
                    <div data-i18n="Without navbar">Without navbar</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="layouts-container.html" class="menu-link">
                    <div data-i18n="Container">Container</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="layouts-fluid.html" class="menu-link">
                    <div data-i18n="Fluid">Fluid</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="layouts-blank.html" class="menu-link">
                    <div data-i18n="Blank">Blank</div>
                  </a>
                </li>
              </ul>
            </li>

            <!-- Front Pages -->
            <li class="menu-item">
              <a href="javascript:void(0);" class="menu-link menu-toggle">
                <i class="menu-icon icon-base ri ri-file-copy-line"></i>
                <div data-i18n="Front Pages">Front Pages</div>
                <div class="badge rounded-pill bg-label-primary fs-tiny ms-auto">Pro</div>
              </a>
              <ul class="menu-sub">
                <li class="menu-item">
                  <a
                    href="https://demos.themeselection.com/materio-bootstrap-html-admin-template/html/front-pages/landing-page.html"
                    class="menu-link"
                    target="_blank">
                    <div data-i18n="Landing">Landing</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a
                    href="https://demos.themeselection.com/materio-bootstrap-html-admin-template/html/front-pages/pricing-page.html"
                    class="menu-link"
                    target="_blank">
                    <div data-i18n="Pricing">Pricing</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a
                    href="https://demos.themeselection.com/materio-bootstrap-html-admin-template/html/front-pages/payment-page.html"
                    class="menu-link"
                    target="_blank">
                    <div data-i18n="Payment">Payment</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a
                    href="https://demos.themeselection.com/materio-bootstrap-html-admin-template/html/front-pages/checkout-page.html"
                    class="menu-link"
                    target="_blank">
                    <div data-i18n="Checkout">Checkout</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a
                    href="https://demos.themeselection.com/materio-bootstrap-html-admin-template/html/front-pages/help-center-landing.html"
                    class="menu-link"
                    target="_blank">
                    <div data-i18n="Help Center">Help Center</div>
                  </a>
                </li>
              </ul>
            </li>

            <!-- Apps & Pages -->
            <li class="menu-header mt-7">
              <span class="menu-header-text">Apps &amp; Pages</span>
            </li>
            <li class="menu-item">
              <a
                href="https://demos.themeselection.com/materio-bootstrap-html-admin-template/html/vertical-menu-template/app-email.html"
                target="_blank"
                class="menu-link">
                <i class="menu-icon icon-base ri ri-mail-open-line"></i>
                <div data-i18n="Email">Email</div>
                <div class="badge rounded-pill bg-label-primary fs-tiny ms-auto">Pro</div>
              </a>
            </li>
            <li class="menu-item">
              <a
                href="https://demos.themeselection.com/materio-bootstrap-html-admin-template/html/vertical-menu-template/app-chat.html"
                target="_blank"
                class="menu-link">
                <i class="menu-icon icon-base ri ri-wechat-line"></i>
                <div data-i18n="Chat">Chat</div>
                <div class="badge rounded-pill bg-label-primary fs-tiny ms-auto">Pro</div>
              </a>
            </li>
            <li class="menu-item">
              <a
                href="https://demos.themeselection.com/materio-bootstrap-html-admin-template/html/vertical-menu-template/app-calendar.html"
                target="_blank"
                class="menu-link">
                <i class="menu-icon icon-base ri ri-calendar-line"></i>
                <div data-i18n="Calendar">Calendar</div>
                <div class="badge rounded-pill bg-label-primary fs-tiny ms-auto">Pro</div>
              </a>
            </li>
            <li class="menu-item">
              <a
                href="https://demos.themeselection.com/materio-bootstrap-html-admin-template/html/vertical-menu-template/app-kanban.html"
                target="_blank"
                class="menu-link">
                <i class="menu-icon icon-base ri ri-drag-drop-line"></i>
                <div data-i18n="Kanban">Kanban</div>
                <div class="badge rounded-pill bg-label-primary fs-tiny ms-auto">Pro</div>
              </a>
            </li>
            <!-- Pages -->
            <li class="menu-item">
              <a href="javascript:void(0);" class="menu-link menu-toggle">
                <i class="menu-icon icon-base ri ri-layout-left-line"></i>
                <div data-i18n="Account Settings">Account Settings</div>
              </a>
              <ul class="menu-sub">
                <li class="menu-item">
                  <a href="pages-account-settings-account.html" class="menu-link">
                    <div data-i18n="Account">Account</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="pages-account-settings-notifications.html" class="menu-link">
                    <div data-i18n="Notifications">Notifications</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="pages-account-settings-connections.html" class="menu-link">
                    <div data-i18n="Connections">Connections</div>
                  </a>
                </li>
              </ul>
            </li>
            <li class="menu-item">
              <a href="javascript:void(0);" class="menu-link menu-toggle">
                <i class="menu-icon icon-base ri ri-shield-keyhole-line"></i>
                <div data-i18n="Authentications">Authentications</div>
              </a>
              <ul class="menu-sub">
                <li class="menu-item">
                  <a href="auth-login-basic.html" class="menu-link" target="_blank">
                    <div data-i18n="Basic">Login</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="auth-register-basic.html" class="menu-link" target="_blank">
                    <div data-i18n="Basic">Register</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="auth-forgot-password-basic.html" class="menu-link" target="_blank">
                    <div data-i18n="Basic">Forgot Password</div>
                  </a>
                </li>
              </ul>
            </li>
            <li class="menu-item">
              <a href="javascript:void(0);" class="menu-link menu-toggle">
                <i class="menu-icon icon-base ri ri-box-3-line"></i>
                <div data-i18n="Misc">Misc</div>
              </a>
              <ul class="menu-sub">
                <li class="menu-item">
                  <a href="pages-misc-error.html" class="menu-link" target="_blank">
                    <div data-i18n="Error">Error</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="pages-misc-under-maintenance.html" class="menu-link" target="_blank">
                    <div data-i18n="Under Maintenance">Under Maintenance</div>
                  </a>
                </li>
              </ul>
            </li>
            <!-- Components -->
            <li class="menu-header mt-7"><span class="menu-header-text">Components</span></li>
            <!-- Cards -->
            <li class="menu-item">
              <a href="cards-basic.html" class="menu-link">
                <i class="menu-icon icon-base ri ri-bank-card-2-line"></i>
                <div data-i18n="Basic">Cards</div>
              </a>
            </li>
            <!-- User interface -->
            <li class="menu-item active open">
              <a href="javascript:void(0)" class="menu-link menu-toggle">
                <i class="menu-icon icon-base ri ri-toggle-line"></i>
                <div data-i18n="User interface">User interface</div>
              </a>
              <ul class="menu-sub">
                <li class="menu-item">
                  <a href="ui-accordion.html" class="menu-link">
                    <div data-i18n="Accordion">Accordion</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-alerts.html" class="menu-link">
                    <div data-i18n="Alerts">Alerts</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-badges.html" class="menu-link">
                    <div data-i18n="Badges">Badges</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-buttons.html" class="menu-link">
                    <div data-i18n="Buttons">Buttons</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-carousel.html" class="menu-link">
                    <div data-i18n="Carousel">Carousel</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-collapse.html" class="menu-link">
                    <div data-i18n="Collapse">Collapse</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-dropdowns.html" class="menu-link">
                    <div data-i18n="Dropdowns">Dropdowns</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-footer.html" class="menu-link">
                    <div data-i18n="Footer">Footer</div>
                  </a>
                </li>
                <li class="menu-item active">
                  <a href="ui-list-groups.html" class="menu-link">
                    <div data-i18n="List Groups">List Groups</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-modals.html" class="menu-link">
                    <div data-i18n="Modals">Modals</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-navbar.html" class="menu-link">
                    <div data-i18n="Navbar">Navbar</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-offcanvas.html" class="menu-link">
                    <div data-i18n="Offcanvas">Offcanvas</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-pagination-breadcrumbs.html" class="menu-link">
                    <div data-i18n="Pagination & Breadcrumbs">Pagination &amp; Breadcrumbs</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-progress.html" class="menu-link">
                    <div data-i18n="Progress">Progress</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-spinners.html" class="menu-link">
                    <div data-i18n="Spinners">Spinners</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-tabs-pills.html" class="menu-link">
                    <div data-i18n="Tabs & Pills">Tabs &amp; Pills</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-toasts.html" class="menu-link">
                    <div data-i18n="Toasts">Toasts</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-tooltips-popovers.html" class="menu-link">
                    <div data-i18n="Tooltips & Popovers">Tooltips &amp; Popovers</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="ui-typography.html" class="menu-link">
                    <div data-i18n="Typography">Typography</div>
                  </a>
                </li>
              </ul>
            </li>

            <!-- Extended components -->
            <li class="menu-item">
              <a href="javascript:void(0)" class="menu-link menu-toggle">
                <i class="menu-icon icon-base ri ri-box-3-line"></i>
                <div data-i18n="Extended UI">Extended UI</div>
              </a>
              <ul class="menu-sub">
                <li class="menu-item">
                  <a href="extended-ui-perfect-scrollbar.html" class="menu-link">
                    <div data-i18n="Perfect Scrollbar">Perfect Scrollbar</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="extended-ui-text-divider.html" class="menu-link">
                    <div data-i18n="Text Divider">Text Divider</div>
                  </a>
                </li>
              </ul>
            </li>

            <!-- Icons -->
            <li class="menu-item">
              <a href="icons-ri.html" class="menu-link">
                <i class="menu-icon icon-base ri ri-remixicon-line"></i>
                <div data-i18n="Icons">Icons</div>
              </a>
            </li>

            <!-- Forms & Tables -->
            <li class="menu-header mt-7"><span class="menu-header-text">Forms &amp; Tables</span></li>
            <!-- Forms -->
            <li class="menu-item">
              <a href="javascript:void(0);" class="menu-link menu-toggle">
                <i class="menu-icon icon-base ri ri-radio-button-line"></i>
                <div data-i18n="Form Elements">Form Elements</div>
              </a>
              <ul class="menu-sub">
                <li class="menu-item">
                  <a href="forms-basic-inputs.html" class="menu-link">
                    <div data-i18n="Basic Inputs">Basic Inputs</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="forms-input-groups.html" class="menu-link">
                    <div data-i18n="Input groups">Input groups</div>
                  </a>
                </li>
              </ul>
            </li>
            <li class="menu-item">
              <a href="javascript:void(0);" class="menu-link menu-toggle">
                <i class="menu-icon icon-base ri ri-box-3-line"></i>
                <div data-i18n="Form Layouts">Form Layouts</div>
              </a>
              <ul class="menu-sub">
                <li class="menu-item">
                  <a href="form-layouts-vertical.html" class="menu-link">
                    <div data-i18n="Vertical Form">Vertical Form</div>
                  </a>
                </li>
                <li class="menu-item">
                  <a href="form-layouts-horizontal.html" class="menu-link">
                    <div data-i18n="Horizontal Form">Horizontal Form</div>
                  </a>
                </li>
              </ul>
            </li>
            <!-- Form Validation -->
            <li class="menu-item">
              <a
                href="https://demos.themeselection.com/materio-bootstrap-html-admin-template/html/vertical-menu-template/form-validation.html"
                target="_blank"
                class="menu-link">
                <i class="menu-icon icon-base ri ri-checkbox-multiple-line"></i>
                <div data-i18n="Form Validation">Form Validation</div>
                <div class="badge rounded-pill bg-label-primary fs-tiny ms-auto">Pro</div>
              </a>
            </li>
            <!-- Tables -->
            <li class="menu-item">
              <a href="tables-basic.html" class="menu-link">
                <i class="menu-icon icon-base ri ri-table-alt-line"></i>
                <div data-i18n="Tables">Tables</div>
              </a>
            </li>
            <!-- Data Tables -->
            <li class="menu-item">
              <a
                href="https://demos.themeselection.com/materio-bootstrap-html-admin-template/html/vertical-menu-template/tables-datatables-basic.html"
                target="_blank"
                class="menu-link">
                <i class="menu-icon icon-base ri ri-grid-line"></i>
                <div data-i18n="Datatables">Datatables</div>
                <div class="badge rounded-pill bg-label-primary fs-tiny ms-auto">Pro</div>
              </a>
            </li>
            <!-- Misc -->
            <li class="menu-header mt-7"><span class="menu-header-text">Misc</span></li>
            <li class="menu-item">
              <a
                href="https://github.com/themeselection/materio-bootstrap-html-admin-template-free/issues"
                target="_blank"
                class="menu-link">
                <i class="menu-icon icon-base ri ri-lifebuoy-line"></i>
                <div data-i18n="Support">Support</div>
              </a>
            </li>
            <li class="menu-item">
              <a
                href="https://demos.themeselection.com/materio-bootstrap-html-admin-template/documentation/"
                target="_blank"
                class="menu-link">
                <i class="menu-icon icon-base ri ri-article-line"></i>
                <div data-i18n="Documentation">Documentation</div>
              </a>
            </li>
          </ul>
        </aside>
        <!-- / Menu -->

        <!-- Layout container -->
        <div class="layout-page">
          <!-- Navbar -->

          <nav
            class="layout-navbar container-xxl navbar-detached navbar navbar-expand-xl align-items-center bg-navbar-theme"
            id="layout-navbar">
            <div class="layout-menu-toggle navbar-nav align-items-xl-center me-4 me-xl-0 d-xl-none">
              <a class="nav-item nav-link px-0 me-xl-6" href="javascript:void(0)">
                <i class="icon-base ri ri-menu-line icon-md"></i>
              </a>
            </div>

            <div class="navbar-nav-right d-flex align-items-center justify-content-end" id="navbar-collapse">
              <!-- Search -->
              <div class="navbar-nav align-items-center">
                <div class="nav-item d-flex align-items-center">
                  <i class="icon-base ri ri-search-line icon-lg lh-0"></i>
                  <input
                    type="text"
                    class="form-control border-0 shadow-none"
                    placeholder="Search..."
                    aria-label="Search..." />
                </div>
              </div>
              <!-- /Search -->

              <ul class="navbar-nav flex-row align-items-center ms-md-auto">
                <!-- Place this tag where you want the button to render. -->
                <li class="nav-item lh-1 me-4">
                  <a
                    class="github-button"
                    href="https://github.com/themeselection/materio-bootstrap-html-admin-template-free"
                    data-icon="octicon-star"
                    data-size="large"
                    data-show-count="true"
                    aria-label="Star themeselection/materio-html-admin-template-free on GitHub"
                    >Star</a
                  >
                </li>

                <!-- User -->
                <li class="nav-item navbar-dropdown dropdown-user dropdown">
                  <a
                    class="nav-link dropdown-toggle hide-arrow p-0"
                    href="javascript:void(0);"
                    data-bs-toggle="dropdown">
                    <div class="avatar avatar-online">
                      <img src="../assets/img/avatars/1.png" alt="alt" class="rounded-circle" />
                    </div>
                  </a>
                  <ul class="dropdown-menu dropdown-menu-end">
                    <li>
                      <a class="dropdown-item" href="#">
                        <div class="d-flex">
                          <div class="flex-shrink-0 me-3">
                            <div class="avatar avatar-online">
                              <img src="../assets/img/avatars/1.png" alt="alt" class="w-px-40 h-auto rounded-circle" />
                            </div>
                          </div>
                          <div class="flex-grow-1">
                            <h6 class="mb-0">John Doe</h6>
                            <small class="text-body-secondary">Admin</small>
                          </div>
                        </div>
                      </a>
                    </li>
                    <li>
                      <div class="dropdown-divider my-1"></div>
                    </li>
                    <li>
                      <a class="dropdown-item" href="#">
                        <i class="icon-base ri ri-user-line icon-md me-3"></i>
                        <span>My Profile</span>
                      </a>
                    </li>
                    <li>
                      <a class="dropdown-item" href="#">
                        <i class="icon-base ri ri-settings-4-line icon-md me-3"></i>
                        <span>Settings</span>
                      </a>
                    </li>
                    <li>
                      <a class="dropdown-item" href="#">
                        <span class="d-flex align-items-center align-middle">
                          <i class="flex-shrink-0 icon-base ri ri-bank-card-line icon-md me-3"></i>
                          <span class="flex-grow-1 align-middle ms-1">Billing Plan</span>
                          <span class="flex-shrink-0 badge rounded-pill bg-danger">4</span>
                        </span>
                      </a>
                    </li>
                    <li>
                      <div class="dropdown-divider my-1"></div>
                    </li>
                    <li>
                      <div class="d-grid px-4 pt-2 pb-1">
                        <a class="btn btn-danger d-flex" href="javascript:void(0);">
                          <small class="align-middle">Logout</small>
                          <i class="ri ri-logout-box-r-line ms-2 ri-xs"></i>
                        </a>
                      </div>
                    </li>
                  </ul>
                </li>
                <!--/ User -->
              </ul>
            </div>
          </nav>

          <!-- / Navbar -->

          <!-- Content wrapper -->
          <div class="content-wrapper">
            <!-- Content -->
            <div class="container-xxl flex-grow-1 container-p-y">
              <div class="row g-6">
                <div class="col-lg-12">
                  <div class="card">
                    <h5 class="card-header">List groups</h5>
                    <div class="card-body">
                      <div class="row">
                        <!-- Basic List group -->
                        <div class="col-lg-6 mb-6 mb-xl-0">
                          <small class="fw-medium">Basic</small>
                          <div class="demo-inline-spacing mt-4">
                            <div class="list-group">
                              <a href="javascript:void(0);" class="list-group-item list-group-item-action active"
                                >Bear claw cake biscuit</a
                              >
                              <a href="javascript:void(0);" class="list-group-item list-group-item-action"
                                >Soufflé pastry pie ice</a
                              >
                              <a href="javascript:void(0);" class="list-group-item list-group-item-action disabled"
                                >Tart tiramisu cake</a
                              >
                              <a href="javascript:void(0);" class="list-group-item list-group-item-action"
                                >Bonbon toffee muffin</a
                              >
                              <a href="javascript:void(0);" class="list-group-item list-group-item-action"
                                >Dragée tootsie roll</a
                              >
                            </div>
                          </div>
                        </div>
                        <!--/ Basic List group -->
                        <!-- List group with Badges & Pills -->
                        <div class="col-lg-6">
                          <small class="fw-medium">With Bagdes & Pills</small>
                          <div class="demo-inline-spacing mt-4">
                            <ul class="list-group">
                              <li class="list-group-item d-flex justify-content-between align-items-center">
                                Soufflé pastry pie ice
                                <span class="badge bg-primary">5</span>
                              </li>
                              <li class="list-group-item disabled">Bear claw cake biscuit</li>
                              <li class="list-group-item d-flex justify-content-between align-items-center">
                                Tart tiramisu cake
                                <span class="badge bg-success">2</span>
                              </li>
                              <li class="list-group-item d-flex justify-content-between align-items-center">
                                Bonbon toffee muffin
                                <span class="badge bg-danger rounded-pill">3</span>
                              </li>
                              <li class="list-group-item">Dragée tootsie roll</li>
                            </ul>
                          </div>
                        </div>
                        <!--/ List group with Badges & Pills -->
                      </div>
                    </div>
                    <hr class="m-0" />
                    <div class="card-body">
                      <div class="row">
                        <!-- List group Flush (Without main border) -->
                        <div class="col-lg-6 mb-6 mb-xl-0">
                          <small class="fw-medium">Flush</small>
                          <div class="demo-inline-spacing mt-4">
                            <div class="list-group list-group-flush">
                              <a href="javascript:void(0);" class="list-group-item list-group-item-action"
                                >Bear claw cake biscuit</a
                              >
                              <a href="javascript:void(0);" class="list-group-item list-group-item-action"
                                >Soufflé pastry pie ice</a
                              >
                              <a href="javascript:void(0);" class="list-group-item list-group-item-action"
                                >Tart tiramisu cake</a
                              >
                              <a href="javascript:void(0);" class="list-group-item list-group-item-action"
                                >Bonbon toffee muffin</a
                              >
                              <a href="javascript:void(0);" class="list-group-item list-group-item-action"
                                >Dragée tootsie roll</a
                              >
                            </div>
                          </div>
                        </div>
                        <!--/ List group Flush (Without main border) -->
                        <!-- List group Icons -->
                        <div class="col-lg-6">
                          <small class="fw-medium">With Icons</small>
                          <div class="demo-inline-spacing mt-4">
                            <ul class="list-group">
                              <li class="list-group-item d-flex align-items-center">
                                <i class="icon-base ri ri-computer-line icon-md me-3"></i>
                                Soufflé pastry pie ice
                              </li>
                              <li class="list-group-item d-flex align-items-center">
                                <i class="icon-base ri ri-notification-4-line icon-md me-3"></i>
                                Bear claw cake biscuit
                              </li>
                              <li class="list-group-item d-flex align-items-center">
                                <i class="icon-base ri ri-headphone-fill icon-md me-3"></i>
                                Tart tiramisu cake
                              </li>
                              <li class="list-group-item d-flex align-items-center">
                                <i class="icon-base ri ri-price-tag-3-line icon-md me-3"></i>
                                Bonbon toffee muffin
                              </li>
                              <li class="list-group-item d-flex align-items-center">
                                <i class="icon-base ri ri-focus-2-line icon-md me-3"></i>
                                Dragée tootsie roll
                              </li>
                            </ul>
                          </div>
                        </div>
                        <!--/ List group Icons -->
                      </div>
                    </div>
                    <hr class="m-0" />
                    <div class="card-body">
                      <div class="row">
                        <!-- List group Numbered -->
                        <div class="col-lg-6 mb-6 mb-xl-0">
                          <small class="fw-medium">Numbered</small>
                          <div class="demo-inline-spacing mt-4">
                            <ol class="list-group list-group-numbered">
                              <li class="list-group-item">Bear claw cake biscuit</li>
                              <li class="list-group-item">Soufflé pastry pie ice</li>
                              <li class="list-group-item">Tart tiramisu cake</li>
                              <li class="list-group-item">Bonbon toffee muffin</li>
                              <li class="list-group-item">Dragée tootsie roll</li>
                            </ol>
                          </div>
                        </div>
                        <!--/ List group Numbered -->
                        <!-- List group checkbox -->
                        <div class="col-lg-6">
                          <small class="fw-medium">List Group With Checkbox</small>
                          <div class="demo-inline-spacing mt-4">
                            <div class="list-group">
                              <label class="list-group-item">
                                <span class="form-check mb-0">
                                  <input class="form-check-input me-4" type="checkbox" value="" />
                                  Soufflé pastry pie ice
                                </span>
                              </label>
                              <label class="list-group-item">
                                <span class="form-check mb-0">
                                  <input class="form-check-input me-4" type="checkbox" value="" />
                                  Bear claw cake biscuit
                                </span>
                              </label>
                              <label class="list-group-item">
                                <span class="form-check mb-0">
                                  <input class="form-check-input me-4" type="checkbox" value="" />
                                  Tart tiramisu cake
                                </span>
                              </label>
                              <label class="list-group-item">
                                <span class="form-check mb-0">
                                  <input class="form-check-input me-4" type="checkbox" value="" />
                                  Bonbon toffee muffin
                                </span>
                              </label>
                              <label class="list-group-item">
                                <span class="form-check mb-0">
                                  <input class="form-check-input me-4" type="checkbox" value="" />
                                  Dragée tootsie roll
                                </span>
                              </label>
                            </div>
                          </div>
                        </div>
                        <!--/ List group checkbox -->
                      </div>
                    </div>
                    <hr class="m-0" />
                    <div class="card-body">
                      <div class="row">
                        <!-- Contextual List group -->
                        <div class="col-lg-6 mb-6 mb-xl-0">
                          <small class="fw-medium">Contextual classes</small>
                          <div class="demo-inline-spacing mt-4">
                            <ul class="list-group">
                              <li class="list-group-item list-group-item-action list-group-item-primary">
                                Primary list group item
                              </li>
                              <li class="list-group-item list-group-item-action list-group-item-secondary">
                                Secondary list group item
                              </li>
                              <li class="list-group-item list-group-item-action list-group-item-success">
                                Success list group item
                              </li>
                              <li class="list-group-item list-group-item-action list-group-item-danger">
                                Danger list group item
                              </li>
                              <li class="list-group-item list-group-item-action list-group-item-warning">
                                Warning list group item
                              </li>
                              <li class="list-group-item list-group-item-action list-group-item-info">
                                Info list group item
                              </li>
                              <li class="list-group-item list-group-item-action list-group-item-dark">
                                Dark list group item
                              </li>
                            </ul>
                          </div>
                        </div>
                        <!--/ Contextual List group -->
                        <!-- Custom content with heading -->
                        <div class="col-lg-6">
                          <small class="fw-medium">Custom content</small>
                          <div class="demo-inline-spacing mt-4">
                            <div class="list-group">
                              <a
                                href="javascript:void(0);"
                                class="list-group-item list-group-item-action flex-column align-items-start active">
                                <div class="d-flex justify-content-between w-100">
                                  <h4 class="mb-1">List group item heading</h4>
                                  <small>3 days ago</small>
                                </div>
                                <p class="mb-1">
                                  Donec id elit non mi porta gravida at eget metus. Maecenas sed diam eget risus varius
                                  blandit.
                                </p>
                                <small>Donec id elit non mi porta.</small>
                              </a>
                              <a
                                href="javascript:void(0);"
                                class="list-group-item list-group-item-action flex-column align-items-start">
                                <div class="d-flex justify-content-between w-100">
                                  <h4 class="mb-1">List group item heading</h4>
                                  <small class="text-body-secondary">3 days ago</small>
                                </div>
                                <p class="mb-1">
                                  Donec id elit non mi porta gravida at eget metus. Maecenas sed diam eget risus varius
                                  blandit.
                                </p>
                                <small class="text-body-secondary">Donec id elit non mi porta.</small>
                              </a>
                              <a
                                href="javascript:void(0);"
                                class="list-group-item list-group-item-action flex-column align-items-start">
                                <div class="d-flex justify-content-between w-100">
                                  <h4 class="mb-1">List group item heading</h4>
                                  <small class="text-body-secondary">3 days ago</small>
                                </div>
                                <p class="mb-1">
                                  Donec id elit non mi porta gravida at eget metus. Maecenas sed diam eget risus varius
                                  blandit.
                                </p>
                                <small class="text-body-secondary">Donec id elit non mi porta.</small>
                              </a>
                            </div>
                          </div>
                        </div>
                        <!--/ Custom content with heading -->
                      </div>
                    </div>
                  </div>
                </div>

                <div class="col-lg-12">
                  <div class="card">
                    <h5 class="card-header">Javascript behavior</h5>
                    <div class="card-body">
                      <div class="row">
                        <!-- Custom content with heading -->
                        <div class="col-lg-6 mb-6 mb-xl-0">
                          <small class="fw-medium">Vertical</small>
                          <div class="mt-4">
                            <div class="row">
                              <div class="col-md-4 col-12 mb-4 mb-md-0">
                                <div class="list-group">
                                  <a
                                    class="list-group-item list-group-item-action active"
                                    id="list-home-list"
                                    data-bs-toggle="list"
                                    href="#list-home"
                                    >Home</a
                                  >
                                  <a
                                    class="list-group-item list-group-item-action"
                                    id="list-profile-list"
                                    data-bs-toggle="list"
                                    href="#list-profile"
                                    >Profile</a
                                  >
                                  <a
                                    class="list-group-item list-group-item-action"
                                    id="list-messages-list"
                                    data-bs-toggle="list"
                                    href="#list-messages"
                                    >Messages</a
                                  >
                                  <a
                                    class="list-group-item list-group-item-action"
                                    id="list-settings-list"
                                    data-bs-toggle="list"
                                    href="#list-settings"
                                    >Settings</a
                                  >
                                </div>
                              </div>
                              <div class="col-md-8 col-12">
                                <div class="tab-content p-0">
                                  <div class="tab-pane fade show active" id="list-home">
                                    Donut sugar plum sweet roll biscuit. Cake oat cake gummi bears. Tart wafer wafer
                                    halvah gummi bears cheesecake. Topping croissant cake sweet roll. Dessert fruitcake
                                    gingerbread halvah marshmallow pudding bear claw cheesecake. Bonbon dragée cookie
                                    gummies. Pudding marzipan liquorice. Sugar plum dragée cupcake cupcake cake dessert
                                    chocolate bar. Pastry lollipop lemon drops lollipop halvah croissant. Pastry sweet
                                    gingerbread lemon drops topping ice cream.
                                  </div>
                                  <div class="tab-pane fade" id="list-profile">
                                    Muffin lemon drops chocolate chupa chups jelly beans dessert jelly-o. Soufflé
                                    gummies gummies. Ice cream powder marshmallow cotton candy oat cake wafer.
                                    Marshmallow gingerbread tootsie roll. Chocolate cake bonbon jelly beans lollipop
                                    jelly beans halvah marzipan danish pie. Oat cake chocolate cake pudding bear claw
                                    liquorice gingerbread icing sugar plum brownie. Toffee cookie apple pie cheesecake
                                    bear claw sugar plum wafer gummi bears fruitcake.
                                  </div>
                                  <div class="tab-pane fade" id="list-messages">
                                    Ice cream dessert candy sugar plum croissant cupcake tart pie apple pie. Pastry
                                    chocolate chupa chups tiramisu. Tiramisu cookie oat cake. Pudding brownie bonbon.
                                    Pie carrot cake chocolate macaroon. Halvah jelly jelly beans cake macaroon jelly-o.
                                    Danish pastry dessert gingerbread powder halvah. Muffin bonbon fruitcake dragée
                                    sweet sesame snaps oat cake marshmallow cheesecake. Cupcake donut sweet bonbon
                                    cheesecake soufflé chocolate bar.
                                  </div>
                                  <div class="tab-pane fade" id="list-settings">
                                    Marzipan cake oat cake. Marshmallow pie chocolate. Liquorice oat cake donut halvah
                                    jelly-o. Jelly-o muffin macaroon cake gingerbread candy cupcake. Cake lollipop
                                    lollipop jelly brownie cake topping chocolate. Pie oat cake jelly. Lemon drops
                                    halvah jelly cookie bonbon cake cupcake ice cream. Donut tart bonbon sweet roll
                                    soufflé gummies biscuit. Wafer toffee topping jelly beans icing pie apple pie toffee
                                    pudding. Tiramisu powder macaroon tiramisu cake halvah.
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="col-lg-6">
                          <small class="fw-medium">Horizontal</small>
                          <div class="mt-4">
                            <div class="list-group list-group-horizontal-md text-md-center">
                              <a
                                class="list-group-item list-group-item-action active"
                                id="home-list-item"
                                data-bs-toggle="list"
                                href="#horizontal-home"
                                >Home</a
                              >
                              <a
                                class="list-group-item list-group-item-action"
                                id="profile-list-item"
                                data-bs-toggle="list"
                                href="#horizontal-profile"
                                >Profile</a
                              >
                              <a
                                class="list-group-item list-group-item-action"
                                id="messages-list-item"
                                data-bs-toggle="list"
                                href="#horizontal-messages"
                                >Messages</a
                              >
                              <a
                                class="list-group-item list-group-item-action"
                                id="settings-list-item"
                                data-bs-toggle="list"
                                href="#horizontal-settings"
                                >Settings</a
                              >
                            </div>
                            <div class="tab-content px-0 mt-0">
                              <div class="tab-pane fade show active" id="horizontal-home">
                                Donut sugar plum sweet roll biscuit. Cake oat cake gummi bears. Tart wafer wafer halvah
                                gummi bears cheesecake. Topping croissant cake sweet roll. Dessert fruitcake gingerbread
                                halvah marshmallow pudding bear claw cheesecake. Bonbon dragée cookie gummies. Pudding
                                marzipan liquorice. Sugar plum dragée cupcake cupcake cake dessert chocolate bar. Pastry
                                lollipop lemon drops lollipop halvah croissant. Pastry sweet gingerbread lemon drops
                                topping ice cream.
                              </div>
                              <div class="tab-pane fade" id="horizontal-profile">
                                Muffin lemon drops chocolate chupa chups jelly beans dessert jelly-o. Soufflé gummies
                                gummies. Ice cream powder marshmallow cotton candy oat cake wafer. Marshmallow
                                gingerbread tootsie roll. Chocolate cake bonbon jelly beans lollipop jelly beans halvah
                                marzipan danish pie. Oat cake chocolate cake pudding bear claw liquorice gingerbread
                                icing sugar plum brownie. Toffee cookie apple pie cheesecake bear claw sugar plum wafer
                                gummi bears fruitcake.
                              </div>
                              <div class="tab-pane fade" id="horizontal-messages">
                                Ice cream dessert candy sugar plum croissant cupcake tart pie apple pie. Pastry
                                chocolate chupa chups tiramisu. Tiramisu cookie oat cake. Pudding brownie bonbon. Pie
                                carrot cake chocolate macaroon. Halvah jelly jelly beans cake macaroon jelly-o. Danish
                                pastry dessert gingerbread powder halvah. Muffin bonbon fruitcake dragée sweet sesame
                                snaps oat cake marshmallow cheesecake. Cupcake donut sweet bonbon cheesecake soufflé
                                chocolate bar.
                              </div>
                              <div class="tab-pane fade" id="horizontal-settings">
                                Marzipan cake oat cake. Marshmallow pie chocolate. Liquorice oat cake donut halvah
                                jelly-o. Jelly-o muffin macaroon cake gingerbread candy cupcake. Cake lollipop lollipop
                                jelly brownie cake topping chocolate. Pie oat cake jelly. Lemon drops halvah jelly
                                cookie bonbon cake cupcake ice cream. Donut tart bonbon sweet roll soufflé gummies
                                biscuit. Wafer toffee topping jelly beans icing pie apple pie toffee pudding. Tiramisu
                                powder macaroon tiramisu cake halvah.
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- / Content -->

            <!-- Footer -->
            <footer class="content-footer footer bg-footer-theme">
              <div class="container-xxl">
                <div
                  class="footer-container d-flex align-items-center justify-content-between py-4 flex-md-row flex-column">
                  <div class="mb-2 mb-md-0">
                    &#169;
                    <script>
                      document.write(new Date().getFullYear());
                    </script>
                    , made with ❤️ by
                    <a href="https://themeselection.com" target="_blank" class="footer-link fw-medium"
                      >ThemeSelection</a
                    >
                  </div>
                  <div class="d-none d-lg-inline-block">
                    <a
                      href="https://themeselection.com/item/category/admin-templates/"
                      target="_blank"
                      class="footer-link me-4"
                      >Admin Templates</a
                    >

                    <a href="https://themeselection.com/license/" class="footer-link me-4" target="_blank">License</a>

                    <a
                      href="https://themeselection.com/item/category/bootstrap-templates/"
                      target="_blank"
                      class="footer-link me-4"
                      >Bootstrap Templates</a
                    >
                    <a
                      href="https://demos.themeselection.com/materio-bootstrap-html-admin-template/documentation/"
                      target="_blank"
                      class="footer-link me-4"
                      >Documentation</a
                    >

                    <a
                      href="https://github.com/themeselection/materio-bootstrap-html-admin-template-free/issues"
                      target="_blank"
                      class="footer-link"
                      >Support</a
                    >
                  </div>
                </div>
              </div>
            </footer>
            <!-- / Footer -->

            <div class="content-backdrop fade"></div>
          </div>
          <!-- Content wrapper -->
        </div>
        <!-- / Layout page -->
      </div>

      <!-- Overlay -->
      <div class="layout-overlay layout-menu-toggle"></div>
    </div>
    <!-- / Layout wrapper -->

    <div class="buy-now">
      <a
        href="https://themeselection.com/item/materio-dashboard-pro-bootstrap/"
        target="_blank"
        class="btn btn-danger btn-buy-now"
        >Upgrade to Pro</a
      >
    </div>

    <!-- Core JS -->

    <script src="../assets/vendor/libs/jquery/jquery.js"></script>

    <script src="../assets/vendor/libs/popper/popper.js"></script>
    <script src="../assets/vendor/js/bootstrap.js"></script>
    <script src="../assets/vendor/libs/node-waves/node-waves.js"></script>

    <script src="../assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.js"></script>

    <script src="../assets/vendor/js/menu.js"></script>

    <!-- endbuild -->

    <!-- Vendors JS -->

    <!-- Main JS -->

    <script src="../assets/js/main.js"></script>

    <!-- Page JS -->

    <!-- Place this tag before closing body tag for github widget button. -->
    <script async="async" defer="defer" src="https://buttons.github.io/buttons.js"></script>
  </body>
</html>
