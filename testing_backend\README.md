# 🧪 Backend Testing Framework

## Overview
This directory contains comprehensive step-by-step testing for the RPS Management System backend. Each test is designed to show visual progression and detailed logging.

## Testing Structure

```
testing_backend/
├── README.md                    # This file
├── run_all_tests.bat           # Main test runner
├── config/
│   ├── test_config.php         # Test configuration
│   └── test_database.php       # Test database setup
├── tests/
│   ├── 01_environment/         # Environment & setup tests
│   ├── 02_database/           # Database connectivity tests
│   ├── 03_authentication/     # Auth system tests
│   ├── 04_api_endpoints/      # API endpoint tests
│   ├── 05_models/            # Model functionality tests
│   ├── 06_integration/       # Integration tests
│   └── 07_performance/       # Performance tests
├── utils/
│   ├── TestLogger.php         # Test logging utility
│   ├── TestRunner.php         # Test execution utility
│   └── ProgressTracker.php    # Visual progress tracking
└── reports/
    ├── html/                  # HTML test reports
    ├── json/                  # JSON test results
    └── logs/                  # Detailed test logs
```

## Quick Start

### 1. Run All Tests
```batch
cd testing_backend
run_all_tests.bat
```

### 2. Run Specific Test Category
```batch
cd testing_backend
php tests/01_environment/run_environment_tests.php
```

### 3. View Test Results
- **HTML Report**: `reports/html/index.html`
- **JSON Results**: `reports/json/test_results.json`
- **Detailed Logs**: `reports/logs/test_log.txt`

## Test Categories

### 🔧 01. Environment Tests
- PHP version and extensions
- CodeIgniter framework
- Database connectivity
- File permissions
- Configuration validation

### 🗄️ 02. Database Tests
- Connection establishment
- Migration status
- Seeder execution
- Table structure validation
- Foreign key constraints

### 🔐 03. Authentication Tests
- User registration
- Login/logout functionality
- JWT token generation
- Password hashing
- Role-based access

### 🌐 04. API Endpoint Tests
- Route accessibility
- Request/response validation
- HTTP status codes
- JSON structure validation
- Error handling

### 📊 05. Model Tests
- CRUD operations
- Data validation
- Relationships
- Business logic
- Data integrity

### 🔗 06. Integration Tests
- Frontend-backend communication
- API workflow testing
- Cross-module functionality
- Data flow validation

### ⚡ 07. Performance Tests
- Response time measurement
- Database query optimization
- Memory usage tracking
- Concurrent request handling

## Test Execution Flow

1. **Environment Check** → Verify system requirements
2. **Database Setup** → Ensure database connectivity
3. **Authentication** → Test user management
4. **API Endpoints** → Validate all routes
5. **Model Testing** → Test data operations
6. **Integration** → Test system workflows
7. **Performance** → Measure system performance

## Visual Progress Tracking

Each test provides:
- ✅ **Success indicators**
- ❌ **Failure notifications**
- 📊 **Progress bars**
- 📝 **Detailed logs**
- 🕒 **Execution timing**

## Test Results

Results are saved in multiple formats:
- **Console Output**: Real-time progress
- **HTML Report**: Visual test results
- **JSON Data**: Machine-readable results
- **Log Files**: Detailed execution logs

## Configuration

Edit `config/test_config.php` to customize:
- Test database settings
- API endpoints to test
- Performance thresholds
- Logging preferences

## Requirements

- PHP 8.1+
- CodeIgniter 4
- PostgreSQL
- Composer dependencies
- PHPUnit (optional)

## Usage Examples

### Run Environment Tests Only
```batch
php tests/01_environment/run_environment_tests.php
```

### Run API Tests with Verbose Output
```batch
php tests/04_api_endpoints/run_api_tests.php --verbose
```

### Generate HTML Report
```batch
php utils/generate_report.php --format=html
```

## Troubleshooting

### Common Issues
1. **Database Connection Failed**
   - Check database credentials in `.env`
   - Ensure PostgreSQL is running
   - Verify database exists

2. **Permission Denied**
   - Check file permissions on `writable/` directory
   - Ensure web server has write access

3. **API Tests Failing**
   - Verify backend server is running
   - Check API routes configuration
   - Validate authentication tokens

### Getting Help
- Check `testing_log.md` for detailed logs
- Review individual test files for specific issues
- Ensure all dependencies are installed

## Contributing

When adding new tests:
1. Follow the existing directory structure
2. Include progress tracking
3. Add comprehensive logging
4. Update this README
5. Test on clean environment

---

**Built for RPS Management System Backend Testing**
