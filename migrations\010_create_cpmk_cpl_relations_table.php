<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateCpmkCplRelationsTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'cpmk_id' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
                'null'       => false,
            ],
            'cpl_id' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
                'null'       => false,
            ],
            'contribution_level' => [
                'type'       => 'ENUM',
                'constraint' => ['rendah', 'sedang', 'tinggi'],
                'null'       => false,
            ],
            'weight_percentage' => [
                'type'       => 'DECIMAL',
                'constraint' => '5,2',
                'null'       => false,
            ],
            'created_by' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
                'null'       => true,
            ],
            'created_at' => [
                'type'    => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
            ],
            'updated_at' => [
                'type'    => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
                'on_update' => 'CURRENT_TIMESTAMP',
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addUniqueKey(['cpmk_id', 'cpl_id']);
        $this->forge->addKey('cpmk_id');
        $this->forge->addKey('cpl_id');
        $this->forge->addKey('contribution_level');
        $this->forge->addKey('created_by');
        
        $this->forge->addForeignKey('cpmk_id', 'cpmk', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('cpl_id', 'cpl', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('created_by', 'users', 'id', 'SET NULL', 'CASCADE');
        
        $this->forge->createTable('cpmk_cpl_relations');
    }

    public function down()
    {
        $this->forge->dropTable('cpmk_cpl_relations');
    }
}
