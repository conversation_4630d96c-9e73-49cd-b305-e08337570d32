# RPS Management System - Product Backlog

## 📋 Backlog Overview

**Product Owner:** <PERSON><PERSON><PERSON><PERSON>, S.Ko<PERSON>, MT  
**Last Updated:** January 25, 2025  
**Total Story Points:** 340  
**Estimated Velocity:** 30 points per sprint  

## 🎯 Epic Breakdown

### Epic 1: User Management & Authentication (60 points)
**Goal:** Secure user authentication dan role-based access control

### Epic 2: Master Data Management (45 points)
**Goal:** Foundational data management untuk akademik

### Epic 3: Course Management (55 points)
**Goal:** Comprehensive course dan curriculum management

### Epic 4: CPMK & CPL Management (70 points)
**Goal:** Learning outcomes management dan mapping

### Epic 5: Assessment System (65 points)
**Goal:** Assessment planning dan tracking system

### Epic 6: Reporting & Analytics (45 points)
**Goal:** Comprehensive reporting dan data visualization

## 📊 User Stories by Priority

### 🔴 MUST HAVE (Priority 1) - 200 points

#### Epic 1: User Management & Authentication

**US-001: User Login System**
- **As a** system user
- **I want to** login securely with my credentials
- **So that** I can access the system based on my role
- **Story Points:** 8
- **Acceptance Criteria:**
  - [ ] User can login with username/email dan password
  - [ ] JWT token generated upon successful login
  - [ ] Failed login attempts are logged
  - [ ] Session timeout after inactivity
  - [ ] Password strength validation

**US-002: Role-Based Access Control**
- **As a** system administrator
- **I want to** assign granular permissions to users
- **So that** each user only accesses appropriate features
- **Story Points:** 13
- **Acceptance Criteria:**
  - [ ] 6 distinct user roles implemented
  - [ ] Granular permissions per feature
  - [ ] Role inheritance capability
  - [ ] Permission validation on every request
  - [ ] Audit trail for permission changes

**US-003: User Profile Management**
- **As a** user
- **I want to** manage my profile information
- **So that** my data is current dan accurate
- **Story Points:** 5
- **Acceptance Criteria:**
  - [ ] View dan edit personal information
  - [ ] Change password functionality
  - [ ] Profile photo upload
  - [ ] Email verification
  - [ ] Activity history view

**US-004: Admin User Management**
- **As an** admin
- **I want to** manage all system users
- **So that** I can control system access
- **Story Points:** 8
- **Acceptance Criteria:**
  - [ ] Create, read, update, delete users
  - [ ] Bulk user import from CSV/Excel
  - [ ] User status management (active/inactive)
  - [ ] Password reset functionality
  - [ ] User activity monitoring

#### Epic 2: Master Data Management

**US-005: Faculty Management**
- **As a** system admin
- **I want to** manage faculty data
- **So that** organizational structure is maintained
- **Story Points:** 5
- **Acceptance Criteria:**
  - [ ] CRUD operations for faculties
  - [ ] Faculty hierarchy management
  - [ ] Dean assignment
  - [ ] Faculty statistics dashboard
  - [ ] Data validation dan constraints

**US-006: Study Program Management**
- **As a** dekan/wakil dekan
- **I want to** manage study programs
- **So that** academic programs are properly organized
- **Story Points:** 8
- **Acceptance Criteria:**
  - [ ] CRUD operations for study programs
  - [ ] Program head assignment
  - [ ] Accreditation status tracking
  - [ ] Program statistics
  - [ ] Course allocation per program

#### Epic 3: Course Management

**US-007: Course CRUD Operations**
- **As a** kepala prodi
- **I want to** manage course information
- **So that** curriculum is properly maintained
- **Story Points:** 13
- **Acceptance Criteria:**
  - [ ] Create, read, update, delete courses
  - [ ] Course code uniqueness validation
  - [ ] Prerequisite course management
  - [ ] Course status tracking
  - [ ] Bulk course operations

**US-008: Course Reference Management**
- **As a** dosen pengampu
- **I want to** manage course references
- **So that** students have proper learning materials
- **Story Points:** 8
- **Acceptance Criteria:**
  - [ ] Add/edit/delete course references
  - [ ] Reference categorization (utama/pendukung/tambahan)
  - [ ] File attachment support
  - [ ] Reference validation
  - [ ] Export reference list

**US-009: Course Topic Planning**
- **As a** dosen pengampu
- **I want to** plan weekly course topics
- **So that** learning is structured dan progressive
- **Story Points:** 8
- **Acceptance Criteria:**
  - [ ] Weekly topic management
  - [ ] Learning objectives per topic
  - [ ] Time allocation planning
  - [ ] Topic sequencing
  - [ ] Progress tracking

#### Epic 4: CPMK & CPL Management

**US-010: CPL Management**
- **As a** kepala prodi
- **I want to** manage graduate learning outcomes
- **So that** program objectives are clear
- **Story Points:** 13
- **Acceptance Criteria:**
  - [ ] CRUD operations for CPL
  - [ ] CPL categorization (sikap, pengetahuan, keterampilan)
  - [ ] CPL validation rules
  - [ ] Import/export functionality
  - [ ] CPL achievement tracking

**US-011: CPMK Management**
- **As a** dosen pengampu
- **I want to** manage course learning outcomes
- **So that** course objectives align with program goals
- **Story Points:** 13
- **Acceptance Criteria:**
  - [ ] CRUD operations for CPMK
  - [ ] Cognitive level assignment
  - [ ] Weight percentage allocation
  - [ ] CPMK validation
  - [ ] Achievement calculation

**US-012: CPMK-CPL Mapping**
- **As a** kepala prodi
- **I want to** map CPMK to CPL
- **So that** course outcomes contribute to program outcomes
- **Story Points:** 13
- **Acceptance Criteria:**
  - [ ] Visual mapping interface
  - [ ] Contribution level assignment
  - [ ] Weight distribution validation
  - [ ] Mapping reports
  - [ ] Impact analysis

#### Epic 5: Assessment System

**US-013: Assessment Method Management**
- **As a** dosen pengampu
- **I want to** define assessment methods
- **So that** evaluation is comprehensive dan fair
- **Story Points:** 8
- **Acceptance Criteria:**
  - [ ] Assessment method library
  - [ ] Method categorization
  - [ ] Custom method creation
  - [ ] Method templates
  - [ ] Usage analytics

**US-014: Assessment Planning**
- **As a** dosen pengampu
- **I want to** plan assessments for each CPMK
- **So that** learning outcomes are properly evaluated
- **Story Points:** 13
- **Acceptance Criteria:**
  - [ ] Assessment plan creation
  - [ ] CPMK-assessment mapping
  - [ ] Weight allocation
  - [ ] Schedule planning
  - [ ] Rubric development

### 🟡 SHOULD HAVE (Priority 2) - 90 points

**US-015: Sub-CPMK Management**
- **As a** dosen pengampu
- **I want to** break down CPMK into sub-components
- **So that** assessment is more granular
- **Story Points:** 8

**US-016: Assessment Result Entry**
- **As a** dosen pengampu
- **I want to** enter assessment results
- **So that** student progress is tracked
- **Story Points:** 13

**US-017: CPMK Achievement Calculation**
- **As a** system
- **I want to** automatically calculate CPMK achievement
- **So that** progress is accurately measured
- **Story Points:** 13

**US-018: Basic Reporting Dashboard**
- **As a** kepala prodi
- **I want to** view basic reports
- **So that** I can monitor program performance
- **Story Points:** 13

**US-019: Data Export Functionality**
- **As a** user
- **I want to** export data to Excel/PDF
- **So that** I can use data externally
- **Story Points:** 8

**US-020: Course Class Management**
- **As a** sekretaris prodi
- **I want to** manage course class instances
- **So that** course delivery is organized
- **Story Points:** 8

**US-021: Student Enrollment Tracking**
- **As a** sekretaris prodi
- **I want to** track student enrollments
- **So that** class capacity is managed
- **Story Points:** 8

**US-022: Academic Year Management**
- **As a** admin
- **I want to** manage academic years
- **So that** data is organized by period
- **Story Points:** 5

**US-023: Notification System**
- **As a** user
- **I want to** receive system notifications
- **So that** I'm informed of important updates
- **Story Points:** 8

**US-024: File Attachment Management**
- **As a** user
- **I want to** attach files to records
- **So that** supporting documents are available
- **Story Points:** 8

### 🟢 COULD HAVE (Priority 3) - 50 points

**US-025: Advanced Analytics Dashboard**
- **As a** dekan
- **I want to** view advanced analytics
- **So that** strategic decisions are data-driven
- **Story Points:** 13

**US-026: CPL Achievement Tracking**
- **As a** kepala prodi
- **I want to** track CPL achievement across cohorts
- **So that** program effectiveness is measured
- **Story Points:** 13

**US-027: Automated Report Generation**
- **As a** user
- **I want to** schedule automated reports
- **So that** regular reporting is streamlined
- **Story Points:** 8

**US-028: Data Import/Export Tools**
- **As a** admin
- **I want to** bulk import/export data
- **So that** data migration is efficient
- **Story Points:** 8

**US-029: System Audit Trail**
- **As a** admin
- **I want to** view system audit logs
- **So that** system usage is monitored
- **Story Points:** 8

## 📋 Sprint Planning

### Sprint 0 (Setup) - Week 1-2
- Project setup dan environment configuration
- Team onboarding dan tool setup
- Initial architecture dan database design

### Sprint 1 - Week 3-4 (30 points)
- US-001: User Login System (8)
- US-002: Role-Based Access Control (13)
- US-003: User Profile Management (5)
- US-022: Academic Year Management (5)

### Sprint 2 - Week 5-6 (30 points)
- US-004: Admin User Management (8)
- US-005: Faculty Management (5)
- US-006: Study Program Management (8)
- US-020: Course Class Management (8)

### Sprint 3 - Week 7-8 (30 points)
- US-007: Course CRUD Operations (13)
- US-008: Course Reference Management (8)
- US-009: Course Topic Planning (8)

### Sprint 4 - Week 9-10 (30 points)
- US-010: CPL Management (13)
- US-011: CPMK Management (13)
- US-022: Academic Year Management (5)

### Sprint 5 - Week 11-12 (30 points)
- US-012: CPMK-CPL Mapping (13)
- US-013: Assessment Method Management (8)
- US-014: Assessment Planning (13)

### Sprint 6 - Week 13-14 (Buffer/Polish)
- Bug fixes dan performance optimization
- User acceptance testing
- Documentation completion
- Deployment preparation

## 📊 Backlog Metrics

- **Total Stories:** 29
- **Completed:** 0
- **In Progress:** 0
- **Ready for Development:** 29
- **Average Story Points:** 11.7
- **Estimated Completion:** 12 sprints (24 weeks)

---

**Note:** Story points menggunakan Fibonacci sequence (1, 2, 3, 5, 8, 13, 21). Velocity akan disesuaikan berdasarkan team performance di sprint awal.
