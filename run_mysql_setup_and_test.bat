@echo off
echo.
echo ========================================
echo  RPS MySQL Setup and Testing
echo ========================================
echo.

cd /d "%~dp0"

echo [STEP 1] Running MySQL Backend Setup...
echo.
call setup_mysql_complete.bat

if errorlevel 1 (
    echo [ERROR] MySQL setup failed
    pause
    exit /b 1
)

echo.
echo [STEP 2] Waiting for setup to complete...
timeout /t 3 /nobreak >nul

echo.
echo [STEP 3] Running Backend Tests...
echo.
cd testing_backend
call run_all_tests.bat

echo.
echo [STEP 4] Starting Backend Server...
echo.
cd ..\backend
echo [INFO] Starting CodeIgniter development server...
echo [INFO] Backend will be available at: http://localhost:8080
echo [INFO] Press Ctrl+C to stop the server
echo.

start "RPS Backend Server" cmd /k "php spark serve --host=0.0.0.0 --port=8080"

echo.
echo ========================================
echo  Setup and Testing Complete!
echo ========================================
echo.
echo ✅ MySQL Backend: Configured and running
echo ✅ Database: Migrated and seeded
echo ✅ Tests: Executed (check results)
echo ✅ Server: Running on http://localhost:8080
echo.
echo Next steps:
echo 1. Check test results in testing_backend/reports/html/test_report.html
echo 2. Test API endpoints: http://localhost:8080/api/v1
echo 3. Start frontend: cd frontend && npm run dev
echo.
pause
