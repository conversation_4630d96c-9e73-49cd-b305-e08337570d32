/* App Brand
******************************************************************************* */
.app-brand {
  display: flex;
  flex-grow: 0;
  flex-shrink: 0;
  align-items: center;
  line-height: 1;


  .app-brand-text {
    opacity: 1;
    transition: opacity $menu-animation-duration ease-in-out;
  }


  .layout-menu-toggle {
    display: block;
  }

  .app-brand-img{
    display: block;
  }
  .app-brand-img-collapsed{
    display: none;
  }
}
.app-brand-text {
  text-transform: uppercase;
}

// For cover auth pages
.auth-cover-brand {
  position: absolute;
  z-index: 1;
  inset-block-start: 1.8rem;
  inset-inline-start: 3rem;
}

.app-brand-link {
  display: flex;
  align-items: center;
}

/* App brand with vertical menu */

.menu-vertical .app-brand {
  padding-inline: calc($menu-vertical-link-padding-x + .125rem) calc($menu-vertical-link-padding-x - .25rem);
}

/* App brand with horizontal menu */
.menu-horizontal .app-brand,
.menu-horizontal .app-brand + .menu-divider {
  display: none !important;
}

@include media-breakpoint-up($menu-collapsed-layout-breakpoint) {
  // .layout-menu-collapsed:not(.layout-menu-hover, .layout-menu-offcanvas, .layout-menu-fixed-offcanvas) .layout-menu {
  .layout-menu-collapsed:not(.layout-menu-hover) .layout-menu,
  .menu-collapsed:not(:hover) .app-brand {
    .app-brand-logo ~ .app-brand-text{
      opacity: 0;
    }
    .app-brand-img{
      display: none;
    }
    .app-brand-img-collapsed{
      display: block;
    }
  }
}
