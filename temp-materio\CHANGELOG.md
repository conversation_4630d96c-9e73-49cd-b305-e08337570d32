<!-- Available h3 headings: Added, Fixed, Updated, Removed, Deprecated -->

# Changelog

All notable changes to this template will be documented in this file

## v2.3.0 (2025-01-01)

### Updated

- Updated Vue & Vuetify to the latest version
  
## v2.2.2 (2024-07-29)

### Updated

- Updated all the libraries to the latest version

## v2.2.1 (2024-01-05)

### Added

- Added Hire Us file
- Added Documentation File

## v2.2.0 (2024-01-04)

### Updated

- Updated all dependencies and devDependencies to latest

### Added

- Added Remix Icons

### Removed

- Material Design icons removed

## v2.1.0 (2023-05-26)

### Updated

- Updated repo according to pro template
- Use Vuetify's official release instead of BETA

## v2.0.0 (2022-10-15)

### Added

- Vue 3 version added
- Vuetify 3 support added
- TypeScript version added (Vue 3 only)

## v1.0.3 (2021-11-12)

### Updated

- Updated [@vue/composition-api](https://github.com/vuejs/composition-api) package to v1.3.3 to mitigate composition api error

## v1.0.2 (2021-08-18)

### Fixed

- broken links updated in footer

## Updates

- "Basic Cards" page renamed to "Cards"
- `README.md` updated

## v1.0.1 (2021-08-13)

### Fixed

- Fixed missing fonts + branding updated

### Updated

- public path updated in `vue.config.js`
- `README.md` updated

## v1.0.0 (2021-08-13)

### Added

- Initial Release
