<template>
  <div>
    <!-- <PERSON>er -->
    <v-row class="mb-6">
      <v-col>
        <h1 class="text-h4 font-weight-bold text-primary">CPL Management</h1>
        <p class="text-subtitle-1 text-medium-emphasis">
          Manage Graduate Learning Outcomes (Capaian Pembelajaran Lulusan)
        </p>
      </v-col>
    </v-row>

    <!-- Statistics Cards -->
    <v-row class="mb-6">
      <v-col cols="12" sm="6" md="3">
        <v-card color="primary" variant="tonal">
          <v-card-text>
            <div class="d-flex align-center justify-space-between">
              <div>
                <p class="text-subtitle-2 text-medium-emphasis mb-1">Total CPL</p>
                <h2 class="text-h4 font-weight-bold">{{ totalCPL }}</h2>
              </div>
              <v-icon size="48" color="primary">mdi-target</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card color="success" variant="tonal">
          <v-card-text>
            <div class="d-flex align-center justify-space-between">
              <div>
                <p class="text-subtitle-2 text-medium-emphasis mb-1">Sikap</p>
                <h2 class="text-h4 font-weight-bold">{{ attitudeCPL }}</h2>
              </div>
              <v-icon size="48" color="success">mdi-heart</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card color="info" variant="tonal">
          <v-card-text>
            <div class="d-flex align-center justify-space-between">
              <div>
                <p class="text-subtitle-2 text-medium-emphasis mb-1">Pengetahuan</p>
                <h2 class="text-h4 font-weight-bold">{{ knowledgeCPL }}</h2>
              </div>
              <v-icon size="48" color="info">mdi-brain</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card color="warning" variant="tonal">
          <v-card-text>
            <div class="d-flex align-center justify-space-between">
              <div>
                <p class="text-subtitle-2 text-medium-emphasis mb-1">Keterampilan</p>
                <h2 class="text-h4 font-weight-bold">{{ skillsCPL }}</h2>
              </div>
              <v-icon size="48" color="warning">mdi-tools</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Study Program Filter -->
    <v-row class="mb-4">
      <v-col cols="12" md="6">
        <v-select
          v-model="selectedStudyProgram"
          :items="studyProgramOptions"
          label="Select Study Program"
          variant="outlined"
          prepend-inner-icon="mdi-school"
          clearable
          @update:model-value="handleStudyProgramChange"
        />
      </v-col>

      <v-col cols="12" md="6" class="d-flex align-center gap-2">
        <v-btn
          color="secondary"
          variant="outlined"
          @click="showMappingMatrix = true"
          :disabled="!selectedStudyProgram"
        >
          <v-icon start>mdi-matrix</v-icon>
          View Mapping Matrix
        </v-btn>

        <v-btn
          color="info"
          variant="outlined"
          @click="showAchievementReport = true"
          :disabled="!selectedStudyProgram"
        >
          <v-icon start>mdi-chart-line</v-icon>
          Achievement Report
        </v-btn>
      </v-col>
    </v-row>

    <!-- Data Table -->
    <DataTable
      title="Graduate Learning Outcomes (CPL)"
      icon="mdi-target"
      item-name="CPL"
      :headers="tableHeaders"
      :items="cpls"
      :loading="loading"
      :total-items="totalItems"
      :filters="tableFilters"
      @add="openCreateModal"
      @edit="openEditModal"
      @delete="openDeleteDialog"
      @view="openViewModal"
      @refresh="loadCPLs"
      @search="handleSearch"
      @filter="handleFilter"
      @update:options="handleTableOptions"
    >
      <!-- Custom slots for specific columns -->
      <template #item.code="{ item }">
        <v-chip
          color="primary"
          variant="tonal"
          size="small"
          class="font-weight-bold"
        >
          {{ item.code }}
        </v-chip>
      </template>

      <template #item.category="{ item }">
        <v-chip
          :color="getCategoryColor(item.category)"
          variant="tonal"
          size="small"
        >
          <v-icon start size="small">{{ getCategoryIcon(item.category) }}</v-icon>
          {{ formatCategory(item.category) }}
        </v-chip>
      </template>

      <template #item.study_program="{ item }">
        <div v-if="item.study_program_name">
          <div class="font-weight-medium">{{ item.study_program_name }}</div>
          <div class="text-caption text-medium-emphasis">{{ item.study_program_code }}</div>
        </div>
        <span v-else class="text-medium-emphasis">-</span>
      </template>

      <template #item.description="{ item }">
        <div class="text-truncate" style="max-width: 300px;">
          {{ item.description }}
        </div>
      </template>

      <template #item.actions="{ item }">
        <div class="d-flex align-center gap-1">
          <v-btn
            icon
            size="small"
            variant="text"
            @click="openViewModal(item)"
          >
            <v-icon size="small">mdi-eye</v-icon>
            <v-tooltip activator="parent">View Details</v-tooltip>
          </v-btn>

          <v-btn
            icon
            size="small"
            variant="text"
            @click="openEditModal(item)"
          >
            <v-icon size="small">mdi-pencil</v-icon>
            <v-tooltip activator="parent">Edit CPL</v-tooltip>
          </v-btn>

          <v-btn
            icon
            size="small"
            variant="text"
            @click="openMappingModal(item)"
          >
            <v-icon size="small">mdi-vector-link</v-icon>
            <v-tooltip activator="parent">View CPMK Mapping</v-tooltip>
          </v-btn>

          <v-btn
            icon
            size="small"
            variant="text"
            color="error"
            @click="openDeleteDialog(item)"
          >
            <v-icon size="small">mdi-delete</v-icon>
            <v-tooltip activator="parent">Delete CPL</v-tooltip>
          </v-btn>
        </div>
      </template>
    </DataTable>

    <!-- CPL Form Modal -->
    <FormModal
      v-model="showModal"
      :title="modalTitle"
      :icon="modalIcon"
      :mode="modalMode"
      :loading="modalLoading"
      max-width="800"
      @submit="handleSubmit"
      @close="closeModal"
    >
      <v-row>
        <v-col cols="12" md="6">
          <v-text-field
            v-model="formData.code"
            :rules="codeRules"
            label="CPL Code *"
            variant="outlined"
            prepend-inner-icon="mdi-identifier"
            :disabled="modalLoading || modalMode === 'view'"
            placeholder="e.g., CPL-01"
          />
        </v-col>

        <v-col cols="12" md="6">
          <v-select
            v-model="formData.study_program_id"
            :items="studyProgramOptions"
            :rules="studyProgramRules"
            label="Study Program *"
            variant="outlined"
            prepend-inner-icon="mdi-school"
            :disabled="modalLoading || modalMode === 'view'"
          />
        </v-col>

        <v-col cols="12">
          <v-select
            v-model="formData.category"
            :items="categoryOptions"
            :rules="categoryRules"
            label="CPL Category *"
            variant="outlined"
            prepend-inner-icon="mdi-tag"
            :disabled="modalLoading || modalMode === 'view'"
          />
        </v-col>

        <v-col cols="12">
          <v-textarea
            v-model="formData.description"
            :rules="descriptionRules"
            label="Description *"
            variant="outlined"
            prepend-inner-icon="mdi-text"
            rows="3"
            :disabled="modalLoading || modalMode === 'view'"
            hint="Brief description of the learning outcome"
          />
        </v-col>

        <v-col cols="12">
          <v-textarea
            v-model="formData.learning_outcome"
            :rules="learningOutcomeRules"
            label="Learning Outcome Statement *"
            variant="outlined"
            prepend-inner-icon="mdi-target"
            rows="4"
            :disabled="modalLoading || modalMode === 'view'"
            hint="Detailed, measurable learning outcome statement"
          />
        </v-col>

        <v-col cols="12" md="6">
          <v-text-field
            v-model="formData.achievement_target"
            :rules="achievementTargetRules"
            label="Achievement Target"
            type="number"
            variant="outlined"
            prepend-inner-icon="mdi-bullseye"
            suffix="%"
            :disabled="modalLoading || modalMode === 'view'"
            min="0"
            max="100"
            hint="Target achievement percentage"
          />
        </v-col>

        <v-col cols="12" md="6">
          <v-switch
            v-model="formData.is_active"
            label="Active"
            color="primary"
            :disabled="modalLoading || modalMode === 'view'"
          />
        </v-col>

        <v-col cols="12">
          <v-textarea
            v-model="formData.measurement_criteria"
            label="Measurement Criteria"
            variant="outlined"
            prepend-inner-icon="mdi-ruler"
            rows="3"
            :disabled="modalLoading || modalMode === 'view'"
            hint="Criteria for measuring achievement of this CPL"
          />
        </v-col>
      </v-row>
    </FormModal>

    <!-- CPL Mapping Modal -->
    <v-dialog v-model="showMappingModal" max-width="1200" scrollable>
      <v-card>
        <v-card-title class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <v-icon class="mr-2">mdi-vector-link</v-icon>
            <span class="text-h6 font-weight-bold">CPMK Mapping for {{ selectedCPL?.code }}</span>
          </div>
          <v-btn icon variant="text" @click="showMappingModal = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>

        <v-divider />

        <v-card-text>
          <CPLMappingView
            v-if="selectedCPL"
            :cpl="selectedCPL"
            @close="showMappingModal = false"
          />
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- Mapping Matrix Modal -->
    <v-dialog v-model="showMappingMatrix" max-width="1400" scrollable>
      <v-card>
        <v-card-title class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <v-icon class="mr-2">mdi-matrix</v-icon>
            <span class="text-h6 font-weight-bold">CPL-CPMK Mapping Matrix</span>
          </div>
          <v-btn icon variant="text" @click="showMappingMatrix = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>

        <v-divider />

        <v-card-text>
          <CPLMappingMatrix
            v-if="selectedStudyProgram"
            :study-program-id="selectedStudyProgram"
            @close="showMappingMatrix = false"
          />
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- Achievement Report Modal -->
    <v-dialog v-model="showAchievementReport" max-width="1200" scrollable>
      <v-card>
        <v-card-title class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <v-icon class="mr-2">mdi-chart-line</v-icon>
            <span class="text-h6 font-weight-bold">CPL Achievement Report</span>
          </div>
          <v-btn icon variant="text" @click="showAchievementReport = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>

        <v-divider />

        <v-card-text>
          <CPLAchievementReport
            v-if="selectedStudyProgram"
            :study-program-id="selectedStudyProgram"
            @close="showAchievementReport = false"
          />
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- Delete Confirmation Dialog -->
    <v-dialog v-model="showDeleteDialog" max-width="400">
      <v-card>
        <v-card-title class="text-h6">Confirm Delete</v-card-title>
        <v-card-text>
          Are you sure you want to delete CPL "{{ selectedCPL?.code }}"?
          This action cannot be undone and will also remove all CPMK mappings.
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn @click="showDeleteDialog = false">Cancel</v-btn>
          <v-btn
            color="error"
            :loading="deleteLoading"
            @click="confirmDelete"
          >
            Delete
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Success/Error Snackbars -->
    <v-snackbar
      v-model="showSuccess"
      color="success"
      timeout="3000"
    >
      {{ successMessage }}
      <template v-slot:actions>
        <v-btn @click="showSuccess = false">Close</v-btn>
      </template>
    </v-snackbar>

    <v-snackbar
      v-model="showError"
      color="error"
      timeout="5000"
    >
      {{ errorMessage }}
      <template v-slot:actions>
        <v-btn @click="showError = false">Close</v-btn>
      </template>
    </v-snackbar>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import DataTable from '@/components/common/DataTable.vue'
import FormModal from '@/components/common/FormModal.vue'
import CPLMappingView from '@/components/cpl/CPLMappingView.vue'
import CPLMappingMatrix from '@/components/cpl/CPLMappingMatrix.vue'
import CPLAchievementReport from '@/components/cpl/CPLAchievementReport.vue'
import { cplAPI, studyProgramsAPI } from '@/services/api'
import type { CPL, StudyProgram, PaginationParams } from '@/types/auth'

// State
const loading = ref(false)
const modalLoading = ref(false)
const deleteLoading = ref(false)
const showModal = ref(false)
const showMappingModal = ref(false)
const showMappingMatrix = ref(false)
const showAchievementReport = ref(false)
const showDeleteDialog = ref(false)
const showSuccess = ref(false)
const showError = ref(false)
const successMessage = ref('')
const errorMessage = ref('')

const cpls = ref<CPL[]>([])
const studyPrograms = ref<StudyProgram[]>([])
const totalItems = ref(0)
const selectedCPL = ref<CPL | null>(null)
const selectedStudyProgram = ref<number | null>(null)
const modalMode = ref<'create' | 'edit' | 'view'>('create')

// Form data
const formData = reactive({
  code: '',
  study_program_id: null as number | null,
  category: 'knowledge' as 'attitude' | 'knowledge' | 'general_skills' | 'specific_skills',
  description: '',
  learning_outcome: '',
  achievement_target: null as number | null,
  measurement_criteria: '',
  is_active: true
})

// Pagination and filtering
const searchQuery = ref('')
const filters = ref<Record<string, any>>({})
const pagination = ref<PaginationParams>({
  page: 1,
  per_page: 20,
  sort_by: 'created_at',
  sort_order: 'desc'
})

// Statistics
const totalCPL = computed(() => cpls.value.length)
const attitudeCPL = computed(() => cpls.value.filter(c => c.category === 'attitude').length)
const knowledgeCPL = computed(() => cpls.value.filter(c => c.category === 'knowledge').length)
const skillsCPL = computed(() =>
  cpls.value.filter(c => c.category === 'general_skills' || c.category === 'specific_skills').length
)

// Table configuration
const tableHeaders = [
  { key: 'code', title: 'Code', sortable: true, type: 'text' as const },
  { key: 'category', title: 'Category', sortable: true, type: 'text' as const },
  { key: 'description', title: 'Description', sortable: false, type: 'text' as const },
  { key: 'study_program', title: 'Study Program', sortable: false, type: 'text' as const },
  { key: 'is_active', title: 'Status', sortable: true, type: 'boolean' as const },
  { key: 'created_at', title: 'Created', sortable: true, type: 'date' as const }
]

const tableFilters = [
  {
    key: 'category',
    label: 'Category',
    options: [
      { title: 'Sikap (Attitude)', value: 'attitude' },
      { title: 'Pengetahuan (Knowledge)', value: 'knowledge' },
      { title: 'Keterampilan Umum (General Skills)', value: 'general_skills' },
      { title: 'Keterampilan Khusus (Specific Skills)', value: 'specific_skills' }
    ]
  },
  {
    key: 'study_program_id',
    label: 'Study Program',
    options: [] as Array<{ title: string; value: number }>
  },
  {
    key: 'is_active',
    label: 'Status',
    options: [
      { title: 'Active', value: true },
      { title: 'Inactive', value: false }
    ]
  }
]

// Computed options
const modalTitle = computed(() => {
  switch (modalMode.value) {
    case 'create':
      return 'Create New CPL'
    case 'edit':
      return 'Edit CPL'
    case 'view':
      return 'View CPL Details'
    default:
      return 'CPL Form'
  }
})

const modalIcon = computed(() => {
  switch (modalMode.value) {
    case 'create':
      return 'mdi-target-plus'
    case 'edit':
      return 'mdi-target-edit'
    case 'view':
      return 'mdi-target'
    default:
      return 'mdi-target'
  }
})

const studyProgramOptions = computed(() =>
  studyPrograms.value.map(sp => ({
    title: `${sp.name} (${sp.code})`,
    value: sp.id
  }))
)

const categoryOptions = [
  {
    title: 'Sikap (Attitude)',
    value: 'attitude',
    subtitle: 'Professional ethics, responsibility, integrity'
  },
  {
    title: 'Pengetahuan (Knowledge)',
    value: 'knowledge',
    subtitle: 'Theoretical foundation, domain expertise'
  },
  {
    title: 'Keterampilan Umum (General Skills)',
    value: 'general_skills',
    subtitle: 'Communication, teamwork, leadership'
  },
  {
    title: 'Keterampilan Khusus (Specific Skills)',
    value: 'specific_skills',
    subtitle: 'Technical competencies, problem-solving'
  }
]

// Validation rules
const codeRules = [
  (v: string) => !!v || 'CPL code is required',
  (v: string) => v.length >= 3 || 'CPL code must be at least 3 characters',
  (v: string) => /^CPL-\d{2}$/.test(v) || 'CPL code format: CPL-01, CPL-02, etc.'
]

const studyProgramRules = [
  (v: number) => !!v || 'Study program is required'
]

const categoryRules = [
  (v: string) => !!v || 'Category is required'
]

const descriptionRules = [
  (v: string) => !!v || 'Description is required',
  (v: string) => v.length >= 10 || 'Description must be at least 10 characters'
]

const learningOutcomeRules = [
  (v: string) => !!v || 'Learning outcome statement is required',
  (v: string) => v.length >= 20 || 'Learning outcome must be at least 20 characters'
]

const achievementTargetRules = [
  (v: number) => v === null || v === undefined || (v >= 0 && v <= 100) || 'Achievement target must be between 0 and 100'
]

// Methods
const loadCPLs = async () => {
  loading.value = true
  try {
    const params = {
      ...pagination.value,
      search: searchQuery.value,
      ...filters.value
    }

    const response = await cplAPI.getAll(params)
    cpls.value = response.data.data
    totalItems.value = response.data.meta?.total || 0
  } catch (error: any) {
    errorMessage.value = 'Failed to load CPLs'
    showError.value = true
    console.error('Load CPLs error:', error)
  } finally {
    loading.value = false
  }
}

const loadStudyPrograms = async () => {
  try {
    const response = await studyProgramsAPI.getAll({ per_page: 100 })
    studyPrograms.value = response.data.data

    // Update filter options
    tableFilters[1].options = studyPrograms.value.map(sp => ({
      title: sp.name,
      value: sp.id
    }))
  } catch (error) {
    console.error('Load study programs error:', error)
  }
}

const handleStudyProgramChange = () => {
  if (selectedStudyProgram.value) {
    filters.value.study_program_id = selectedStudyProgram.value
    loadCPLs()
  } else {
    delete filters.value.study_program_id
    loadCPLs()
  }
}

const openCreateModal = () => {
  modalMode.value = 'create'
  resetForm()
  showModal.value = true
}

const openEditModal = (cpl: CPL) => {
  modalMode.value = 'edit'
  selectedCPL.value = cpl
  populateForm(cpl)
  showModal.value = true
}

const openViewModal = (cpl: CPL) => {
  modalMode.value = 'view'
  selectedCPL.value = cpl
  populateForm(cpl)
  showModal.value = true
}

const openMappingModal = (cpl: CPL) => {
  selectedCPL.value = cpl
  showMappingModal.value = true
}

const openDeleteDialog = (cpl: CPL) => {
  selectedCPL.value = cpl
  showDeleteDialog.value = true
}

const resetForm = () => {
  Object.assign(formData, {
    code: '',
    study_program_id: selectedStudyProgram.value,
    category: 'knowledge',
    description: '',
    learning_outcome: '',
    achievement_target: null,
    measurement_criteria: '',
    is_active: true
  })
}

const populateForm = (cpl: CPL) => {
  Object.assign(formData, {
    code: cpl.code,
    study_program_id: cpl.study_program_id,
    category: cpl.category,
    description: cpl.description,
    learning_outcome: cpl.learning_outcome,
    achievement_target: cpl.achievement_target,
    measurement_criteria: cpl.measurement_criteria || '',
    is_active: cpl.is_active
  })
}

const handleSubmit = async () => {
  modalLoading.value = true
  try {
    if (modalMode.value === 'create') {
      await cplAPI.create(formData)
      successMessage.value = 'CPL created successfully!'
    } else if (modalMode.value === 'edit' && selectedCPL.value) {
      await cplAPI.update(selectedCPL.value.id, formData)
      successMessage.value = 'CPL updated successfully!'
    }

    showSuccess.value = true
    closeModal()
    await loadCPLs()
  } catch (error: any) {
    errorMessage.value = error.response?.data?.message || 'Operation failed'
    showError.value = true
  } finally {
    modalLoading.value = false
  }
}

const confirmDelete = async () => {
  if (!selectedCPL.value) return

  deleteLoading.value = true
  try {
    await cplAPI.delete(selectedCPL.value.id)
    successMessage.value = 'CPL deleted successfully!'
    showSuccess.value = true
    showDeleteDialog.value = false
    await loadCPLs()
  } catch (error: any) {
    errorMessage.value = error.response?.data?.message || 'Delete failed'
    showError.value = true
  } finally {
    deleteLoading.value = false
  }
}

const closeModal = () => {
  showModal.value = false
  selectedCPL.value = null
  resetForm()
}

const handleSearch = (query: string) => {
  searchQuery.value = query
  pagination.value.page = 1
  loadCPLs()
}

const handleFilter = (filterValues: Record<string, any>) => {
  filters.value = filterValues
  pagination.value.page = 1
  loadCPLs()
}

const handleTableOptions = (options: any) => {
  pagination.value = {
    ...pagination.value,
    page: options.page,
    per_page: options.itemsPerPage,
    sort_by: options.sortBy?.[0]?.key || 'created_at',
    sort_order: options.sortBy?.[0]?.order || 'desc'
  }
  loadCPLs()
}

// Utility functions
const getCategoryColor = (category: string) => {
  const colors: Record<string, string> = {
    attitude: 'success',
    knowledge: 'info',
    general_skills: 'warning',
    specific_skills: 'secondary'
  }
  return colors[category] || 'primary'
}

const getCategoryIcon = (category: string) => {
  const icons: Record<string, string> = {
    attitude: 'mdi-heart',
    knowledge: 'mdi-brain',
    general_skills: 'mdi-account-group',
    specific_skills: 'mdi-tools'
  }
  return icons[category] || 'mdi-target'
}

const formatCategory = (category: string) => {
  const categories: Record<string, string> = {
    attitude: 'Sikap',
    knowledge: 'Pengetahuan',
    general_skills: 'Keterampilan Umum',
    specific_skills: 'Keterampilan Khusus'
  }
  return categories[category] || category
}

// Lifecycle
onMounted(async () => {
  await Promise.all([
    loadStudyPrograms(),
    loadCPLs()
  ])
})
</script>
