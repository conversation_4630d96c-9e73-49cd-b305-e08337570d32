version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:14-alpine
    container_name: rps_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: rps_management
      POSTGRES_USER: rps_user
      POSTGRES_PASSWORD: rps_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/database_design.sql:/docker-entrypoint-initdb.d/01-schema.sql
    networks:
      - rps_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U rps_user -d rps_management"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: rps_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - rps_network
    command: redis-server --appendonly yes --requirepass rps_redis_password
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PHP Backend (CodeIgniter 4)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: rps_backend
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - ./backend:/var/www/html
      - ./backend/writable:/var/www/html/writable
    environment:
      - CI_ENVIRONMENT=development
      - database.default.hostname=postgres
      - database.default.database=rps_management
      - database.default.username=rps_user
      - database.default.password=rps_password
      - cache.redis.host=redis
      - cache.redis.password=rps_redis_password
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - rps_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend (Vue.js 3)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: rps_frontend
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - VITE_API_BASE_URL=http://localhost:8080/api/v1
      - VITE_APP_NAME=RPS Management System
    depends_on:
      - backend
    networks:
      - rps_network
    command: npm run dev -- --host 0.0.0.0

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: rps_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - frontend
    networks:
      - rps_network

  # pgAdmin for Database Management
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: rps_pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - rps_network

  # Redis Commander for Redis Management
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: rps_redis_commander
    restart: unless-stopped
    environment:
      - REDIS_HOSTS=local:redis:6379:0:rps_redis_password
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - rps_network

  # Mailhog for Email Testing
  mailhog:
    image: mailhog/mailhog:latest
    container_name: rps_mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - rps_network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  rps_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
