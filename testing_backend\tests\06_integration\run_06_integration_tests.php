<?php

/**
 * Integration Tests
 * 
 * Tests frontend-backend communication and cross-module functionality
 */

// Ensure we have access to the test runner
if (!isset($testRunner)) {
    die("This test must be run through the TestRunner\n");
}

$testRunner->logSubSection('Integration Tests');

$tests = [
    'frontend_backend_communication' => 'Frontend-Backend Communication',
    'api_workflow' => 'API Workflow Testing',
    'cross_module_functionality' => 'Cross-Module Functionality',
    'data_flow_validation' => 'Data Flow Validation',
    'session_management' => 'Session Management',
    'file_upload_integration' => 'File Upload Integration',
    'error_propagation' => 'Error Propagation'
];

$totalTests = count($tests);
$currentTest = 0;
$passedTests = 0;

foreach ($tests as $testKey => $testName) {
    $currentTest++;
    $testRunner->logProgress($currentTest, $totalTests, "Running {$testName}");
    
    $startTime = microtime(true);
    $testPassed = false;
    $message = '';
    $details = [];

    try {
        $baseUrl = $config['api']['base_url'];
        
        switch ($testKey) {
            case 'frontend_backend_communication':
                // Test if frontend can communicate with backend
                try {
                    // Check if backend serves frontend
                    $frontendUrl = str_replace('/api/v1', '', $baseUrl);
                    $response = $testRunner->makeHttpRequest('GET', $frontendUrl);
                    
                    $backendServesFrontend = $response['status_code'] === 200;
                    
                    // Check if API is accessible from frontend perspective
                    $apiResponse = $testRunner->makeHttpRequest('GET', $baseUrl . '/auth/profile');
                    $apiAccessible = in_array($apiResponse['status_code'], [401, 404]); // Expected without auth
                    
                    $testPassed = $backendServesFrontend && $apiAccessible;
                    $message = $testPassed 
                        ? "Frontend-backend communication is working"
                        : "Frontend-backend communication has issues";
                    $details = [
                        'frontend_url' => $frontendUrl,
                        'backend_serves_frontend' => $backendServesFrontend,
                        'api_accessible' => $apiAccessible,
                        'frontend_response_code' => $response['status_code'],
                        'api_response_code' => $apiResponse['status_code']
                    ];
                    
                } catch (Exception $e) {
                    $testPassed = false;
                    $message = "Frontend-backend communication test failed: " . $e->getMessage();
                    $details = ['error' => $e->getMessage()];
                }
                break;

            case 'api_workflow':
                // Test complete API workflow: login -> access protected resource -> logout
                try {
                    $workflowSteps = [];
                    $allStepsSuccessful = true;
                    
                    // Step 1: Login
                    $loginUrl = $baseUrl . '/auth/login';
                    $testUser = $config['test_data']['users']['admin'];
                    $loginData = [
                        'username' => $testUser['username'],
                        'password' => $testUser['password']
                    ];
                    
                    $loginResponse = $testRunner->makeHttpRequest('POST', $loginUrl, $loginData);
                    $loginSuccessful = $loginResponse['status_code'] === 200 && isset($loginResponse['json']['data']['token']);
                    $workflowSteps['login'] = [
                        'successful' => $loginSuccessful,
                        'status_code' => $loginResponse['status_code'],
                        'duration' => $loginResponse['duration']
                    ];
                    
                    if (!$loginSuccessful) {
                        $allStepsSuccessful = false;
                    }
                    
                    $authToken = $loginSuccessful ? $loginResponse['json']['data']['token'] : null;
                    
                    // Step 2: Access protected resource
                    if ($authToken) {
                        $profileUrl = $baseUrl . '/auth/profile';
                        $headers = ['Authorization' => 'Bearer ' . $authToken];
                        $profileResponse = $testRunner->makeHttpRequest('GET', $profileUrl, null, $headers);
                        $profileSuccessful = $profileResponse['status_code'] === 200;
                        $workflowSteps['protected_access'] = [
                            'successful' => $profileSuccessful,
                            'status_code' => $profileResponse['status_code'],
                            'duration' => $profileResponse['duration']
                        ];
                        
                        if (!$profileSuccessful) {
                            $allStepsSuccessful = false;
                        }
                    } else {
                        $workflowSteps['protected_access'] = [
                            'successful' => false,
                            'reason' => 'No auth token available'
                        ];
                        $allStepsSuccessful = false;
                    }
                    
                    // Step 3: Logout
                    if ($authToken) {
                        $logoutUrl = $baseUrl . '/auth/logout';
                        $headers = ['Authorization' => 'Bearer ' . $authToken];
                        $logoutResponse = $testRunner->makeHttpRequest('POST', $logoutUrl, null, $headers);
                        $logoutSuccessful = in_array($logoutResponse['status_code'], [200, 204]);
                        $workflowSteps['logout'] = [
                            'successful' => $logoutSuccessful,
                            'status_code' => $logoutResponse['status_code'],
                            'duration' => $logoutResponse['duration']
                        ];
                        
                        if (!$logoutSuccessful) {
                            $allStepsSuccessful = false;
                        }
                    } else {
                        $workflowSteps['logout'] = [
                            'successful' => false,
                            'reason' => 'No auth token available'
                        ];
                        $allStepsSuccessful = false;
                    }
                    
                    $testPassed = $allStepsSuccessful;
                    $message = $testPassed 
                        ? "Complete API workflow successful"
                        : "API workflow has issues";
                    $details = ['workflow_steps' => $workflowSteps];
                    
                } catch (Exception $e) {
                    $testPassed = false;
                    $message = "API workflow test failed: " . $e->getMessage();
                    $details = ['error' => $e->getMessage()];
                }
                break;

            case 'cross_module_functionality':
                // Test cross-module functionality (e.g., user -> faculty -> study program)
                try {
                    $dbConfig = $config['database'];
                    $dsn = "pgsql:host={$dbConfig['hostname']};port={$dbConfig['port']};dbname={$dbConfig['database']}";
                    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
                    ]);
                    
                    $moduleTests = [];
                    
                    // Test user-role relationship
                    try {
                        $stmt = $pdo->query("
                            SELECT u.username, r.name as role_name 
                            FROM users u 
                            LEFT JOIN roles r ON u.role_id = r.id 
                            LIMIT 1
                        ");
                        $userRole = $stmt->fetch(PDO::FETCH_ASSOC);
                        $moduleTests['user_role'] = $userRole !== false;
                    } catch (Exception $e) {
                        $moduleTests['user_role'] = false;
                    }
                    
                    // Test faculty-study program relationship
                    try {
                        $stmt = $pdo->query("
                            SELECT f.name as faculty_name, sp.name as study_program_name 
                            FROM faculties f 
                            LEFT JOIN study_programs sp ON f.id = sp.faculty_id 
                            LIMIT 1
                        ");
                        $facultyProgram = $stmt->fetch(PDO::FETCH_ASSOC);
                        $moduleTests['faculty_program'] = $facultyProgram !== false;
                    } catch (Exception $e) {
                        $moduleTests['faculty_program'] = false;
                    }
                    
                    // Test study program-course relationship
                    try {
                        $stmt = $pdo->query("
                            SELECT sp.name as study_program_name, c.name as course_name 
                            FROM study_programs sp 
                            LEFT JOIN courses c ON sp.id = c.study_program_id 
                            LIMIT 1
                        ");
                        $programCourse = $stmt->fetch(PDO::FETCH_ASSOC);
                        $moduleTests['program_course'] = $programCourse !== false;
                    } catch (Exception $e) {
                        $moduleTests['program_course'] = false;
                    }
                    
                    $successfulTests = array_filter($moduleTests);
                    $testPassed = count($successfulTests) >= 2; // At least 2 relationships working
                    $message = $testPassed 
                        ? "Cross-module functionality working (" . count($successfulTests) . "/3 relationships)"
                        : "Cross-module functionality has issues";
                    $details = ['module_tests' => $moduleTests];
                    
                } catch (Exception $e) {
                    $testPassed = false;
                    $message = "Cross-module functionality test failed: " . $e->getMessage();
                    $details = ['error' => $e->getMessage()];
                }
                break;

            case 'data_flow_validation':
                // Test data flow between different components
                try {
                    $dataFlowTests = [];
                    
                    // Test API data format consistency
                    $loginUrl = $baseUrl . '/auth/login';
                    $testUser = $config['test_data']['users']['admin'];
                    $loginData = [
                        'username' => $testUser['username'],
                        'password' => $testUser['password']
                    ];
                    
                    $response = $testRunner->makeHttpRequest('POST', $loginUrl, $loginData);
                    
                    if ($response['status_code'] === 200 && isset($response['json'])) {
                        $responseData = $response['json'];
                        
                        // Check response structure
                        $hasStatus = isset($responseData['status']);
                        $hasMessage = isset($responseData['message']);
                        $hasData = isset($responseData['data']);
                        
                        $dataFlowTests['response_structure'] = $hasStatus && $hasMessage && $hasData;
                        
                        // Check data consistency
                        if ($hasData && isset($responseData['data']['user'])) {
                            $userData = $responseData['data']['user'];
                            $hasRequiredFields = isset($userData['id']) && isset($userData['username']);
                            $dataFlowTests['data_consistency'] = $hasRequiredFields;
                        } else {
                            $dataFlowTests['data_consistency'] = false;
                        }
                    } else {
                        $dataFlowTests['response_structure'] = false;
                        $dataFlowTests['data_consistency'] = false;
                    }
                    
                    $successfulTests = array_filter($dataFlowTests);
                    $testPassed = count($successfulTests) >= 1;
                    $message = $testPassed 
                        ? "Data flow validation successful"
                        : "Data flow validation failed";
                    $details = ['data_flow_tests' => $dataFlowTests];
                    
                } catch (Exception $e) {
                    $testPassed = false;
                    $message = "Data flow validation test failed: " . $e->getMessage();
                    $details = ['error' => $e->getMessage()];
                }
                break;

            case 'session_management':
                // Test session management across requests
                try {
                    // Login and get token
                    $loginUrl = $baseUrl . '/auth/login';
                    $testUser = $config['test_data']['users']['admin'];
                    $loginData = [
                        'username' => $testUser['username'],
                        'password' => $testUser['password']
                    ];
                    
                    $loginResponse = $testRunner->makeHttpRequest('POST', $loginUrl, $loginData);
                    
                    if ($loginResponse['status_code'] === 200 && isset($loginResponse['json']['data']['token'])) {
                        $token = $loginResponse['json']['data']['token'];
                        
                        // Make multiple requests with the same token
                        $sessionTests = [];
                        $headers = ['Authorization' => 'Bearer ' . $token];
                        
                        for ($i = 1; $i <= 3; $i++) {
                            $profileResponse = $testRunner->makeHttpRequest('GET', $baseUrl . '/auth/profile', null, $headers);
                            $sessionTests["request_{$i}"] = [
                                'status_code' => $profileResponse['status_code'],
                                'successful' => $profileResponse['status_code'] === 200
                            ];
                        }
                        
                        $successfulRequests = array_filter($sessionTests, fn($test) => $test['successful']);
                        $testPassed = count($successfulRequests) === 3;
                        $message = $testPassed 
                            ? "Session management working correctly"
                            : "Session management has issues";
                        $details = ['session_tests' => $sessionTests];
                    } else {
                        $testPassed = false;
                        $message = "Could not obtain session token for testing";
                        $details = ['login_response' => $loginResponse['status_code']];
                    }
                    
                } catch (Exception $e) {
                    $testPassed = false;
                    $message = "Session management test failed: " . $e->getMessage();
                    $details = ['error' => $e->getMessage()];
                }
                break;

            case 'file_upload_integration':
                // Test file upload integration (check if upload directory exists and is writable)
                try {
                    $uploadPath = __DIR__ . '/../../../backend/writable/uploads/';
                    $uploadTests = [];
                    
                    $uploadTests['directory_exists'] = is_dir($uploadPath);
                    $uploadTests['directory_writable'] = is_writable($uploadPath);
                    
                    // Check if we can create a test file
                    $testFile = $uploadPath . 'test_integration.txt';
                    $canCreateFile = false;
                    
                    try {
                        file_put_contents($testFile, 'Integration test file');
                        $canCreateFile = file_exists($testFile);
                        if ($canCreateFile) {
                            unlink($testFile); // Clean up
                        }
                    } catch (Exception $e) {
                        $canCreateFile = false;
                    }
                    
                    $uploadTests['can_create_file'] = $canCreateFile;
                    
                    $successfulTests = array_filter($uploadTests);
                    $testPassed = count($successfulTests) >= 2;
                    $message = $testPassed 
                        ? "File upload integration ready"
                        : "File upload integration has issues";
                    $details = [
                        'upload_path' => $uploadPath,
                        'upload_tests' => $uploadTests
                    ];
                    
                } catch (Exception $e) {
                    $testPassed = false;
                    $message = "File upload integration test failed: " . $e->getMessage();
                    $details = ['error' => $e->getMessage()];
                }
                break;

            case 'error_propagation':
                // Test error propagation from backend to frontend
                try {
                    $errorTests = [];
                    
                    // Test 404 error
                    $response404 = $testRunner->makeHttpRequest('GET', $baseUrl . '/nonexistent/endpoint');
                    $errorTests['404_error'] = [
                        'status_code' => $response404['status_code'],
                        'correct' => $response404['status_code'] === 404
                    ];
                    
                    // Test 401 error (unauthorized)
                    $response401 = $testRunner->makeHttpRequest('GET', $baseUrl . '/auth/profile');
                    $errorTests['401_error'] = [
                        'status_code' => $response401['status_code'],
                        'correct' => $response401['status_code'] === 401
                    ];
                    
                    // Test 400 error (bad request)
                    $response400 = $testRunner->makeHttpRequest('POST', $baseUrl . '/auth/login', 'invalid json');
                    $errorTests['400_error'] = [
                        'status_code' => $response400['status_code'],
                        'correct' => in_array($response400['status_code'], [400, 422])
                    ];
                    
                    $correctErrors = array_filter($errorTests, fn($test) => $test['correct']);
                    $testPassed = count($correctErrors) >= 2;
                    $message = $testPassed 
                        ? "Error propagation working correctly"
                        : "Error propagation has issues";
                    $details = ['error_tests' => $errorTests];
                    
                } catch (Exception $e) {
                    $testPassed = false;
                    $message = "Error propagation test failed: " . $e->getMessage();
                    $details = ['error' => $e->getMessage()];
                }
                break;
        }

    } catch (Exception $e) {
        $testPassed = false;
        $message = "Exception: " . $e->getMessage();
        $details = [
            'exception' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ];
    }

    $duration = round((microtime(true) - $startTime) * 1000);
    $status = $testPassed ? 'PASS' : 'FAIL';
    
    $testRunner->logTestResult($testName, $status, $message, $duration, $details);
    
    if ($testPassed) {
        $passedTests++;
    }
}

// Summary for this test category
$testRunner->logSubSection('Integration Tests Summary');
$testRunner->log('info', "Passed: {$passedTests}/{$totalTests} tests", 'SUMMARY');

if ($passedTests >= $totalTests * 0.7) { // 70% success rate for integration tests
    $testRunner->logSuccess("Integration tests passed! System components are working together.");
    return true;
} else {
    $failedTests = $totalTests - $passedTests;
    $testRunner->logError("Integration tests failed! {$failedTests} test(s) need attention.");
    return false;
}
