<?php

namespace App\Controllers\Api;

use CodeIgniter\RESTful\ResourceController;
use CodeIgniter\API\ResponseTrait;

class BaseApiController extends ResourceController
{
    use ResponseTrait;

    /**
     * Default response format
     */
    protected $format = 'json';

    /**
     * Constructor
     */
    public function __construct()
    {
        // Set CORS headers for all API responses
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
        
        // Handle preflight requests
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            http_response_code(200);
            exit();
        }
    }

    /**
     * Return success response
     */
    protected function respondSuccess($data = null, string $message = 'Success', int $code = 200)
    {
        $response = [
            'status' => 'success',
            'message' => $message,
            'code' => $code
        ];

        if ($data !== null) {
            $response['data'] = $data;
        }

        return $this->respond($response, $code);
    }

    /**
     * Return error response
     */
    protected function respondError(string $message = 'Error', int $code = 400, $errors = null)
    {
        $response = [
            'status' => 'error',
            'message' => $message,
            'code' => $code
        ];

        if ($errors !== null) {
            $response['errors'] = $errors;
        }

        return $this->respond($response, $code);
    }

    /**
     * Return validation error response
     */
    protected function respondValidationError($errors, string $message = 'Validation failed')
    {
        return $this->respondError($message, 422, $errors);
    }

    /**
     * Return not found response
     */
    protected function respondNotFound(string $message = 'Resource not found')
    {
        return $this->respondError($message, 404);
    }

    /**
     * Return unauthorized response
     */
    protected function respondUnauthorized(string $message = 'Unauthorized')
    {
        return $this->respondError($message, 401);
    }

    /**
     * Return forbidden response
     */
    protected function respondForbidden(string $message = 'Forbidden')
    {
        return $this->respondError($message, 403);
    }

    /**
     * Get authenticated user from request
     */
    protected function getAuthenticatedUser()
    {
        return $this->request->user ?? null;
    }

    /**
     * Check if user has specific role
     */
    protected function hasRole(string $role): bool
    {
        $user = $this->getAuthenticatedUser();
        return $user && isset($user->role) && $user->role === $role;
    }

    /**
     * Check if user has any of the specified roles
     */
    protected function hasAnyRole(array $roles): bool
    {
        $user = $this->getAuthenticatedUser();
        return $user && isset($user->role) && in_array($user->role, $roles);
    }

    /**
     * Validate request data
     */
    protected function validateRequest(array $rules, array $messages = [])
    {
        $validation = \Config\Services::validation();
        $validation->setRules($rules, $messages);

        if (!$validation->withRequest($this->request)->run()) {
            return $this->respondValidationError($validation->getErrors());
        }

        return null; // No validation errors
    }

    /**
     * Get paginated data
     */
    protected function getPaginatedData($model, int $page = 1, int $perPage = 10, array $conditions = [])
    {
        $offset = ($page - 1) * $perPage;
        
        $query = $model;
        
        // Apply conditions
        foreach ($conditions as $field => $value) {
            if (is_array($value)) {
                $query = $query->whereIn($field, $value);
            } else {
                $query = $query->where($field, $value);
            }
        }
        
        $total = $query->countAllResults(false);
        $data = $query->findAll($perPage, $offset);
        
        return [
            'data' => $data,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'total_pages' => ceil($total / $perPage),
                'has_next' => $page < ceil($total / $perPage),
                'has_prev' => $page > 1
            ]
        ];
    }
}
