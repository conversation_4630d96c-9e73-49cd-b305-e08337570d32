<?php

/**
 * RPS Management System - Migration Runner
 * 
 * This script provides a convenient way to run migrations
 * with proper error handling and logging.
 * 
 * Usage:
 * php run_migrations.php [action] [options]
 * 
 * Actions:
 * - migrate: Run all pending migrations
 * - rollback: Rollback last migration
 * - refresh: Rollback all and re-run
 * - status: Show migration status
 * - reset: Rollback all migrations
 */

// Ensure we're running from command line
if (php_sapi_name() !== 'cli') {
    die('This script can only be run from command line.');
}

// Define paths
define('FCPATH', __DIR__ . '/../public/');
define('SYSTEMPATH', __DIR__ . '/../system/');
define('APPPATH', __DIR__ . '/../app/');
define('WRITEPATH', __DIR__ . '/../writable/');
define('ROOTPATH', __DIR__ . '/../');

// Load CodeIgniter
require_once SYSTEMPATH . 'bootstrap.php';

// Get command line arguments
$action = $argv[1] ?? 'migrate';
$options = array_slice($argv, 2);

// Initialize CodeIgniter
$app = \Config\Services::codeigniter();
$app->initialize();

// Get migration service
$migrate = \Config\Services::migrations();

// Color output functions
function colorOutput($text, $color = 'white') {
    $colors = [
        'red'     => "\033[31m",
        'green'   => "\033[32m",
        'yellow'  => "\033[33m",
        'blue'    => "\033[34m",
        'magenta' => "\033[35m",
        'cyan'    => "\033[36m",
        'white'   => "\033[37m",
        'reset'   => "\033[0m"
    ];
    
    return $colors[$color] . $text . $colors['reset'];
}

function logMessage($message, $type = 'info') {
    $timestamp = date('Y-m-d H:i:s');
    $colors = [
        'info'    => 'cyan',
        'success' => 'green',
        'warning' => 'yellow',
        'error'   => 'red'
    ];
    
    echo colorOutput("[$timestamp] ", 'white');
    echo colorOutput(strtoupper($type) . ': ', $colors[$type]);
    echo colorOutput($message, 'white') . PHP_EOL;
}

function showUsage() {
    echo colorOutput("RPS Management System - Migration Runner", 'cyan') . PHP_EOL;
    echo colorOutput("Usage: php run_migrations.php [action] [options]", 'white') . PHP_EOL . PHP_EOL;
    
    echo colorOutput("Actions:", 'yellow') . PHP_EOL;
    echo "  migrate   - Run all pending migrations (default)" . PHP_EOL;
    echo "  rollback  - Rollback last migration" . PHP_EOL;
    echo "  refresh   - Rollback all and re-run migrations" . PHP_EOL;
    echo "  status    - Show migration status" . PHP_EOL;
    echo "  reset     - Rollback all migrations" . PHP_EOL;
    echo "  help      - Show this help message" . PHP_EOL . PHP_EOL;
    
    echo colorOutput("Examples:", 'yellow') . PHP_EOL;
    echo "  php run_migrations.php migrate" . PHP_EOL;
    echo "  php run_migrations.php rollback" . PHP_EOL;
    echo "  php run_migrations.php status" . PHP_EOL;
}

try {
    logMessage("Starting RPS Management System Migration Runner", 'info');
    
    switch ($action) {
        case 'migrate':
            logMessage("Running pending migrations...", 'info');
            
            if ($migrate->latest()) {
                logMessage("All migrations completed successfully!", 'success');
            } else {
                logMessage("No pending migrations found.", 'warning');
            }
            break;
            
        case 'rollback':
            logMessage("Rolling back last migration...", 'info');
            
            if ($migrate->regress()) {
                logMessage("Rollback completed successfully!", 'success');
            } else {
                logMessage("No migrations to rollback.", 'warning');
            }
            break;
            
        case 'refresh':
            logMessage("Refreshing all migrations...", 'info');
            
            // Rollback all
            logMessage("Rolling back all migrations...", 'info');
            $migrate->regress(0);
            
            // Run all
            logMessage("Running all migrations...", 'info');
            if ($migrate->latest()) {
                logMessage("Migration refresh completed successfully!", 'success');
            } else {
                logMessage("Failed to refresh migrations.", 'error');
            }
            break;
            
        case 'reset':
            logMessage("Resetting all migrations...", 'warning');
            
            if ($migrate->regress(0)) {
                logMessage("All migrations rolled back successfully!", 'success');
            } else {
                logMessage("No migrations to rollback.", 'warning');
            }
            break;
            
        case 'status':
            logMessage("Checking migration status...", 'info');
            
            // Get migration files
            $migrationFiles = [];
            $migrationPath = APPPATH . 'Database/Migrations/';
            
            if (is_dir($migrationPath)) {
                $files = scandir($migrationPath);
                foreach ($files as $file) {
                    if (pathinfo($file, PATHINFO_EXTENSION) === 'php') {
                        $migrationFiles[] = $file;
                    }
                }
            }
            
            // Get executed migrations
            $db = \Config\Database::connect();
            $executedMigrations = [];
            
            if ($db->tableExists('migrations')) {
                $query = $db->query("SELECT version FROM migrations ORDER BY version");
                $executedMigrations = array_column($query->getResultArray(), 'version');
            }
            
            echo PHP_EOL . colorOutput("Migration Status:", 'yellow') . PHP_EOL;
            echo str_repeat('-', 80) . PHP_EOL;
            
            foreach ($migrationFiles as $file) {
                $version = substr($file, 0, 14); // Extract timestamp
                $name = substr($file, 15, -4); // Extract name without .php
                
                $status = in_array($version, $executedMigrations) ? 
                    colorOutput('✓ EXECUTED', 'green') : 
                    colorOutput('✗ PENDING', 'red');
                
                printf("%-20s %-50s %s\n", $version, $name, $status);
            }
            
            echo str_repeat('-', 80) . PHP_EOL;
            echo "Total files: " . count($migrationFiles) . PHP_EOL;
            echo "Executed: " . colorOutput(count($executedMigrations), 'green') . PHP_EOL;
            echo "Pending: " . colorOutput(count($migrationFiles) - count($executedMigrations), 'red') . PHP_EOL;
            break;
            
        case 'help':
        case '--help':
        case '-h':
            showUsage();
            break;
            
        default:
            logMessage("Unknown action: $action", 'error');
            showUsage();
            exit(1);
    }
    
} catch (\Exception $e) {
    logMessage("Migration failed: " . $e->getMessage(), 'error');
    logMessage("Stack trace: " . $e->getTraceAsString(), 'error');
    exit(1);
}

logMessage("Migration runner completed.", 'success');
