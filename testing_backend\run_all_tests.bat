@echo off
echo.
echo ========================================
echo  RPS Backend Testing Framework
echo ========================================
echo.

cd /d "%~dp0"

echo [INFO] Starting comprehensive backend testing...
echo [INFO] Test results will be saved to reports/ directory
echo.

REM Check if PHP is available
php --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] PHP is not available in PATH
    echo [ERROR] Please ensure PHP 8.1+ is installed and accessible
    pause
    exit /b 1
)

REM Create reports directory if it doesn't exist
if not exist "reports" mkdir reports
if not exist "reports\logs" mkdir reports\logs
if not exist "reports\html" mkdir reports\html
if not exist "reports\json" mkdir reports\json

echo [INFO] Running backend tests...
echo.

REM Run the main test runner
php run_tests.php

echo.
echo ========================================
echo  Testing Complete
echo ========================================
echo.

REM Check if HTML report exists and open it
if exist "reports\html\test_report.html" (
    echo [INFO] Opening HTML test report...
    start reports\html\test_report.html
)

echo [INFO] Test results available in:
echo   - HTML Report: reports\html\test_report.html
echo   - JSON Results: reports\json\test_results.json
echo   - Detailed Logs: reports\logs\
echo.

pause
