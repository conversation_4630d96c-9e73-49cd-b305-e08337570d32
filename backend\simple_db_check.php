<?php

try {
    $pdo = new PDO('mysql:host=localhost;dbname=rpswebid', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connected to rpswebid database\n\n";
    
    // Show all tables
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "Tables found: " . count($tables) . "\n";
    foreach ($tables as $table) {
        echo "- $table\n";
    }
    
    // If we have users table, show some data
    if (in_array('users', $tables)) {
        echo "\nUsers in database:\n";
        $stmt = $pdo->query("SELECT id, username, email, full_name, role_id, is_active FROM users");
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($users as $user) {
            echo "ID: {$user['id']}, Username: {$user['username']}, Email: {$user['email']}, Active: {$user['is_active']}\n";
        }
    }
    
    // If we have roles table, show roles
    if (in_array('roles', $tables)) {
        echo "\nRoles in database:\n";
        $stmt = $pdo->query("SELECT id, name, description FROM roles");
        $roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($roles as $role) {
            echo "ID: {$role['id']}, Name: {$role['name']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
