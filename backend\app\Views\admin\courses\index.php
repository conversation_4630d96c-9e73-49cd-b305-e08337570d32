<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('page_css') ?>
<style>
.course-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: 1px solid #e0e0e0;
    border-radius: 0.5rem;
}

.course-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.course-header {
    background: linear-gradient(135deg, #696cff 0%, #5f61e6 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 0.5rem 0.5rem 0 0;
}

.course-code {
    font-size: 0.875rem;
    opacity: 0.9;
    margin-bottom: 0.5rem;
}

.course-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0;
}

.course-body {
    padding: 1.5rem;
}

.course-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.course-stats {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 600;
    color: #696cff;
}

.stat-label {
    font-size: 0.75rem;
    color: #a8b1bb;
    text-transform: uppercase;
}

.course-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="row">
    <div class="col-12 mb-4">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-1">
                    <i class="ri-book-open-line me-2"></i>Courses Management
                </h4>
                <p class="text-muted mb-0">Manage your academic courses and curriculum</p>
            </div>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCourseModal">
                <i class="ri-add-line me-2"></i>Add New Course
            </button>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="ri-search-line"></i>
                            </span>
                            <input type="text" class="form-control" placeholder="Search courses..." id="searchCourses">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="filterSemester">
                            <option value="">All Semesters</option>
                            <option value="1">Semester 1</option>
                            <option value="2">Semester 2</option>
                            <option value="3">Semester 3</option>
                            <option value="4">Semester 4</option>
                            <option value="5">Semester 5</option>
                            <option value="6">Semester 6</option>
                            <option value="7">Semester 7</option>
                            <option value="8">Semester 8</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="filterStatus">
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="button" class="btn btn-outline-secondary w-100">
                            <i class="ri-filter-line me-1"></i>Filter
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Courses Grid -->
    <div class="col-12">
        <?php if (empty($courses)): ?>
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="ri-book-open-line" style="font-size: 4rem; color: #e0e0e0;"></i>
                <h5 class="mt-3 mb-2">No Courses Found</h5>
                <p class="text-muted mb-4">Start by adding your first course to the system</p>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCourseModal">
                    <i class="ri-add-line me-2"></i>Add First Course
                </button>
            </div>
        </div>
        <?php else: ?>
        <div class="row">
            <?php foreach ($courses as $course): ?>
            <div class="col-lg-4 col-md-6 col-12 mb-4">
                <div class="card course-card">
                    <div class="course-header">
                        <div class="course-code"><?= esc($course['code']) ?></div>
                        <div class="course-title"><?= esc($course['name']) ?></div>
                    </div>
                    <div class="course-body">
                        <div class="course-meta">
                            <span class="badge bg-primary">Semester <?= $course['semester'] ?></span>
                            <span class="badge bg-success"><?= $course['credits'] ?> SKS</span>
                        </div>
                        
                        <div class="course-stats">
                            <div class="stat-item">
                                <div class="stat-number">5</div>
                                <div class="stat-label">CPMK</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">12</div>
                                <div class="stat-label">Topics</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">3</div>
                                <div class="stat-label">References</div>
                            </div>
                        </div>
                        
                        <p class="text-muted small mb-3"><?= esc(substr($course['description'], 0, 100)) ?>...</p>
                        
                        <div class="course-actions">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewCourse(<?= $course['id'] ?>)">
                                <i class="ri-eye-line"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-warning" onclick="editCourse(<?= $course['id'] ?>)">
                                <i class="ri-edit-line"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteCourse(<?= $course['id'] ?>)">
                                <i class="ri-delete-bin-line"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Add Course Modal -->
<div class="modal fade" id="addCourseModal" tabindex="-1" aria-labelledby="addCourseModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCourseModalLabel">
                    <i class="ri-book-add-line me-2"></i>Add New Course
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addCourseForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="courseCode" class="form-label">Course Code</label>
                            <input type="text" class="form-control" id="courseCode" name="code" placeholder="e.g., CS101" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="courseName" class="form-label">Course Name</label>
                            <input type="text" class="form-control" id="courseName" name="name" placeholder="e.g., Introduction to Programming" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="courseSemester" class="form-label">Semester</label>
                            <select class="form-select" id="courseSemester" name="semester" required>
                                <option value="">Select Semester</option>
                                <option value="1">Semester 1</option>
                                <option value="2">Semester 2</option>
                                <option value="3">Semester 3</option>
                                <option value="4">Semester 4</option>
                                <option value="5">Semester 5</option>
                                <option value="6">Semester 6</option>
                                <option value="7">Semester 7</option>
                                <option value="8">Semester 8</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="courseCredits" class="form-label">Credits (SKS)</label>
                            <input type="number" class="form-control" id="courseCredits" name="credits" min="1" max="6" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="courseType" class="form-label">Course Type</label>
                            <select class="form-select" id="courseType" name="type" required>
                                <option value="">Select Type</option>
                                <option value="mandatory">Mandatory</option>
                                <option value="elective">Elective</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="courseDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="courseDescription" name="description" rows="3" placeholder="Course description..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="ri-save-line me-2"></i>Save Course
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('page_js') ?>
<script>
// Search functionality
document.getElementById('searchCourses').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const courseCards = document.querySelectorAll('.course-card');
    
    courseCards.forEach(card => {
        const text = card.textContent.toLowerCase();
        card.closest('.col-lg-4').style.display = text.includes(searchTerm) ? '' : 'none';
    });
});

// Course actions
function viewCourse(courseId) {
    alert('View course: ' + courseId);
    // Implement view course functionality
}

function editCourse(courseId) {
    alert('Edit course: ' + courseId);
    // Implement edit course functionality
}

function deleteCourse(courseId) {
    if (confirm('Are you sure you want to delete this course?')) {
        alert('Delete course: ' + courseId);
        // Implement delete course functionality
    }
}

// Add course form submission
document.getElementById('addCourseForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    // Here you would typically send the data to the server
    alert('Course would be created with data: ' + JSON.stringify(Object.fromEntries(formData)));
    
    // Close modal and reset form
    const modal = bootstrap.Modal.getInstance(document.getElementById('addCourseModal'));
    modal.hide();
    this.reset();
});
</script>
<?= $this->endSection() ?>
