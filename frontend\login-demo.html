<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RPS Management - Login</title>
    <link href="https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@mdi/font@6.x/css/materialdesignicons.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/vuetify@3.4.0/dist/vuetify.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Roboto', sans-serif;
        }
        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 16px;
        }
        .login-card {
            max-width: 400px;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 32px;
            animation: slideUp 0.5s ease-out;
        }
        .logo-section {
            text-align: center;
            margin-bottom: 24px;
        }
        .logo-avatar {
            width: 60px;
            height: 60px;
            background: #1976D2;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .logo-icon {
            color: white;
            font-size: 30px;
        }
        .app-title {
            font-size: 24px;
            font-weight: bold;
            color: #1976D2;
            margin-bottom: 4px;
        }
        .app-subtitle {
            font-size: 14px;
            color: #666;
        }
        .form-group {
            margin-bottom: 16px;
        }
        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }
        .form-input:focus {
            outline: none;
            border-color: #1976D2;
        }
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }
        .checkbox-group input {
            margin-right: 8px;
        }
        .login-btn {
            width: 100%;
            padding: 12px;
            background: #1976D2;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s;
            margin-bottom: 16px;
        }
        .login-btn:hover {
            background: #1565C0;
        }
        .forgot-password {
            text-align: center;
        }
        .forgot-password a {
            color: #1976D2;
            text-decoration: none;
            font-size: 14px;
        }
        .system-info {
            text-align: center;
            margin-top: 16px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
        }
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        @media (max-width: 600px) {
            .login-container {
                padding: 8px;
            }
            .login-card {
                padding: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <!-- Logo and Title -->
            <div class="logo-section">
                <div class="logo-avatar">
                    <i class="mdi mdi-school logo-icon"></i>
                </div>
                <h1 class="app-title">RPS Management</h1>
                <p class="app-subtitle">Sistem Manajemen Rencana Pembelajaran Semester</p>
            </div>

            <!-- Login Form -->
            <form>
                <div class="form-group">
                    <label class="form-label" for="username">Username atau Email</label>
                    <input type="text" id="username" class="form-input" placeholder="Masukkan username atau email">
                </div>

                <div class="form-group">
                    <label class="form-label" for="password">Password</label>
                    <input type="password" id="password" class="form-input" placeholder="Masukkan password">
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="remember">
                    <label for="remember">Remember me</label>
                </div>

                <button type="submit" class="login-btn">
                    <i class="mdi mdi-login"></i> Login
                </button>
            </form>

            <!-- Additional Links -->
            <div class="forgot-password">
                <a href="#">Forgot Password?</a>
            </div>
        </div>

        <!-- System Info -->
        <div class="system-info">
            <p>RPS Management System v1.0.0</p>
            <p>© 2025 Universitas Example</p>
        </div>
    </div>
</body>
</html>
