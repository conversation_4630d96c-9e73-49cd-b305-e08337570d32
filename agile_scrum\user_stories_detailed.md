# RPS Management System - Detailed User Stories

## 📋 User Stories by Role

**Product Owner:** <PERSON><PERSON><PERSON><PERSON>, <PERSON>.<PERSON>, MT  
**Last Updated:** January 25, 2025  

## 👤 User Personas

### 1. Admin Sistem
**Name:** <PERSON><PERSON>  
**Role:** System Administrator  
**Goals:** Maintain system stability, manage users, ensure data security  
**Pain Points:** Manual user management, lack of audit trail  

### 2. Dekan
**Name:** Prof. Dr. <PERSON>, <PERSON><PERSON>  
**Role:** Faculty Dean  
**Goals:** Oversee faculty academic quality, strategic planning  
**Pain Points:** Limited visibility into program performance  

### 3. <PERSON><PERSON><PERSON>
**Name:** Dr<PERSON>, <PERSON>.T  
**Role:** Vice Dean for Academic Affairs  
**Goals:** Ensure curriculum quality, coordinate academic programs  
**Pain Points:** Fragmented curriculum data, manual reporting  

### 4. Kepala Program Studi
**Name:** Dr. <PERSON>, <PERSON>.<PERSON>  
**Role:** Head of Study Program  
**Goals:** Manage curriculum, ensure learning outcome achievement  
**Pain Points:** Time-consuming RPS creation, difficult CPMK tracking  

### 5. Sekretaris Program Studi
**Name:** <PERSON><PERSON>, <PERSON>.<PERSON>  
**Role:** Study Program Secretary  
**Goals:** Support administrative tasks, maintain accurate records  
**Pain Points:** Manual data entry, repetitive reporting tasks  

### 6. Dosen Pengampu
**Name:** Dr. Joko Widodo, M.T  
**Role:** Course Lecturer  
**Goals:** Create effective RPS, track student progress  
**Pain Points:** Complex RPS format, manual assessment tracking  

## 🎯 Epic 1: Authentication & User Management

### US-001: Secure User Authentication
**As a** system user  
**I want to** login securely with my institutional credentials  
**So that** I can access the system safely and efficiently  

**Priority:** Must Have  
**Story Points:** 8  
**Sprint:** 1  

#### Acceptance Criteria
- [ ] **AC1.1:** User can login with username/email and password
- [ ] **AC1.2:** System validates credentials against database
- [ ] **AC1.3:** JWT token is generated upon successful authentication
- [ ] **AC1.4:** Failed login attempts are logged and limited (max 5 attempts)
- [ ] **AC1.5:** Account is temporarily locked after failed attempts
- [ ] **AC1.6:** Session expires after 8 hours of inactivity
- [ ] **AC1.7:** User can logout and invalidate session
- [ ] **AC1.8:** Password must meet complexity requirements

#### Technical Requirements
- JWT token with 8-hour expiration
- Bcrypt password hashing
- Rate limiting for login attempts
- Audit logging for security events
- HTTPS enforcement

#### Definition of Done
- [ ] Unit tests written and passing
- [ ] Integration tests for authentication flow
- [ ] Security review completed
- [ ] Performance tested (< 200ms response time)
- [ ] Documentation updated

### US-002: Granular Role-Based Access Control
**As a** system administrator  
**I want to** assign specific roles and permissions to users  
**So that** each user can only access features appropriate to their position  

**Priority:** Must Have  
**Story Points:** 13  
**Sprint:** 1  

#### Acceptance Criteria
- [ ] **AC2.1:** Six distinct user roles are implemented (Admin, Dekan, Wakil Dekan, Kepala Prodi, Sekretaris Prodi, Dosen)
- [ ] **AC2.2:** Granular permissions control access to specific features
- [ ] **AC2.3:** Role assignments can be scoped to specific faculty/study program
- [ ] **AC2.4:** Permission inheritance from organizational hierarchy
- [ ] **AC2.5:** Real-time permission validation on every request
- [ ] **AC2.6:** Audit trail for all permission changes
- [ ] **AC2.7:** Role assignments have start and end dates
- [ ] **AC2.8:** User can have multiple roles in different contexts

#### Role-Permission Matrix
| Role | Faculty Mgmt | Course Mgmt | RPS Mgmt | Reporting |
|------|-------------|-------------|----------|-----------|
| Admin | Full | View All | View All | Full |
| Dekan | Own Faculty | Faculty | Faculty | Faculty |
| Wakil Dekan | Own Faculty | Faculty | Faculty | Faculty |
| Kepala Prodi | View | Own Prodi | Own Prodi | Own Prodi |
| Sekretaris Prodi | View | View Prodi | View Prodi | View Prodi |
| Dosen | View | Assigned | Own | Own |

## 🎯 Epic 2: Master Data Management

### US-005: Faculty Management
**As a** system administrator  
**I want to** manage faculty organizational structure  
**So that** the system reflects the current institutional hierarchy  

**Priority:** Must Have  
**Story Points:** 5  
**Sprint:** 2  

#### Acceptance Criteria
- [ ] **AC5.1:** Create new faculty with complete information
- [ ] **AC5.2:** Assign dean and vice deans to faculty
- [ ] **AC5.3:** View faculty hierarchy and statistics
- [ ] **AC5.4:** Update faculty information and leadership
- [ ] **AC5.5:** Deactivate faculty (soft delete with dependency check)
- [ ] **AC5.6:** Faculty code must be unique across system
- [ ] **AC5.7:** Validation for required fields and data formats

#### Business Rules
- Faculty code format: 3-letter uppercase (e.g., FTI, FEB)
- Dean assignment requires user with appropriate role
- Cannot delete faculty with active study programs
- Faculty name must be unique within institution

### US-006: Study Program Management
**As a** dekan or wakil dekan  
**I want to** manage study programs within my faculty  
**So that** academic programs are properly organized and maintained  

**Priority:** Must Have  
**Story Points:** 8  
**Sprint:** 2  

#### Acceptance Criteria
- [ ] **AC6.1:** Create study program with complete curriculum information
- [ ] **AC6.2:** Assign head and secretary to study program
- [ ] **AC6.3:** Set degree level and study type
- [ ] **AC6.4:** Track accreditation status and dates
- [ ] **AC6.5:** Define total credits and study period
- [ ] **AC6.6:** Manage program vision, mission, and objectives
- [ ] **AC6.7:** View program statistics and course allocation
- [ ] **AC6.8:** Cannot delete program with active courses

## 🎯 Epic 3: Course Management

### US-007: Comprehensive Course Management
**As a** kepala program studi  
**I want to** manage all aspects of course information  
**So that** curriculum is properly maintained and up-to-date  

**Priority:** Must Have  
**Story Points:** 13  
**Sprint:** 3  

#### Acceptance Criteria
- [ ] **AC7.1:** Create course with complete information (code, name, credits, semester)
- [ ] **AC7.2:** Set course type (mandatory/elective) and prerequisites
- [ ] **AC7.3:** Assign course coordinator and teaching staff
- [ ] **AC7.4:** Define learning objectives and course description
- [ ] **AC7.5:** Manage course status (active/inactive)
- [ ] **AC7.6:** Bulk operations for course management
- [ ] **AC7.7:** Course code uniqueness within study program
- [ ] **AC7.8:** Prerequisite validation (circular dependency check)

#### Business Rules
- Course code format: [PRODI][LEVEL][SEQUENCE] (e.g., TIF101, TIF201)
- Credits must be between 1-6
- Semester must be 1-8 for undergraduate programs
- Prerequisites must be from earlier semesters

### US-008: Course Reference Management
**As a** dosen pengampu  
**I want to** manage course references and learning materials  
**So that** students have access to proper learning resources  

**Priority:** Must Have  
**Story Points:** 8  
**Sprint:** 3  

#### Acceptance Criteria
- [ ] **AC8.1:** Add references with complete bibliographic information
- [ ] **AC8.2:** Categorize references (main/supporting/additional)
- [ ] **AC8.3:** Upload and attach reference files
- [ ] **AC8.4:** Set reference availability and access level
- [ ] **AC8.5:** Generate reference list in standard format
- [ ] **AC8.6:** Validate reference information completeness
- [ ] **AC8.7:** Support multiple reference formats (book, journal, web)

## 🎯 Epic 4: CPMK & CPL Management

### US-010: Graduate Learning Outcomes (CPL) Management
**As a** kepala program studi  
**I want to** define and manage graduate learning outcomes  
**So that** program objectives are clearly articulated and measurable  

**Priority:** Must Have  
**Story Points:** 13  
**Sprint:** 4  

#### Acceptance Criteria
- [ ] **AC10.1:** Create CPL with clear, measurable statements
- [ ] **AC10.2:** Categorize CPL (attitude, knowledge, general skills, specific skills)
- [ ] **AC10.3:** Map CPL to SNPT standards and accreditation requirements
- [ ] **AC10.4:** Set achievement targets and measurement criteria
- [ ] **AC10.5:** Import/export CPL data for reporting
- [ ] **AC10.6:** Version control for CPL changes
- [ ] **AC10.7:** Validate CPL completeness and coverage
- [ ] **AC10.8:** Generate CPL reports and documentation

#### CPL Categories (SNPT Standard)
1. **Sikap (Attitude):** Professional ethics, responsibility, integrity
2. **Pengetahuan (Knowledge):** Theoretical foundation, domain expertise
3. **Keterampilan Umum (General Skills):** Communication, teamwork, leadership
4. **Keterampilan Khusus (Specific Skills):** Technical competencies, problem-solving

### US-011: Course Learning Outcomes (CPMK) Management
**As a** dosen pengampu  
**I want to** define course learning outcomes that align with program goals  
**So that** course objectives contribute to overall program achievement  

**Priority:** Must Have  
**Story Points:** 13  
**Sprint:** 4  

#### Acceptance Criteria
- [ ] **AC11.1:** Create CPMK with specific, measurable outcomes
- [ ] **AC11.2:** Assign cognitive level (Bloom's taxonomy C1-C6)
- [ ] **AC11.3:** Set weight percentage for each CPMK
- [ ] **AC11.4:** Map CPMK to relevant CPL
- [ ] **AC11.5:** Define assessment methods for each CPMK
- [ ] **AC11.6:** Validate CPMK coverage and balance
- [ ] **AC11.7:** Generate CPMK documentation and reports
- [ ] **AC11.8:** Track CPMK achievement over time

#### Cognitive Levels (Bloom's Taxonomy)
- **C1 (Remember):** Recall facts, terms, concepts
- **C2 (Understand):** Explain ideas, concepts
- **C3 (Apply):** Use information in new situations
- **C4 (Analyze):** Draw connections, identify patterns
- **C5 (Evaluate):** Justify decisions, critique
- **C6 (Create):** Produce new work, design solutions

### US-012: CPMK-CPL Mapping and Analysis
**As a** kepala program studi  
**I want to** map course outcomes to program outcomes  
**So that** I can ensure curriculum coherence and coverage  

**Priority:** Must Have  
**Story Points:** 13  
**Sprint:** 5  

#### Acceptance Criteria
- [ ] **AC12.1:** Visual mapping interface for CPMK-CPL relationships
- [ ] **AC12.2:** Set contribution levels (low, medium, high) for each mapping
- [ ] **AC12.3:** Assign weight percentages for contribution calculation
- [ ] **AC12.4:** Validate mapping completeness and balance
- [ ] **AC12.5:** Generate mapping matrix reports
- [ ] **AC12.6:** Analyze CPL coverage across curriculum
- [ ] **AC12.7:** Identify gaps and overlaps in mapping
- [ ] **AC12.8:** Impact analysis for curriculum changes

## 🎯 Epic 5: Assessment System

### US-013: Assessment Method Management
**As a** dosen pengampu  
**I want to** define various assessment methods  
**So that** I can evaluate student learning comprehensively  

**Priority:** Must Have  
**Story Points:** 8  
**Sprint:** 5  

#### Acceptance Criteria
- [ ] **AC13.1:** Create assessment method library
- [ ] **AC13.2:** Categorize methods (formative/summative)
- [ ] **AC13.3:** Define method types (assignment, quiz, exam, project, etc.)
- [ ] **AC13.4:** Set default parameters for each method
- [ ] **AC13.5:** Create custom assessment methods
- [ ] **AC13.6:** Template system for common assessments
- [ ] **AC13.7:** Usage analytics and recommendations

#### Assessment Types
- **Tugas (Assignment):** Individual/group tasks
- **Kuis (Quiz):** Short assessments
- **UTS (Midterm Exam):** Mid-semester examination
- **UAS (Final Exam):** End-semester examination
- **Praktikum (Lab Work):** Practical assessments
- **Proyek (Project):** Long-term projects
- **Presentasi (Presentation):** Oral presentations

### US-014: Assessment Planning and Scheduling
**As a** dosen pengampu  
**I want to** plan assessments for each CPMK  
**So that** learning outcomes are systematically evaluated  

**Priority:** Must Have  
**Story Points:** 13  
**Sprint:** 5  

#### Acceptance Criteria
- [ ] **AC14.1:** Create assessment plan for each course
- [ ] **AC14.2:** Map assessments to specific CPMK
- [ ] **AC14.3:** Set weight distribution across assessments
- [ ] **AC14.4:** Schedule assessments throughout semester
- [ ] **AC14.5:** Define rubrics and grading criteria
- [ ] **AC14.6:** Set passing grades and achievement thresholds
- [ ] **AC14.7:** Generate assessment calendar
- [ ] **AC14.8:** Validate assessment coverage and balance

## 📊 Story Estimation Guidelines

### Story Point Scale (Fibonacci)
- **1 Point:** Very simple, well-understood task (< 2 hours)
- **2 Points:** Simple task with minimal complexity (2-4 hours)
- **3 Points:** Moderate task with some complexity (4-8 hours)
- **5 Points:** Complex task requiring research (1-2 days)
- **8 Points:** Very complex task with unknowns (2-3 days)
- **13 Points:** Epic-level task, should be broken down (3-5 days)
- **21 Points:** Too large, must be split into smaller stories

### Estimation Factors
- **Technical Complexity:** Algorithm complexity, integration points
- **Business Complexity:** Business rules, validation requirements
- **Uncertainty:** Unknown requirements, technical risks
- **Dependencies:** External systems, team dependencies
- **Testing Effort:** Unit, integration, and acceptance testing

## ✅ Definition of Ready (DoR)

Before a story enters a sprint:
- [ ] Story is clearly written with acceptance criteria
- [ ] Business value is understood and articulated
- [ ] Dependencies are identified and resolved
- [ ] Story is estimated by the team
- [ ] Mockups/wireframes available if needed
- [ ] Technical approach is understood
- [ ] Story fits within sprint capacity

## ✅ Definition of Done (DoD)

Before a story is considered complete:
- [ ] All acceptance criteria are met
- [ ] Code is written and reviewed
- [ ] Unit tests written and passing (>80% coverage)
- [ ] Integration tests passing
- [ ] Security review completed
- [ ] Performance requirements met
- [ ] Documentation updated
- [ ] Demo prepared and delivered
- [ ] Product Owner acceptance obtained

---

**Note:** User stories akan terus diperbarui berdasarkan feedback stakeholder dan learning dari sprint sebelumnya. Prioritas dapat berubah sesuai kebutuhan bisnis.
