<template>
  <div class="pa-6">
    <div class="text-center py-8">
      <v-icon size="64" color="grey-lighten-2">mdi-format-list-checks</v-icon>
      <p class="text-h6 text-medium-emphasis mt-4">Assessment Rubric Builder</p>
      <p class="text-body-2 text-medium-emphasis">
        Create and manage detailed rubrics for {{ assessmentPlan.assessment_title }}
      </p>
      <p class="text-caption text-medium-emphasis mt-2">
        This feature will be fully implemented in the next development phase
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { AssessmentPlan } from '@/types/auth'

interface Props {
  assessmentPlan: AssessmentPlan
}

defineProps<Props>()
defineEmits<{
  close: []
  updated: []
}>()
</script>
