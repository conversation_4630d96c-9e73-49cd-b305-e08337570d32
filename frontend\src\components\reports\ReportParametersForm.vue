<template>
  <div>
    <div class="text-center py-8">
      <v-icon size="48" color="grey-lighten-2">mdi-form-select</v-icon>
      <p class="text-h6 text-medium-emphasis mt-4">Report Parameters Form</p>
      <p class="text-body-2 text-medium-emphasis">
        Dynamic form for configuring report parameters for {{ report.name }}
      </p>
      <p class="text-caption text-medium-emphasis mt-2">
        This feature will be fully implemented in the next development phase
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  report: any
  modelValue: any
}

defineProps<Props>()
defineEmits<{
  'update:modelValue': [value: any]
}>()
</script>
