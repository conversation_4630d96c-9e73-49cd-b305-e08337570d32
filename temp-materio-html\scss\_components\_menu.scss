/* Menu
******************************************************************************* */

.menu {
  display: flex;
  background-color: var(--#{$prefix}menu-bg);

  .app-brand {
    inline-size: 100%;
    @include transition(padding .3s ease-in-out);
    padding-inline: calc(calc(#{var(--#{$prefix}menu-vertical-link-padding-x)} * 2) - .25rem);
    .app-brand-text {
      color: var(--#{$prefix}heading-color);
    }
  }

  // Sub menu item link bullet
  .menu-sub > .menu-item > .menu-link{
    &::before {
      position: absolute;
      border: 1.5px solid var(--#{$prefix}body-color);
      block-size: .625rem;
      content: "";
      inline-size: .625rem;
      inset-inline-start: $menu-icon-expanded-left-spacer;
      @include border-radius(50%);
    }
  }

  .ps.ps--active-y > .ps__rail-y {
    background: none;
  }
  .ps__rail-y {
    inset-inline: auto .25rem !important;
  }

  .ps .ps__thumb-y {
    inline-size: .125rem;
  }
  .ps__thumb-y,
  .ps__rail-y.ps--clicking > .ps__thumb-y {
    opacity: .3;
  }
}

.menu-inner {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 0;
  margin: 0;
  block-size: 100%;

  > .menu-item.menu-item-closing .menu-item.open .menu-sub,
  > .menu-item.menu-item-closing .menu-item.open .menu-toggle {
    background-color: transparent;
    color: var(--#{$prefix}menu-color);
  }

  > .menu-header {
    &::before,
    &::after {
      display: block;
      background-color: rgba(var(--#{$prefix}base-color-rgb), .2);
      block-size: 1px;
      content: "";
    }
    &::before {
      inline-size: 8%;
      margin-inline: -$menu-vertical-link-padding-x $menu-icon-expanded-spacer;
    }
    &::after {
      inline-size: 90%;
      margin-inline-start: $menu-icon-expanded-spacer;
    }
    @include media-breakpoint-down(xl) {
      margin-inline: 0 !important;
    }
  }
}

.menu-inner-shadow {
  position: absolute;
  z-index: 2;
  display: none;
  background: linear-gradient(var(--#{$prefix}menu-bg) 41%, rgba(var(--#{$prefix}menu-bg-rgb), .11) 95%, rgba(var(--#{$prefix}menu-bg-rgb), 0));
  inline-size: 100%;
  inset-block-start: $navbar-height - (($navbar-height - 3rem) * .5);
  pointer-events: none;
  @include media-breakpoint-up($menu-collapsed-layout-breakpoint) {
    block-size: 3rem;
  }
  @include media-breakpoint-down($menu-collapsed-layout-breakpoint) {
    block-size: 1.5rem;
  }
  .layout-navbar-full & {
    inset-block-start: 0;
  }
}

/* Menu item */

.menu-item {
  align-items: flex-start;
  justify-content: flex-start;

  &.menu-item-animating {
    transition: block-size $menu-animation-duration ease-in-out;
  }
  &.active > .menu-link:not(.menu-toggle) {
    background: linear-gradient(270deg, var(--#{$prefix}primary) 0%, color-mix(in sRGB, var(--#{$prefix}primary) 52%, var(--#{$prefix}white)) 100%);
    box-shadow: var(--#{$prefix}box-shadow-sm);
    color: var(--#{$prefix}menu-active-color);
  }
}

.menu-item,
.menu-header,
.menu-divider,
.menu-block {
  flex: 0 0 auto;
  flex-direction: column;
  padding: 0;
  margin: 0;
  list-style: none;
}
.menu-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  inline-size: 100%;
  line-height: normal;
  opacity: 1;
  transition: opacity $menu-animation-duration ease-in-out;
  white-space: nowrap;
  .menu-header-text {
    color: var(--#{$prefix}secondary-color);
    column-gap: .625rem;
    font-size: $font-size-xs;
    letter-spacing: .4px;
    text-transform: uppercase;
    white-space: nowrap;
  }
}

/* Menu Icon */
.menu-icon {
  flex-grow: 0;
  flex-shrink: 0;
  block-size: $menu-icon-expanded-font-size;
  font-size: $menu-icon-expanded-font-size;
  inline-size: $menu-icon-expanded-font-size;
  margin-inline-end: $menu-icon-expanded-spacer;

  .menu:not(.menu-no-animation) & {
    transition: margin-inline-end $menu-animation-duration ease;
  }

  .menu-link {
    transition-duration: $menu-animation-duration;
  }
  .menu-toggle::after {
    transition-duration: $menu-animation-duration;
    transition-property: -webkit-transform, transform;
  }
}

/* Menu link */
.menu-link {
  position: relative;
  display: flex;
  flex: 0 1 auto;
  align-items: center;
  margin: 0;
  color: var(--#{$prefix}menu-color);

  .menu-item.disabled & {
    cursor: not-allowed;
    opacity: .6;
  }

  > :not(.menu-icon) {
    flex: 0 1 auto;
    opacity: 1;
  }

  &:hover,
  &:focus {
    color: var(--#{$prefix}menu-hover-color);
  }
}

/* Sub menu */
.menu-sub {
  display: none;
  flex-direction: column;
  padding: 0;
  margin: 0;

  .menu-item.open > & {
    display: flex;
  }
}

/* Menu toggle open/close arrow */
.menu-toggle::after {
  position: absolute;
  display: block;
  background-color: var(--#{$prefix}menu-item-icon-color);
  content: "";
  inset-block-start: 52%;
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='m13.172 12l-4.95-4.95l1.414-1.413L16 12l-6.364 6.364l-1.414-1.415z'/%3E%3C/svg%3E");
  mask-repeat: no-repeat;
  mask-size: 100% 100%;
  transform: translateY(-50%);
  @include icon-base($menu-icon-expanded-font-size);
}


/* Menu divider */
.menu-divider {
  border: 0;
  border-block-start: 1px solid;
  border-block-start-color: var(--#{$prefix}menu-divider-color);
  inline-size: 100%;
}


/* Vertical Menu
****************************************************************************** */

.menu-vertical {
  overflow: hidden;
  flex-direction: column;

  // menu expand collapse animation
  &:not(.menu-no-animation) {
    transition: inline-size $menu-animation-duration;
  }

  &,
  .menu-block,
  .menu-inner > .menu-item,
  .menu-inner > .menu-header {
    inline-size: var(--#{$prefix}menu-width);
  }

  &,
  .menu-block,
  .menu-inner > .menu-item {
    &:first-of-type {
      margin-block-start: 0;
    }
  }
  .menu-inner {
    flex: 1 1 auto;
    flex-direction: column;

    > .menu-item {
      margin-block-start: $menu-item-spacer;
      margin-inline: 0;

      /* menu-link spacing */
      .menu-link {
        @include border-end-radius($border-radius-pill);
        margin-block: 0;
        margin-inline-end: $menu-vertical-link-margin-x;
      }
    }
  }

  .menu-item .menu-link,
  .menu-block {
    padding-block: var(--#{$prefix}menu-vertical-link-padding-y);
    padding-inline: var(--#{$prefix}menu-vertical-link-padding-x);
  }
  .menu-header {
    padding-block: .5rem;
    padding-inline: calc($menu-vertical-link-margin-x + .325rem);
  }

  .menu-item .menu-link {
    font-size: $menu-font-size;
    min-block-size: 38px;
    > div:not(.badge) {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .menu-item {
    .menu-toggle {
      padding-inline-end: calc(#{var(--#{$prefix}menu-vertical-link-padding-x)} + #{$caret-width * 2.45});
      &::after {
        inset-inline-end: calc(#{$menu-vertical-link-padding-x} - #{.5rem});
        transition: transform $menu-animation-duration;
      }
    }
    &:not(.active):not(.open) .menu-link:hover {
      background-color: var(--#{$prefix}menu-hover-bg);
    }
    &.active > .menu-toggle {
      background-color: var(--#{$prefix}menu-sub-active-bg);
    }
    &.active:not(.open) > .menu-link:not(.menu-toggle)::before {
      border-color: var(--#{$prefix}menu-active-color);
    }
  }

  .menu-item.open:not(.menu-item-closing) > .menu-link::after {
    transform: translateY(-50%) rotate(90deg);
  }

  .menu-divider {
    padding: 0;
    margin-block: $menu-link-spacer-x;
  }

  .menu-sub {
    .menu-item {
      margin-block: $menu-item-spacer 0;
      margin-inline: 0;
    }
  }

  .menu-icon {
    @include icon-base($menu-icon-expanded-font-size);
  }

  ~ .menu-mobile-toggler {
    display: none;
    .layout-navbar-hidden & {
      position: fixed;
      z-index: 1067;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--#{$prefix}secondary);
      inset-block-end: calc(#{$container-padding-x} * 2);
      inset-inline-start: $container-padding-x;
    }
  }

  // Levels
  $menu-first-level-spacer: $menu-vertical-link-padding-x + $menu-icon-expanded-width + $menu-icon-expanded-spacer - .125;
  .menu-sub .menu-link {
    padding-inline-start: $menu-first-level-spacer;
  }

  // Menu levels loop for padding left/right
  @for $i from 2 through $menu-max-levels {
    $selector: "";
    @for $l from 1 through $i {
      $selector: "#{$selector} .menu-sub";
    }

    .layout-wrapper:not(.layout-horizontal) & {
      .menu-inner > .menu-item {
        #{$selector} {
          .menu-link {
            padding-inline-start: $menu-first-level-spacer + ($menu-vertical-menu-level-spacer * ($i)) - .25;
            &::before {
              inset-inline-start: $menu-icon-expanded-left-spacer + ($menu-vertical-menu-level-spacer * $i) - .25;
            }
          }
        }
      }
    }
  }
  .menu-sub .menu-sub .menu-link {
    .menu-icon {
      margin-inline-end: $menu-horizontal-link-padding-x;
      &::before {
        display: flex;
        font-size: $menu-horizontal-sub-menu-icon-size;
      }
    }
  }
}

/* Vertical Menu Collapsed
******************************************************************************* */

@mixin layout-menu-collapsed() {
  inline-size: var(--#{$prefix}menu-collapsed-width);
  .menu-inner > .menu-item {
    inline-size: var(--#{$prefix}menu-collapsed-width);
  }
  .menu-inner > .menu-header,
  .menu-block {
    position: relative;
    inline-size: var(--#{$prefix}menu-width);
    margin-inline-start: var(--#{$prefix}menu-collapsed-width);
    padding-inline: $menu-icon-expanded-spacer calc(calc(var(--#{$prefix}menu-vertical-link-padding-x) * 2) - $menu-icon-expanded-spacer);
    text-indent: -9999px;
    text-overflow: ellipsis;
    white-space: nowrap;
    .menu-header-text {
      overflow: hidden;
      opacity: 0;
    }
    &::before {
      position: absolute;
      display: block;
      content: "";
      inline-size: calc(var(--#{$prefix}menu-collapsed-width) * .31);
      inset-block: .5rem;
      inset-inline-start: calc(-1 * calc(var(--#{$prefix}menu-collapsed-width) * .635));
      margin-block-start: .4375rem;
      margin-inline-start: 0;
      text-align: center;
    }
  }
  .menu-inner > .menu-header {
    &::before {
      block-size: .0625rem;
    }
  }

  .app-brand {
    padding-inline-start: $menu-vertical-link-padding-x - .2rem;
  }

  .menu-inner > .menu-item div:not(.menu-block) {
    overflow: hidden;
    opacity: 0;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .menu-inner > .menu-item > .menu-sub,
  .menu-inner > .menu-item.open > .menu-sub {
    display: none;
  }
  .menu-inner > .menu-item > .menu-toggle::after {
    display: none;
  }

  &:not(.layout-menu-hover) {
    .menu-inner > .menu-item > .menu-link,
    .menu-inner > .menu-block,
    .menu-inner > .menu-header {
      padding-inline-end: calc(#{$menu-vertical-link-padding-x} + #{$caret-width * 1.94});
      padding-inline-start: calc(#{$menu-vertical-link-padding-x} + .01rem);
    }
  }

  .menu-inner > .menu-item > .menu-link .menu-icon {
    margin-inline-end: 0;
    text-align: center;
  }
}

/* Only for menu example */
.layout-menu-collapsed .layout-menu,
.menu-collapsed {
  &:not(:hover) {
    @include media-breakpoint-up(xl) {
      @include layout-menu-collapsed();
    }
  }
  &:hover {
    box-shadow: var(--#{$prefix}menu-box-shadow);
  }
}


@include keyframes(menuDropdownShow) {
  0% {
    opacity: 0;
    transform: translateY(-.5rem);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
