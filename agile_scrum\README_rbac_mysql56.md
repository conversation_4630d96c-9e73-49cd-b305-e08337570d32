# RPS Management System - Enhanced RBAC for MySQL 5.6

## 📋 Overview

File `database_design_rbac_mysql56.sql` adalah versi MySQL 5.6 dari enhanced RBAC system yang dikonversi dari PostgreSQL dengan **granular permission system** dan **contextual role assignments**.

## 🔄 MySQL 5.6 Compatibility Changes

### **Key Modifications for MySQL 5.6:**

| PostgreSQL Original | MySQL 5.6 Compatible | Reason |
|-------------------|----------------------|---------|
| `BOOLEAN` | `TINYINT(1)` | BOOLEAN alias untuk TINYINT di 5.6 |
| `ENUM(...)` | `ENUM(...)` | Supported in MySQL 5.6 |
| `JSON` | `TEXT` with JSON comment | JSON type tidak tersedia di 5.6 |
| `utf8mb4` | `utf8` | utf8mb4 tidak tersedia di 5.6 |
| `SERIAL` | `AUTO_INCREMENT` | Different syntax |

### **Specific RBAC Changes:**
```sql
-- PostgreSQL
is_active BOOLEAN DEFAULT TRUE,
conditions JSON,

-- MySQL 5.6 Compatible
`is_active` TINYINT(1) DEFAULT 1,
`conditions` TEXT NULL COMMENT 'JSON formatted additional conditions',
```

## 🏗️ **Enhanced RBAC Database Structure (10 Tables)**

### **Core RBAC Tables (6 tables)**
- `organizational_positions` - Jabatan struktural (11 positions)
- `users` - Enhanced user profiles dengan academic info
- `roles` - Enhanced role system (10 roles)
- `user_roles` - Contextual role assignments
- `permissions` - Granular permissions (30+ permissions)
- `role_permissions` - Role-permission mappings

### **Organizational Tables (4 tables)**
- `faculties` - Faculty management dengan leadership
- `study_programs` - Program studi dengan complete info
- `position_assignments` - Penugasan jabatan struktural
- `user_permissions` - User-specific permission overrides

## 🎯 **Enhanced RBAC Features**

### **1. Organizational Positions (11 Positions)**
```sql
-- Hierarchical organizational structure
('REKTOR', 'Rektor', 1, 'Pimpinan tertinggi universitas'),
('DEKAN', 'Dekan', 2, 'Pimpinan fakultas'),
('WAKIL_DEKAN_AKADEMIK', 'Wakil Dekan Bidang Akademik', 2, 'Wakil dekan bidang akademik'),
('KEPALA_PRODI', 'Kepala Program Studi', 3, 'Pimpinan program studi'),
('SEKRETARIS_PRODI', 'Sekretaris Program Studi', 3, 'Sekretaris program studi'),
('DOSEN_PENGAMPU', 'Dosen Pengampu', 4, 'Dosen pengampu mata kuliah'),
-- ... dan lainnya
```

### **2. Enhanced Role System (10 Roles)**
```sql
-- Roles dengan level dan requirements
('SUPER_ADMIN', 'super_admin', 'Super Administrator', 'system', 1, 0),
('ADMIN_SISTEM', 'admin_sistem', 'Administrator Sistem', 'university', 0, 0),
('DEKAN', 'dekan', 'Dekan', 'faculty', 0, 1),  -- requires_position = 1
('WAKIL_DEKAN', 'wakil_dekan', 'Wakil Dekan', 'faculty', 0, 1),
('KEPALA_PRODI', 'kepala_prodi', 'Kepala Program Studi', 'study_program', 0, 1),
('DOSEN_PENGAMPU', 'dosen_pengampu', 'Dosen Pengampu', 'course', 0, 0),
-- ... dan lainnya
```

### **3. Granular Permissions (30+ Permissions)**
```sql
-- User Management (10 permissions)
('USER_CREATE', 'Create User', 'users', 'create', 'department'),
('USER_READ_ALL', 'Read All Users', 'users', 'read', 'all'),
('USER_UPDATE_FACULTY', 'Update Faculty Users', 'users', 'update', 'faculty'),

-- Faculty Management (6 permissions)
('FACULTY_CREATE', 'Create Faculty', 'faculties', 'create', 'university'),
('FACULTY_READ_OWN', 'Read Own Faculty', 'faculties', 'read', 'faculty'),

-- Course Management (10 permissions)
('COURSE_CREATE', 'Create Course', 'courses', 'create', 'department'),
('COURSE_READ_ASSIGNED', 'Read Assigned Courses', 'courses', 'read', 'own'),

-- RPS Management (10 permissions)
('RPS_CREATE', 'Create RPS', 'rps', 'create', 'own'),
('RPS_APPROVE', 'Approve RPS', 'rps', 'approve', 'department'),

-- System Administration (10 permissions)
('SYSTEM_BACKUP', 'System Backup', 'system', 'backup', 'all'),
('REPORT_VIEW_FACULTY', 'View Faculty Reports', 'reports', 'read', 'faculty'),
```

### **4. Contextual Role Assignments**
```sql
-- User roles dengan context
CREATE TABLE `user_roles` (
    `user_id` INT UNSIGNED NOT NULL,
    `role_id` INT UNSIGNED NOT NULL,
    `faculty_id` INT UNSIGNED NULL COMMENT 'Context: di fakultas mana',
    `study_program_id` INT UNSIGNED NULL COMMENT 'Context: di prodi mana',
    `course_id` INT UNSIGNED NULL COMMENT 'Context: untuk mata kuliah mana',
    `academic_year_id` INT UNSIGNED NULL COMMENT 'Context: tahun akademik',
    `start_date` DATE NOT NULL,
    `end_date` DATE NULL,
    -- ...
);
```

### **5. Permission Scopes**
- **own** - Hanya data milik sendiri
- **department** - Data dalam departemen/prodi
- **faculty** - Data dalam fakultas
- **university** - Data seluruh universitas
- **all** - Semua data sistem

## 🔒 **Role-Permission Mappings**

### **Super Admin**
- **Full Access** - Semua permissions (30+ permissions)

### **Admin Sistem**
- **User Management** - Create, read all, update all, delete, assign role
- **System Administration** - Backup, restore, maintenance, logs, config
- **Reporting** - View all, export

### **Dekan**
- **Faculty Management** - Read own, update own
- **Study Program** - Create, read faculty, update all, delete
- **Course Management** - Read faculty, update all, delete, assign lecturer
- **RPS Management** - Read faculty, update all, approve
- **User Management** - Read faculty, update faculty, assign role

### **Kepala Prodi**
- **Study Program** - Read own, update own
- **Course Management** - Create, read prodi, update prodi, delete, assign lecturer
- **RPS Management** - Read prodi, update prodi, approve
- **Reporting** - View prodi, export

### **Dosen Pengampu**
- **Course Management** - Read assigned, update assigned
- **RPS Management** - Create, read own, update own, delete own
- **User Management** - Read own, update own

## 📊 **Enhanced Views**

### **1. User Details with Roles**
```sql
CREATE VIEW `v_user_details` AS
SELECT u.id, u.username, u.full_name, u.nip, u.nidn,
       GROUP_CONCAT(CONCAT(r.display_name, 
                          CASE WHEN ur.faculty_id IS NOT NULL 
                               THEN CONCAT(' (', f.name, ')') 
                               ELSE '' END)) as roles
FROM `users` u
LEFT JOIN `user_roles` ur ON u.id = ur.user_id
LEFT JOIN `roles` r ON ur.role_id = r.id
LEFT JOIN `faculties` f ON ur.faculty_id = f.id;
```

### **2. Role Permissions**
```sql
CREATE VIEW `v_role_permissions` AS
SELECT r.code as role_code, r.display_name, p.code as permission_code,
       p.resource, p.action, p.scope
FROM `roles` r
JOIN `role_permissions` rp ON r.id = rp.role_id
JOIN `permissions` p ON rp.permission_id = p.id;
```

### **3. User Permissions (Combined)**
```sql
CREATE VIEW `v_user_permissions` AS
-- Permissions dari roles
SELECT u.username, p.code, p.resource, p.action, 'role' as source
FROM `users` u
JOIN `user_roles` ur ON u.id = ur.user_id
JOIN `role_permissions` rp ON ur.role_id = rp.role_id
JOIN `permissions` p ON rp.permission_id = p.id

UNION

-- Direct permissions
SELECT u.username, p.code, p.resource, p.action, 'direct' as source
FROM `users` u
JOIN `user_permissions` up ON u.id = up.user_id
JOIN `permissions` p ON up.permission_id = p.id;
```

## 🚀 **Installation & Usage**

### **Prerequisites**
- **MySQL 5.6** or higher
- **UTF-8 support** enabled
- **InnoDB storage engine**
- Minimum **100MB** storage space

### **Installation**
```bash
# Install MySQL 5.6
sudo apt-get install mysql-server-5.6

# Run RBAC schema
mysql -u root -p < database_design_rbac_mysql56.sql
```

### **Verification**
```sql
-- Check tables
USE rps_management;
SHOW TABLES;

-- Check organizational positions
SELECT * FROM organizational_positions;

-- Check roles
SELECT * FROM roles;

-- Check permissions count
SELECT COUNT(*) as total_permissions FROM permissions;

-- Check role-permission mappings
SELECT r.display_name, COUNT(rp.permission_id) as permission_count
FROM roles r
LEFT JOIN role_permissions rp ON r.id = rp.role_id
GROUP BY r.id, r.display_name;

-- Test views
SELECT * FROM v_user_details LIMIT 5;
SELECT * FROM v_role_permissions WHERE role_code = 'DEKAN';
```

## 📈 **Performance Optimizations**

### **Indexing Strategy**
```sql
-- Primary keys dengan AUTO_INCREMENT
-- Foreign key indexes untuk JOIN performance
-- Business logic indexes
INDEX `idx_user_roles_user` (`user_id`),
INDEX `idx_user_roles_role` (`role_id`),
INDEX `idx_user_roles_faculty` (`faculty_id`),
INDEX `idx_permissions_resource_action` (`resource`, `action`),
INDEX `idx_role_permissions_role` (`role_id`),
```

### **Query Optimization**
```sql
-- Efficient permission checking
SELECT COUNT(*) FROM user_roles ur
JOIN role_permissions rp ON ur.role_id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
WHERE ur.user_id = ? AND p.code = ? AND ur.is_active = 1;
```

## 🔧 **Application Integration**

### **Permission Checking Function**
```php
class RBACManager {
    public function hasPermission($userId, $permissionCode, $context = []) {
        // Check role-based permissions
        $sql = "SELECT COUNT(*) FROM user_roles ur
                JOIN role_permissions rp ON ur.role_id = rp.role_id
                JOIN permissions p ON rp.permission_id = p.id
                WHERE ur.user_id = ? AND p.code = ? 
                AND ur.is_active = 1 AND rp.is_granted = 1";
        
        // Add context filters
        if (isset($context['faculty_id'])) {
            $sql .= " AND (ur.faculty_id IS NULL OR ur.faculty_id = ?)";
        }
        
        return $this->db->query($sql, $params)->fetchColumn() > 0;
    }
}
```

### **Role Assignment**
```php
public function assignRole($userId, $roleCode, $context = []) {
    $sql = "INSERT INTO user_roles 
            (user_id, role_id, faculty_id, study_program_id, start_date)
            SELECT ?, r.id, ?, ?, CURDATE()
            FROM roles r WHERE r.code = ?";
    
    return $this->db->execute($sql, [
        $userId, 
        $context['faculty_id'] ?? null,
        $context['study_program_id'] ?? null,
        $roleCode
    ]);
}
```

## 🎯 **Security Features**

### **1. Granular Access Control**
- **30+ specific permissions** untuk fine-grained control
- **Contextual scoping** (faculty, study program, course level)
- **Time-bound assignments** dengan start/end dates
- **Permission inheritance** dari organizational hierarchy

### **2. Audit Trail**
- **created_by** tracking untuk semua assignments
- **Timestamp tracking** untuk semua changes
- **Assignment reasons** untuk accountability
- **Position assignment** history

### **3. Data Integrity**
- **Foreign key constraints** untuk referential integrity
- **Unique constraints** untuk business rules
- **Proper indexing** untuk performance
- **Soft delete** dengan is_active flags

## 🎯 **Conclusion**

File `database_design_rbac_mysql56.sql` menyediakan **enterprise-grade RBAC system** yang **100% kompatibel dengan MySQL 5.6** dengan fitur:

- ✅ **10 tables** dengan complete RBAC functionality
- ✅ **11 organizational positions** untuk hierarchy
- ✅ **10 enhanced roles** dengan level dan requirements
- ✅ **30+ granular permissions** dengan scope control
- ✅ **Contextual role assignments** untuk flexibility
- ✅ **3 useful views** untuk reporting
- ✅ **Complete data seeding** untuk immediate use
- ✅ **Production-ready** dengan proper indexing

Sistem ini siap untuk deployment di environment MySQL 5.6 dan menyediakan foundation yang solid untuk RPS Management System dengan security dan scalability yang enterprise-grade.

---

**Note:** Meskipun kompatibel dengan MySQL 5.6, disarankan untuk upgrade ke MySQL 8.0+ untuk mendapatkan fitur JSON native dan performance improvements yang lebih baik.
