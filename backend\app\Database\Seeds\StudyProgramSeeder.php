<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class StudyProgramSeeder extends Seeder
{
    public function run()
    {
        // Get faculty IDs
        $faculties = $this->db->table('faculties')->get()->getResultArray();
        $facultyMap = [];
        foreach ($faculties as $faculty) {
            $facultyMap[$faculty['code']] = $faculty['id'];
        }

        // Get kaprodi user ID
        $kaprodiUser = $this->db->table('users')
            ->where('username', 'kaprodi.ti')
            ->get()
            ->getRowArray();

        $data = [
            // Fakultas Teknik
            [
                'code' => 'TIF',
                'name' => 'Teknik Informatika',
                'faculty_id' => $facultyMap['FT'],
                'head_id' => $kaprodiUser['id'] ?? null,
                'degree_level' => 'S1',
                'accreditation' => 'B',
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'code' => 'TI',
                'name' => 'Teknik Industri',
                'faculty_id' => $facultyMap['FT'],
                'head_id' => null,
                'degree_level' => 'S1',
                'accreditation' => 'B',
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'code' => 'TE',
                'name' => 'Teknik Elektro',
                'faculty_id' => $facultyMap['FT'],
                'head_id' => null,
                'degree_level' => 'S1',
                'accreditation' => 'B',
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'code' => 'TM',
                'name' => 'Teknik Mesin',
                'faculty_id' => $facultyMap['FT'],
                'head_id' => null,
                'degree_level' => 'S1',
                'accreditation' => 'B',
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'code' => 'TS',
                'name' => 'Teknik Sipil',
                'faculty_id' => $facultyMap['FT'],
                'head_id' => null,
                'degree_level' => 'S1',
                'accreditation' => 'B',
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'), 
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            
         // Fakultas Ekonomi & Bisnis
            [
                'code' => 'MJ',
                'name' => 'Manajemen',
                'faculty_id' => $facultyMap['FEB'],
                'head_id' => null,
                'degree_level' => 'S1',
                'accreditation' => 'B',
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'code' => 'AK',
                'name' => 'Akuntansi',
                'faculty_id' => $facultyMap['FEB'],
                'head_id' => null,
                'degree_level' => 'S1',
                'accreditation' => 'A',
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            
            // Fakultas FP
            [
                'code' => 'AGRI',
                'name' => 'Agribisnis',
                'faculty_id' => $facultyMap['FP'],
                'head_id' => null,
                'degree_level' => 'S1',
                'accreditation' => 'B',
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'code' => 'AGRO',
                'name' => 'Agroteknologi',
                'faculty_id' => $facultyMap['FP'],
                'head_id' => null,
                'degree_level' => 'S1',
                'accreditation' => 'B',
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'code' => 'THP',
                'name' => 'Teknologi Hasil Pertanian',
                'faculty_id' => $facultyMap['FP'],
                'head_id' => null,
                'degree_level' => 'S1',
                'accreditation' => 'B',
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            
            // Fakultas Hukum
            [
                'code' => 'HUM',
                'name' => 'Ilmu Hukum',
                'faculty_id' => $facultyMap['FH'],
                'head_id' => null,
                'degree_level' => 'S1',
                'accreditation' => 'B',
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ];

        // Insert data
        $this->db->table('study_programs')->insertBatch($data);
        
        echo "Study programs seeded successfully!" . PHP_EOL;
    }
}
