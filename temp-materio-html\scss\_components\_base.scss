// Base
// *******************************************************************************

body {
  letter-spacing: $letter-spacing;
}

/* App Overlay
******************************************************************************* */

.app-overlay {
  position: absolute;
  z-index: 3;
  background-color: transparent;
  inset: 0;
  transition: all .25s ease-in-out;
  visibility: hidden;

  &.show {
    background-color: rgba($black, .5);
    visibility: visible;
  }
}

/* IE Fixes
******************************************************************************* */

@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  /* Fix IE parent container height bug when containing image with fluid width */
  .card,
  .card-body,
  .media,
  .flex-column,
  .tab-content {
    min-block-size: 1px;
  }

  img {
    block-size: auto;
    min-block-size: 1px;
  }
}

/* Containers
******************************************************************************* */

.container,
.container-fluid,
.container-xxl {
  padding-inline: $container-padding-x-sm;

  @include media-breakpoint-up(lg) {
    padding-inline: $container-padding-x;
  }
}

/* Buy now section
******************************************************************************* */
.buy-now {
  .btn-buy-now {
    position: fixed;
    z-index: $zindex-menu-fixed;
    box-shadow: 0 1px 20px 1px $danger;
    inset-block-end: 3rem;
    inset-inline-end: $container-padding-x;

    &:hover {
      box-shadow: none !important;
    }
  }
}
