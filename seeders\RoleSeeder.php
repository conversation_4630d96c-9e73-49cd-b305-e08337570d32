<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class RoleSeeder extends Seeder
{
    public function run()
    {
        $data = [
            [
                'name' => 'Super Admin',
                'description' => 'Full system access with all permissions',
                'permissions' => json_encode([
                    'all' => true,
                    'system_admin' => true,
                    'user_management' => true,
                    'faculty_management' => true,
                    'study_program_management' => true,
                    'course_management' => true,
                    'rps_management' => true,
                    'assessment_management' => true,
                    'report_management' => true
                ]),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Admin <PERSON>',
                'description' => 'Faculty level administration',
                'permissions' => json_encode([
                    'faculty_management' => true,
                    'study_program_management' => true,
                    'course_management' => true,
                    'user_management' => ['scope' => 'faculty'],
                    'rps_management' => ['scope' => 'faculty'],
                    'report_management' => ['scope' => 'faculty']
                ]),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Koordinator Prodi',
                'description' => 'Study program coordination and management',
                'permissions' => json_encode([
                    'study_program_management' => ['scope' => 'own'],
                    'course_management' => ['scope' => 'study_program'],
                    'rps_management' => ['scope' => 'study_program'],
                    'assessment_management' => ['scope' => 'study_program'],
                    'user_management' => ['scope' => 'study_program'],
                    'report_management' => ['scope' => 'study_program']
                ]),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Dosen',
                'description' => 'Course management and teaching',
                'permissions' => json_encode([
                    'course_management' => ['scope' => 'assigned'],
                    'rps_management' => ['scope' => 'own'],
                    'assessment_management' => ['scope' => 'own'],
                    'student_management' => ['scope' => 'course'],
                    'report_management' => ['scope' => 'own']
                ]),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Mahasiswa',
                'description' => 'Student access for viewing course information',
                'permissions' => json_encode([
                    'course_view' => true,
                    'rps_view' => true,
                    'assessment_view' => ['scope' => 'own'],
                    'profile_management' => ['scope' => 'own']
                ]),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Staff Akademik',
                'description' => 'Academic staff with administrative access',
                'permissions' => json_encode([
                    'course_view' => true,
                    'rps_view' => true,
                    'student_management' => ['scope' => 'faculty'],
                    'report_management' => ['scope' => 'faculty']
                ]),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ];

        // Insert data
        $this->db->table('roles')->insertBatch($data);
        
        echo "Roles seeded successfully!" . PHP_EOL;
    }
}
