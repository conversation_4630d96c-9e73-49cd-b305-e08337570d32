<template>
  <div>
    <!-- Welcome Header -->
    <v-row class="mb-6">
      <v-col>
        <h1 class="text-h4 font-weight-bold text-primary">
          Welcome back, {{ authStore.user?.full_name }}!
        </h1>
        <p class="text-subtitle-1 text-medium-emphasis">
          Here's what's happening with your RPS management system today.
        </p>
      </v-col>
    </v-row>

    <!-- Statistics Cards -->
    <v-row class="mb-6">
      <v-col
        v-for="stat in statistics"
        :key="stat.title"
        cols="12"
        sm="6"
        md="3"
      >
        <v-card
          class="stat-card"
          :color="stat.color"
          variant="tonal"
          hover
        >
          <v-card-text>
            <div class="d-flex align-center justify-space-between">
              <div>
                <p class="text-subtitle-2 text-medium-emphasis mb-1">
                  {{ stat.title }}
                </p>
                <h2 class="text-h4 font-weight-bold">
                  {{ stat.value }}
                </h2>
                <div class="d-flex align-center mt-2">
                  <v-icon
                    :color="stat.trend > 0 ? 'success' : 'error'"
                    size="small"
                    class="mr-1"
                  >
                    {{ stat.trend > 0 ? 'mdi-trending-up' : 'mdi-trending-down' }}
                  </v-icon>
                  <span
                    :class="stat.trend > 0 ? 'text-success' : 'text-error'"
                    class="text-caption"
                  >
                    {{ Math.abs(stat.trend) }}% from last month
                  </span>
                </div>
              </div>
              <v-icon
                :color="stat.color"
                size="48"
                class="stat-icon"
              >
                {{ stat.icon }}
              </v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <v-row>
      <!-- CPMK Achievement Chart -->
      <v-col cols="12" md="8">
        <v-card class="mb-6">
          <v-card-title class="d-flex align-center justify-space-between">
            <div class="d-flex align-center">
              <v-icon class="mr-2">mdi-chart-line</v-icon>
              <span class="text-h6 font-weight-bold">CPMK Achievement Trends</span>
            </div>
            <v-btn-toggle
              v-model="chartPeriod"
              variant="outlined"
              size="small"
              mandatory
            >
              <v-btn value="week">Week</v-btn>
              <v-btn value="month">Month</v-btn>
              <v-btn value="semester">Semester</v-btn>
            </v-btn-toggle>
          </v-card-title>
          <v-card-text>
            <div class="chart-container">
              <LineChart
                v-if="cpmkChartData"
                :data="cpmkChartData"
                :options="chartOptions"
              />
              <div v-else class="d-flex align-center justify-center" style="height: 300px;">
                <v-progress-circular indeterminate color="primary" />
              </div>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- Quick Actions -->
      <v-col cols="12" md="4">
        <v-card class="mb-6">
          <v-card-title class="text-h6 font-weight-bold">
            <v-icon class="mr-2">mdi-lightning-bolt</v-icon>
            Quick Actions
          </v-card-title>
          <v-card-text>
            <v-list>
              <v-list-item
                v-for="action in quickActions"
                :key="action.title"
                :to="action.to"
                class="px-0"
              >
                <v-list-item-prepend>
                  <v-avatar :color="action.color" size="40">
                    <v-icon color="white">{{ action.icon }}</v-icon>
                  </v-avatar>
                </v-list-item-prepend>
                <v-list-item-title class="font-weight-medium">
                  {{ action.title }}
                </v-list-item-title>
                <v-list-item-subtitle>
                  {{ action.subtitle }}
                </v-list-item-subtitle>
              </v-list-item>
            </v-list>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <v-row>
      <!-- CPL Distribution -->
      <v-col cols="12" md="6">
        <v-card class="mb-6">
          <v-card-title class="text-h6 font-weight-bold">
            <v-icon class="mr-2">mdi-chart-donut</v-icon>
            CPL Distribution by Category
          </v-card-title>
          <v-card-text>
            <div class="chart-container">
              <DoughnutChart
                v-if="cplChartData"
                :data="cplChartData"
                :options="doughnutOptions"
              />
              <div v-else class="d-flex align-center justify-center" style="height: 250px;">
                <v-progress-circular indeterminate color="primary" />
              </div>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- Recent Activities -->
      <v-col cols="12" md="6">
        <v-card class="mb-6">
          <v-card-title class="d-flex align-center justify-space-between">
            <div class="d-flex align-center">
              <v-icon class="mr-2">mdi-history</v-icon>
              <span class="text-h6 font-weight-bold">Recent Activities</span>
            </div>
            <v-btn
              variant="text"
              size="small"
              @click="refreshActivities"
            >
              <v-icon>mdi-refresh</v-icon>
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-timeline
              density="compact"
              align="start"
              class="activity-timeline"
            >
              <v-timeline-item
                v-for="activity in recentActivities"
                :key="activity.id"
                :dot-color="getActivityColor(activity.action)"
                size="small"
              >
                <div class="d-flex align-center justify-space-between">
                  <div>
                    <p class="text-body-2 font-weight-medium mb-1">
                      {{ activity.description }}
                    </p>
                    <p class="text-caption text-medium-emphasis">
                      by {{ activity.user_name }}
                    </p>
                  </div>
                  <span class="text-caption text-medium-emphasis">
                    {{ formatTimeAgo(activity.created_at) }}
                  </span>
                </div>
              </v-timeline-item>
            </v-timeline>
            
            <div v-if="recentActivities.length === 0" class="text-center py-4">
              <v-icon size="48" color="grey-lighten-2">mdi-history</v-icon>
              <p class="text-body-2 text-medium-emphasis mt-2">
                No recent activities
              </p>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Assessment Calendar -->
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title class="text-h6 font-weight-bold">
            <v-icon class="mr-2">mdi-calendar</v-icon>
            Upcoming Assessments
          </v-card-title>
          <v-card-text>
            <v-row>
              <v-col
                v-for="assessment in upcomingAssessments"
                :key="assessment.id"
                cols="12"
                sm="6"
                md="4"
                lg="3"
              >
                <v-card
                  variant="outlined"
                  class="assessment-card"
                  hover
                >
                  <v-card-text>
                    <div class="d-flex align-center justify-space-between mb-2">
                      <v-chip
                        :color="getAssessmentTypeColor(assessment.type)"
                        size="small"
                        variant="tonal"
                      >
                        {{ assessment.type }}
                      </v-chip>
                      <span class="text-caption">
                        {{ formatDate(assessment.due_date) }}
                      </span>
                    </div>
                    <h4 class="text-subtitle-1 font-weight-bold mb-1">
                      {{ assessment.title }}
                    </h4>
                    <p class="text-body-2 text-medium-emphasis mb-2">
                      {{ assessment.course_name }}
                    </p>
                    <v-progress-linear
                      :model-value="assessment.completion_rate"
                      :color="getProgressColor(assessment.completion_rate)"
                      height="6"
                      rounded
                    />
                    <p class="text-caption mt-1">
                      {{ assessment.completion_rate }}% completed
                    </p>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
            
            <div v-if="upcomingAssessments.length === 0" class="text-center py-8">
              <v-icon size="64" color="grey-lighten-2">mdi-calendar-check</v-icon>
              <p class="text-h6 text-medium-emphasis mt-4">
                No upcoming assessments
              </p>
              <p class="text-body-2 text-medium-emphasis">
                All caught up! Great work.
              </p>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { dashboardAPI } from '@/services/api'
import LineChart from '@/components/charts/LineChart.vue'
import DoughnutChart from '@/components/charts/DoughnutChart.vue'
import type { DashboardStats, Activity, ChartData } from '@/types/auth'

const authStore = useAuthStore()

// State
const loading = ref(false)
const chartPeriod = ref('month')
const dashboardStats = ref<DashboardStats | null>(null)
const cpmkChartData = ref<ChartData | null>(null)
const cplChartData = ref<ChartData | null>(null)
const recentActivities = ref<Activity[]>([])

// Mock data for demonstration
const statistics = computed(() => [
  {
    title: 'Total Courses',
    value: dashboardStats.value?.total_courses || 24,
    trend: 12,
    icon: 'mdi-book-open-page-variant',
    color: 'primary'
  },
  {
    title: 'Active CPMK',
    value: dashboardStats.value?.total_cpmk || 156,
    trend: 8,
    icon: 'mdi-bullseye-arrow',
    color: 'success'
  },
  {
    title: 'CPL Mapped',
    value: dashboardStats.value?.total_cpl || 32,
    trend: -3,
    icon: 'mdi-target',
    color: 'warning'
  },
  {
    title: 'Assessments',
    value: dashboardStats.value?.active_assessments || 89,
    trend: 15,
    icon: 'mdi-clipboard-check',
    color: 'info'
  }
])

const quickActions = computed(() => {
  const actions = [
    {
      title: 'Create New Course',
      subtitle: 'Add a new course to curriculum',
      icon: 'mdi-plus',
      color: 'primary',
      to: '/courses?action=create'
    },
    {
      title: 'Manage CPMK',
      subtitle: 'Define learning outcomes',
      icon: 'mdi-bullseye-arrow',
      color: 'success',
      to: '/cpmk'
    },
    {
      title: 'Assessment Planning',
      subtitle: 'Plan course assessments',
      icon: 'mdi-calendar-plus',
      color: 'warning',
      to: '/assessments'
    },
    {
      title: 'Generate Reports',
      subtitle: 'View analytics and reports',
      icon: 'mdi-chart-line',
      color: 'info',
      to: '/reports'
    }
  ]
  
  // Filter based on user roles
  return actions.filter(action => {
    // Add role-based filtering logic here
    return true
  })
})

const upcomingAssessments = ref([
  {
    id: 1,
    title: 'Midterm Exam',
    course_name: 'Database Systems',
    type: 'UTS',
    due_date: '2025-02-15',
    completion_rate: 75
  },
  {
    id: 2,
    title: 'Project Presentation',
    course_name: 'Software Engineering',
    type: 'Project',
    due_date: '2025-02-20',
    completion_rate: 45
  },
  {
    id: 3,
    title: 'Lab Assignment',
    course_name: 'Computer Networks',
    type: 'Lab',
    due_date: '2025-02-25',
    completion_rate: 90
  }
])

// Chart options
const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
    },
    title: {
      display: false,
    },
  },
  scales: {
    y: {
      beginAtZero: true,
      max: 100,
    },
  },
}

const doughnutOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom' as const,
    },
  },
}

// Methods
const loadDashboardData = async () => {
  loading.value = true
  try {
    // Load dashboard statistics
    const statsResponse = await dashboardAPI.getStats()
    dashboardStats.value = statsResponse.data.data

    // Load CPMK chart data
    const cpmkResponse = await dashboardAPI.getChartData('cpmk-achievement')
    cpmkChartData.value = cpmkResponse.data.data

    // Load CPL chart data
    const cplResponse = await dashboardAPI.getChartData('cpl-distribution')
    cplChartData.value = cplResponse.data.data

    // Load recent activities
    const activitiesResponse = await dashboardAPI.getRecentActivities()
    recentActivities.value = activitiesResponse.data.data
  } catch (error) {
    console.error('Failed to load dashboard data:', error)
    // Use mock data for demonstration
    loadMockData()
  } finally {
    loading.value = false
  }
}

const loadMockData = () => {
  // Mock CPMK chart data
  cpmkChartData.value = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        label: 'CPMK Achievement (%)',
        data: [65, 72, 68, 75, 82, 78],
        borderColor: 'rgb(25, 118, 210)',
        backgroundColor: 'rgba(25, 118, 210, 0.1)',
        borderWidth: 2
      }
    ]
  }

  // Mock CPL chart data
  cplChartData.value = {
    labels: ['Attitude', 'Knowledge', 'General Skills', 'Specific Skills'],
    datasets: [
      {
        label: 'CPL Distribution',
        data: [25, 35, 20, 20],
        backgroundColor: [
          'rgba(76, 175, 80, 0.8)',
          'rgba(33, 150, 243, 0.8)',
          'rgba(255, 193, 7, 0.8)',
          'rgba(156, 39, 176, 0.8)'
        ]
      }
    ]
  }

  // Mock recent activities
  recentActivities.value = [
    {
      id: 1,
      user_id: 1,
      user_name: 'Dr. Ahmad Rahman',
      action: 'create',
      resource_type: 'course',
      resource_id: 1,
      resource_name: 'Database Systems',
      description: 'Created new course "Database Systems"',
      created_at: '2025-01-25T10:30:00Z'
    },
    {
      id: 2,
      user_id: 2,
      user_name: 'Dr. Maya Sari',
      action: 'update',
      resource_type: 'cpmk',
      resource_id: 5,
      resource_name: 'CPMK-01',
      description: 'Updated CPMK learning outcomes',
      created_at: '2025-01-25T09:15:00Z'
    }
  ]
}

const refreshActivities = async () => {
  try {
    const response = await dashboardAPI.getRecentActivities()
    recentActivities.value = response.data.data
  } catch (error) {
    console.error('Failed to refresh activities:', error)
  }
}

const getActivityColor = (action: string) => {
  const colors: Record<string, string> = {
    create: 'success',
    update: 'warning',
    delete: 'error',
    view: 'info'
  }
  return colors[action] || 'primary'
}

const getAssessmentTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    UTS: 'warning',
    UAS: 'error',
    Project: 'success',
    Lab: 'info',
    Quiz: 'primary'
  }
  return colors[type] || 'primary'
}

const getProgressColor = (progress: number) => {
  if (progress >= 80) return 'success'
  if (progress >= 60) return 'warning'
  return 'error'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('id-ID', {
    day: 'numeric',
    month: 'short'
  })
}

const formatTimeAgo = (dateString: string) => {
  const now = new Date()
  const date = new Date(dateString)
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
  
  if (diffInHours < 1) return 'Just now'
  if (diffInHours < 24) return `${diffInHours}h ago`
  return `${Math.floor(diffInHours / 24)}d ago`
}

// Lifecycle
onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
.stat-card {
  transition: transform 0.2s ease-in-out;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  opacity: 0.7;
}

.chart-container {
  height: 300px;
  position: relative;
}

.activity-timeline {
  max-height: 300px;
  overflow-y: auto;
}

.assessment-card {
  transition: transform 0.2s ease-in-out;
}

.assessment-card:hover {
  transform: translateY(-2px);
}
</style>
