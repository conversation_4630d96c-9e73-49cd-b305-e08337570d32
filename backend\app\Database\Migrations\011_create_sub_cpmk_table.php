<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateSubCpmkTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'code' => [
                'type'       => 'VARCHAR',
                'constraint' => 20,
                'null'       => false,
            ],
            'cpmk_id' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
                'null'       => false,
            ],
            'description' => [
                'type' => 'TEXT',
                'null' => false,
            ],
            'learning_indicator' => [
                'type' => 'TEXT',
                'null' => false,
            ],
            'week_coverage' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'JSON formatted array of week numbers',
            ],
            'weight_percentage' => [
                'type'       => 'DECIMAL',
                'constraint' => '5,2',
                'null'       => false,
            ],
            'is_active' => [
                'type'       => 'TINYINT',
                'constraint' => 1,
                'default'    => 1,
            ],
            'created_by' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
                'null'       => true,
            ],
            'created_at' => [
                'type'    => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
            ],
            'updated_at' => [
                'type'    => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
                'on_update' => 'CURRENT_TIMESTAMP',
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addUniqueKey(['code', 'cpmk_id']);
        $this->forge->addKey('cpmk_id');
        $this->forge->addKey('is_active');
        $this->forge->addKey('created_by');
        
        $this->forge->addForeignKey('cpmk_id', 'cpmk', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('created_by', 'users', 'id', 'SET NULL', 'CASCADE');
        
        $this->forge->createTable('sub_cpmk');
    }

    public function down()
    {
        $this->forge->dropTable('sub_cpmk');
    }
}
