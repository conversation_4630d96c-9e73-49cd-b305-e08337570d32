# =====================================================
# PHP CODEIGNITER 4 .GITIGNORE TEMPLATE
# =====================================================
# Optimized for CodeIgniter 4 backend development
# Version: CodeIgniter 4.3+, PHP 7.4+
# =====================================================

# =====================================================
# CODEIGNITER 4 SPECIFIC
# =====================================================

# CodeIgniter 4 System Files
/system/
!/system/index.html

# CodeIgniter 4 Application Cache
/writable/cache/*
!/writable/cache/index.html
!/writable/cache/.htaccess

# CodeIgniter 4 Logs
/writable/logs/*
!/writable/logs/index.html
!/writable/logs/.htaccess

# CodeIgniter 4 Session Files
/writable/session/*
!/writable/session/index.html
!/writable/session/.htaccess

# CodeIgniter 4 Uploads
/writable/uploads/*
!/writable/uploads/index.html
!/writable/uploads/.htaccess

# CodeIgniter 4 Debugbar
/writable/debugbar/*
!/writable/debugbar/.htaccess

# CodeIgniter 4 Framework Cache
/writable/framework/*
!/writable/framework/.htaccess

# Public Uploads (if using public directory for uploads)
/public/uploads/*
!/public/uploads/index.html
!/public/uploads/.htaccess

# User Guide (if included)
/user_guide/
/user_guide_src/

# =====================================================
# CODEIGNITER 4 ENVIRONMENT & CONFIG
# =====================================================

# Environment Files
.env
.env.local
.env.development
.env.testing
.env.production
.env.backup

# Database Configuration (if separate)
/app/Config/Database.local.php
/app/Config/Database.production.php
/app/Config/Database.development.php

# App Configuration (sensitive)
/app/Config/App.local.php
/app/Config/Email.local.php
/app/Config/Encryption.local.php
/app/Config/Security.local.php

# Custom Configuration Files
/app/Config/Custom.php
/app/Config/Secrets.php
/app/Config/ApiKeys.php

# =====================================================
# PHP SPECIFIC
# =====================================================

# Composer
/vendor/
composer.phar
composer.lock

# PHP Unit Testing
.phpunit.result.cache
.phpunit.cache
/tests/_output/*
/tests/_support/_generated
phpunit.xml
coverage/
.coverage

# PHP CS Fixer
.php_cs.cache
.php-cs-fixer.cache

# PHP Stan
.phpstan.cache

# Psalm
.psalm/

# =====================================================
# CODEIGNITER 4 TESTING
# =====================================================

# CodeIgniter 4 Testing
/tests/_output/*
/tests/_support/_generated/*
/tests/writable/*

# PHPUnit
phpunit.xml
.phpunit.result.cache
coverage.xml
coverage/

# Codeception
/tests/_output/
/tests/_support/_generated/

# =====================================================
# DATABASE FILES
# =====================================================

# SQLite
*.sqlite
*.sqlite3
*.db
*.db3

# Database Dumps
*.sql
!app/Database/Migrations/*.sql
!app/Database/Seeds/*.sql

# Database Backups
*.backup
*.dump

# =====================================================
# LOGS & CACHE
# =====================================================

# Application Logs
*.log
error.log
access.log
debug.log
ci.log

# Cache Files
*.cache
/cache/
/tmp/
/temp/

# Session Files
sess_*
ci_session*

# =====================================================
# UPLOADS & USER CONTENT
# =====================================================

# File Uploads
/public/uploads/*
!/public/uploads/.htaccess
!/public/uploads/index.html

# User Generated Content
/public/avatars/*
!/public/avatars/.htaccess
/public/documents/*
!/public/documents/.htaccess
/public/images/uploads/*
!/public/images/uploads/.htaccess

# Writable Uploads
/writable/uploads/*
!/writable/uploads/.htaccess
!/writable/uploads/index.html

# =====================================================
# ASSETS & BUILD
# =====================================================

# Compiled Assets
/public/assets/dist/
/public/css/compiled/
/public/js/compiled/
/public/build/

# Node.js (if using for asset compilation)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Webpack
webpack-stats.json
.webpack/

# Gulp
.gulp/

# Grunt
.grunt/

# =====================================================
# SECURITY & SECRETS
# =====================================================

# API Keys & Secrets
/app/Config/secrets.php
/app/Config/api-keys.php
*.pem
*.key
*.crt
*.csr
*.p12
*.pfx

# JWT Keys
/writable/jwt/
jwt-private.key
jwt-public.key

# OAuth Keys
oauth-private.key
oauth-public.key

# Encryption Keys
encryption.key
app.key

# =====================================================
# DEVELOPMENT TOOLS
# =====================================================

# CodeIgniter 4 Development Tools
/app/Views/errors/development/
spark

# Kint Debugger
kint.php

# Tracy Debugger
tracy/

# =====================================================
# IDE & EDITORS
# =====================================================

# PhpStorm
.idea/
*.iml
*.iws
*.ipr

# VS Code
.vscode/
*.code-workspace

# Sublime Text
*.sublime-project
*.sublime-workspace

# NetBeans
nbproject/
nbbuild/
nbdist/
.nb-gradle/

# Eclipse
.project
.buildpath
.settings/

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =====================================================
# OPERATING SYSTEM
# =====================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =====================================================
# DEPLOYMENT & DOCKER
# =====================================================

# Docker
.dockerignore
docker-compose.override.yml
docker-compose.local.yml
docker-compose.prod.yml
.docker/

# Kubernetes
*.kubeconfig
k8s-local/

# CI/CD
.github/workflows/local.yml
.gitlab-ci.local.yml
jenkins/local/

# Deployment Scripts
deploy.sh
deploy-local.sh
deploy-prod.sh

# =====================================================
# BACKUP & TEMPORARY FILES
# =====================================================

# Backup Files
*.bak
*.backup
*.old
*.orig
*.save
backup/
backups/

# Temporary Files
*.tmp
*.temp
tmp/
temp/

# Archive Files
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# =====================================================
# PROJECT SPECIFIC (RPS MANAGEMENT)
# =====================================================

# RPS Documents
/writable/rps-documents/*
!/writable/rps-documents/.htaccess
!/writable/rps-documents/index.html

# Generated Reports
/writable/reports/*
!/writable/reports/.htaccess
!/writable/reports/index.html

# Export Files
/writable/exports/*
!/writable/exports/.htaccess
!/writable/exports/index.html

# QR Codes
/public/qr-codes/*
!/public/qr-codes/.htaccess
!/public/qr-codes/index.html

# Email Templates (compiled)
/writable/email-templates/compiled/*
!/writable/email-templates/compiled/.htaccess

# Temporary Processing Files
/writable/processing/*
!/writable/processing/.htaccess
!/writable/processing/index.html

# =====================================================
# PERFORMANCE & OPTIMIZATION
# =====================================================

# CodeIgniter 4 Route Cache
/writable/cache/routes.php

# View Cache
/writable/cache/views/

# Model Cache
/writable/cache/models/

# Config Cache
/writable/cache/config/

# Opcache
opcache/
.opcache/

# =====================================================
# END OF CODEIGNITER 4 .GITIGNORE
# =====================================================
