<template>
  <div class="pa-6">
    <div class="text-center py-8">
      <v-icon size="64" color="grey-lighten-2">mdi-vector-link</v-icon>
      <p class="text-h6 text-medium-emphasis mt-4">CPMK-CPL Relations</p>
      <p class="text-body-2 text-medium-emphasis">
        Manage relationships between CPMK and CPL for {{ cpmk.code }}
      </p>
      <p class="text-caption text-medium-emphasis mt-2">
        This feature will be fully implemented in the next development phase
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { CPMK } from '@/types/auth'

interface Props {
  cpmk: CPMK
}

defineProps<Props>()
defineEmits<{
  close: []
}>()
</script>
