<template>
  <div class="pa-6">
    <!-- Header -->
    <div class="d-flex align-center justify-space-between mb-4">
      <div>
        <h3 class="text-h6 font-weight-bold">{{ course.name }} - References</h3>
        <p class="text-subtitle-2 text-medium-emphasis">
          Manage course references and learning materials
        </p>
      </div>
      
      <v-btn
        color="primary"
        @click="openCreateModal"
      >
        <v-icon start>mdi-plus</v-icon>
        Add Reference
      </v-btn>
    </div>

    <!-- References List -->
    <v-row>
      <v-col
        v-for="reference in references"
        :key="reference.id"
        cols="12"
      >
        <v-card variant="outlined" class="mb-3">
          <v-card-text>
            <div class="d-flex align-start justify-space-between">
              <div class="flex-grow-1">
                <div class="d-flex align-center mb-2">
                  <v-chip
                    :color="getReferenceTypeColor(reference.type)"
                    size="small"
                    variant="tonal"
                    class="mr-2"
                  >
                    {{ formatReferenceType(reference.type) }}
                  </v-chip>
                  
                  <v-chip
                    v-if="reference.year"
                    size="small"
                    variant="outlined"
                  >
                    {{ reference.year }}
                  </v-chip>
                </div>
                
                <h4 class="text-subtitle-1 font-weight-bold mb-1">
                  {{ reference.title }}
                </h4>
                
                <p v-if="reference.author" class="text-body-2 text-medium-emphasis mb-1">
                  <v-icon size="small" class="mr-1">mdi-account</v-icon>
                  {{ reference.author }}
                </p>
                
                <p v-if="reference.publisher" class="text-body-2 text-medium-emphasis mb-1">
                  <v-icon size="small" class="mr-1">mdi-domain</v-icon>
                  {{ reference.publisher }}
                </p>
                
                <p v-if="reference.isbn" class="text-body-2 text-medium-emphasis mb-1">
                  <v-icon size="small" class="mr-1">mdi-barcode</v-icon>
                  ISBN: {{ reference.isbn }}
                </p>
                
                <p v-if="reference.url" class="text-body-2 mb-1">
                  <v-icon size="small" class="mr-1">mdi-link</v-icon>
                  <a :href="reference.url" target="_blank" class="text-primary">
                    {{ reference.url }}
                  </a>
                </p>
                
                <p v-if="reference.notes" class="text-body-2 text-medium-emphasis">
                  <v-icon size="small" class="mr-1">mdi-note-text</v-icon>
                  {{ reference.notes }}
                </p>
              </div>
              
              <div class="d-flex flex-column gap-1 ml-4">
                <v-btn
                  icon
                  size="small"
                  variant="text"
                  @click="openEditModal(reference)"
                >
                  <v-icon size="small">mdi-pencil</v-icon>
                  <v-tooltip activator="parent">Edit</v-tooltip>
                </v-btn>
                
                <v-btn
                  icon
                  size="small"
                  variant="text"
                  color="error"
                  @click="openDeleteDialog(reference)"
                >
                  <v-icon size="small">mdi-delete</v-icon>
                  <v-tooltip activator="parent">Delete</v-tooltip>
                </v-btn>
              </div>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Empty State -->
    <div v-if="references.length === 0" class="text-center py-8">
      <v-icon size="64" color="grey-lighten-2">mdi-book-multiple</v-icon>
      <p class="text-h6 text-medium-emphasis mt-4">No references added yet</p>
      <v-btn
        color="primary"
        @click="openCreateModal"
        class="mt-2"
      >
        <v-icon start>mdi-plus</v-icon>
        Add First Reference
      </v-btn>
    </div>

    <!-- Reference Form Modal -->
    <FormModal
      v-model="showModal"
      :title="modalTitle"
      icon="mdi-book-multiple"
      :mode="modalMode"
      :loading="modalLoading"
      max-width="700"
      @submit="handleSubmit"
      @close="closeModal"
    >
      <v-row>
        <v-col cols="12">
          <v-select
            v-model="formData.type"
            :items="referenceTypeOptions"
            :rules="typeRules"
            label="Reference Type *"
            variant="outlined"
            prepend-inner-icon="mdi-tag"
            :disabled="modalLoading"
          />
        </v-col>
        
        <v-col cols="12">
          <v-text-field
            v-model="formData.title"
            :rules="titleRules"
            label="Title *"
            variant="outlined"
            prepend-inner-icon="mdi-book"
            :disabled="modalLoading"
          />
        </v-col>
        
        <v-col cols="12" md="6">
          <v-text-field
            v-model="formData.author"
            label="Author"
            variant="outlined"
            prepend-inner-icon="mdi-account"
            :disabled="modalLoading"
          />
        </v-col>
        
        <v-col cols="12" md="6">
          <v-text-field
            v-model="formData.publisher"
            label="Publisher"
            variant="outlined"
            prepend-inner-icon="mdi-domain"
            :disabled="modalLoading"
          />
        </v-col>
        
        <v-col cols="12" md="6">
          <v-text-field
            v-model="formData.year"
            label="Year"
            type="number"
            variant="outlined"
            prepend-inner-icon="mdi-calendar"
            :disabled="modalLoading"
          />
        </v-col>
        
        <v-col cols="12" md="6">
          <v-text-field
            v-model="formData.isbn"
            label="ISBN"
            variant="outlined"
            prepend-inner-icon="mdi-barcode"
            :disabled="modalLoading"
          />
        </v-col>
        
        <v-col cols="12">
          <v-text-field
            v-model="formData.url"
            label="URL"
            type="url"
            variant="outlined"
            prepend-inner-icon="mdi-link"
            :disabled="modalLoading"
          />
        </v-col>
        
        <v-col cols="12">
          <v-textarea
            v-model="formData.notes"
            label="Notes"
            variant="outlined"
            prepend-inner-icon="mdi-note-text"
            rows="3"
            :disabled="modalLoading"
          />
        </v-col>
      </v-row>
    </FormModal>

    <!-- Delete Confirmation Dialog -->
    <v-dialog v-model="showDeleteDialog" max-width="400">
      <v-card>
        <v-card-title class="text-h6">Confirm Delete</v-card-title>
        <v-card-text>
          Are you sure you want to delete this reference?
          This action cannot be undone.
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn @click="showDeleteDialog = false">Cancel</v-btn>
          <v-btn
            color="error"
            :loading="deleteLoading"
            @click="confirmDelete"
          >
            Delete
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Success/Error Snackbars -->
    <v-snackbar
      v-model="showSuccess"
      color="success"
      timeout="3000"
    >
      {{ successMessage }}
    </v-snackbar>

    <v-snackbar
      v-model="showError"
      color="error"
      timeout="5000"
    >
      {{ errorMessage }}
    </v-snackbar>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import FormModal from '@/components/common/FormModal.vue'
import { coursesAPI } from '@/services/api'
import type { Course } from '@/types/auth'

interface CourseReference {
  id: number
  course_id: number
  type: 'utama' | 'pendukung' | 'tambahan'
  title: string
  author?: string
  publisher?: string
  year?: number
  isbn?: string
  url?: string
  notes?: string
  created_at: string
  updated_at: string
}

interface Props {
  course: Course
}

const props = defineProps<Props>()
const emit = defineEmits<{
  close: []
}>()

// State
const loading = ref(false)
const modalLoading = ref(false)
const deleteLoading = ref(false)
const showModal = ref(false)
const showDeleteDialog = ref(false)
const showSuccess = ref(false)
const showError = ref(false)
const successMessage = ref('')
const errorMessage = ref('')

const references = ref<CourseReference[]>([])
const selectedReference = ref<CourseReference | null>(null)
const modalMode = ref<'create' | 'edit'>('create')

// Form data
const formData = reactive({
  type: 'utama' as 'utama' | 'pendukung' | 'tambahan',
  title: '',
  author: '',
  publisher: '',
  year: null as number | null,
  isbn: '',
  url: '',
  notes: ''
})

// Computed
const modalTitle = computed(() => 
  modalMode.value === 'create' ? 'Add Reference' : 'Edit Reference'
)

const referenceTypeOptions = [
  { title: 'Main Reference (Utama)', value: 'utama' },
  { title: 'Supporting Reference (Pendukung)', value: 'pendukung' },
  { title: 'Additional Reference (Tambahan)', value: 'tambahan' }
]

// Validation rules
const typeRules = [
  (v: string) => !!v || 'Reference type is required'
]

const titleRules = [
  (v: string) => !!v || 'Title is required',
  (v: string) => v.length >= 3 || 'Title must be at least 3 characters'
]

// Methods
const loadReferences = async () => {
  loading.value = true
  try {
    const response = await coursesAPI.getReferences(props.course.id)
    references.value = response.data.data
  } catch (error: any) {
    errorMessage.value = 'Failed to load references'
    showError.value = true
  } finally {
    loading.value = false
  }
}

const openCreateModal = () => {
  modalMode.value = 'create'
  resetForm()
  showModal.value = true
}

const openEditModal = (reference: CourseReference) => {
  modalMode.value = 'edit'
  selectedReference.value = reference
  populateForm(reference)
  showModal.value = true
}

const openDeleteDialog = (reference: CourseReference) => {
  selectedReference.value = reference
  showDeleteDialog.value = true
}

const resetForm = () => {
  Object.assign(formData, {
    type: 'utama',
    title: '',
    author: '',
    publisher: '',
    year: null,
    isbn: '',
    url: '',
    notes: ''
  })
}

const populateForm = (reference: CourseReference) => {
  Object.assign(formData, {
    type: reference.type,
    title: reference.title,
    author: reference.author || '',
    publisher: reference.publisher || '',
    year: reference.year,
    isbn: reference.isbn || '',
    url: reference.url || '',
    notes: reference.notes || ''
  })
}

const handleSubmit = async () => {
  modalLoading.value = true
  try {
    const data = {
      ...formData,
      course_id: props.course.id
    }
    
    if (modalMode.value === 'create') {
      await coursesAPI.createReference(props.course.id, data)
      successMessage.value = 'Reference added successfully!'
    } else if (selectedReference.value) {
      await coursesAPI.updateReference(props.course.id, selectedReference.value.id, data)
      successMessage.value = 'Reference updated successfully!'
    }
    
    showSuccess.value = true
    closeModal()
    await loadReferences()
  } catch (error: any) {
    errorMessage.value = error.response?.data?.message || 'Operation failed'
    showError.value = true
  } finally {
    modalLoading.value = false
  }
}

const confirmDelete = async () => {
  if (!selectedReference.value) return
  
  deleteLoading.value = true
  try {
    await coursesAPI.deleteReference(props.course.id, selectedReference.value.id)
    successMessage.value = 'Reference deleted successfully!'
    showSuccess.value = true
    showDeleteDialog.value = false
    await loadReferences()
  } catch (error: any) {
    errorMessage.value = error.response?.data?.message || 'Delete failed'
    showError.value = true
  } finally {
    deleteLoading.value = false
  }
}

const closeModal = () => {
  showModal.value = false
  selectedReference.value = null
  resetForm()
}

const getReferenceTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    utama: 'primary',
    pendukung: 'success',
    tambahan: 'info'
  }
  return colors[type] || 'primary'
}

const formatReferenceType = (type: string) => {
  const types: Record<string, string> = {
    utama: 'Main',
    pendukung: 'Supporting',
    tambahan: 'Additional'
  }
  return types[type] || type
}

// Lifecycle
onMounted(() => {
  loadReferences()
})
</script>

<style scoped>
.v-card {
  transition: transform 0.2s ease-in-out;
}

.v-card:hover {
  transform: translateY(-1px);
}
</style>
