import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/dashboard'
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/auth/LoginView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/dashboard',
      name: 'Dashboard',
      component: () => import('@/views/DashboardView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/users',
      name: 'Users',
      component: () => import('@/views/users/UsersView.vue'),
      meta: { requiresAuth: true, roles: ['admin'] }
    },
    {
      path: '/faculties',
      name: 'Faculties',
      component: () => import('@/views/faculties/FacultiesView.vue'),
      meta: { requiresAuth: true, roles: ['admin', 'dekan', 'wakil_dekan'] }
    },
    {
      path: '/study-programs',
      name: 'StudyPrograms',
      component: () => import('@/views/study-programs/StudyProgramsView.vue'),
      meta: { requiresAuth: true, roles: ['admin', 'dekan', 'wakil_dekan', 'kepala_prodi'] }
    },
    {
      path: '/courses',
      name: 'Courses',
      component: () => import('@/views/courses/CoursesView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/courses/:id',
      name: 'CourseDetail',
      component: () => import('@/views/courses/CourseDetailView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/cpl',
      name: 'CPL',
      component: () => import('@/views/cpl/CPLView.vue'),
      meta: { requiresAuth: true, roles: ['admin', 'dekan', 'wakil_dekan', 'kepala_prodi'] }
    },
    {
      path: '/cpmk',
      name: 'CPMK',
      component: () => import('@/views/cpmk/CPMKView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/assessments',
      name: 'Assessments',
      component: () => import('@/views/assessments/AssessmentsView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/reports',
      name: 'Reports',
      component: () => import('@/views/reports/ReportsView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/profile',
      name: 'Profile',
      component: () => import('@/views/auth/ProfileView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/NotFoundView.vue')
    }
  ]
})

// Navigation guards
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  
  // Check if route requires authentication
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/login')
    return
  }
  
  // Check if route requires guest (not authenticated)
  if (to.meta.requiresGuest && authStore.isAuthenticated) {
    next('/dashboard')
    return
  }
  
  // Check role-based access
  if (to.meta.roles && authStore.isAuthenticated) {
    const userRoles = authStore.user?.roles || []
    const requiredRoles = Array.isArray(to.meta.roles) ? to.meta.roles : []
    const hasRequiredRole = requiredRoles.some((role: string) =>
      userRoles.includes(role)
    )
    
    if (!hasRequiredRole) {
      next('/dashboard') // Redirect to dashboard if no permission
      return
    }
  }
  
  next()
})

export default router
