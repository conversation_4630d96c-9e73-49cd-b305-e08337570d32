@use "@styles/variables/_vuetify.scss" as vuetify;
@use "@layouts/styles/mixins" as layoutsMixins;
@use "@core/scss/base/mixins";

body .apexcharts-canvas {
  &line[stroke="transparent"] {
    display: "none";
  }

  .apexcharts-tooltip {
    @include mixins.elevation(3);

    border-color: rgba(var(--v-border-color), var(--v-border-opacity));
    background: rgb(var(--v-theme-surface));

    .apexcharts-tooltip-title {
      border-color: rgba(var(--v-border-color), var(--v-border-opacity));
      background: rgb(var(--v-theme-surface));
      font-weight: 600;
    }

    &.apexcharts-theme-light {
      color: rgba(var(--v-theme-on-background), var(--v-high-emphasis-opacity));
    }

    &.apexcharts-theme-dark {
      color: white;
    }

    .apexcharts-tooltip-series-group:first-of-type {
      padding-block-end: 0;
    }
  }

  .apexcharts-xaxistooltip {
    border-color: rgba(var(--v-border-color), var(--v-border-opacity));
    background: rgb(var(--v-theme-grey-50));
    color: rgba(var(--v-theme-on-background), var(--v-high-emphasis-opacity));

    &::after {
      border-block-end-color: rgb(var(--v-theme-grey-50));
    }

    &::before {
      border-block-end-color: rgba(var(--v-border-color), var(--v-border-opacity));
    }
  }

  .apexcharts-yaxistooltip {
    border-color: rgba(var(--v-border-color), var(--v-border-opacity));
    background: rgb(var(--v-theme-grey-50));

    &::after {
      border-inline-start-color: rgb(var(--v-theme-grey-50));
    }

    &::before {
      border-inline-start-color: rgba(var(--v-border-color), var(--v-border-opacity));
    }
  }

  .apexcharts-xaxistooltip-text,
  .apexcharts-yaxistooltip-text {
    color: rgba(var(--v-theme-on-background), var(--v-high-emphasis-opacity));
  }

  .apexcharts-yaxis .apexcharts-yaxis-texts-g .apexcharts-yaxis-label {
    @include layoutsMixins.rtl {
      text-anchor: start;
    }
  }

  .apexcharts-text,
  .apexcharts-tooltip-text,
  .apexcharts-datalabel-label,
  .apexcharts-datalabel,
  .apexcharts-xaxistooltip-text,
  .apexcharts-yaxistooltip-text,
  .apexcharts-legend-text {
    font-family: vuetify.$body-font-family !important;
  }

  .apexcharts-pie-label {
    fill: white;
    filter: none;
  }

  .apexcharts-marker {
    box-shadow: none;
  }

  .apexcharts-tooltip-marker {
    margin-inline-end: 0.625rem;

    @include layoutsMixins.rtl {
      margin-inline: 0 0.625rem !important;
    }
  }

  .apexcharts-legend-marker {
    margin-inline-end: 0.3875rem !important;

    @include layoutsMixins.rtl {
      margin-inline-end: 0.75rem !important;
    }
  }
}
