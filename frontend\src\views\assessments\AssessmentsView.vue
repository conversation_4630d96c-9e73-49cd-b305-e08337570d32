<template>
  <div>
    <!-- <PERSON> Header -->
    <v-row class="mb-6">
      <v-col>
        <h1 class="text-h4 font-weight-bold text-primary">Assessment System & Planning</h1>
        <p class="text-subtitle-1 text-medium-emphasis">
          Comprehensive assessment management with methods, planning, and analytics
        </p>
      </v-col>
    </v-row>

    <!-- Navigation Tabs -->
    <v-tabs
      v-model="activeTab"
      color="primary"
      class="mb-6"
    >
      <v-tab value="methods">
        <v-icon start>mdi-clipboard-list</v-icon>
        Assessment Methods
      </v-tab>

      <v-tab value="planning">
        <v-icon start>mdi-calendar-clock</v-icon>
        Assessment Planning
      </v-tab>

      <v-tab value="calendar">
        <v-icon start>mdi-calendar</v-icon>
        Assessment Calendar
      </v-tab>

      <v-tab value="analytics">
        <v-icon start>mdi-chart-line</v-icon>
        Analytics & Reports
      </v-tab>
    </v-tabs>

    <!-- Tab Content -->
    <v-tabs-window v-model="activeTab">
      <!-- Assessment Methods Tab -->
      <v-tabs-window-item value="methods">
        <AssessmentMethodsTab />
      </v-tabs-window-item>

      <!-- Assessment Planning Tab -->
      <v-tabs-window-item value="planning">
        <AssessmentPlanningTab />
      </v-tabs-window-item>

      <!-- Assessment Calendar Tab -->
      <v-tabs-window-item value="calendar">
        <AssessmentCalendarTab />
      </v-tabs-window-item>

      <!-- Analytics & Reports Tab -->
      <v-tabs-window-item value="analytics">
        <AssessmentAnalyticsTab />
      </v-tabs-window-item>
    </v-tabs-window>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import AssessmentMethodsTab from '@/components/assessments/AssessmentMethodsTab.vue'
import AssessmentPlanningTab from '@/components/assessments/AssessmentPlanningTab.vue'
import AssessmentCalendarTab from '@/components/assessments/AssessmentCalendarTab.vue'
import AssessmentAnalyticsTab from '@/components/assessments/AssessmentAnalyticsTab.vue'

// State
const activeTab = ref('methods')
</script>
