<template>
  <div v-if="course">
    <!-- Header -->
    <v-row class="mb-6">
      <v-col>
        <div class="d-flex align-center mb-2">
          <v-btn
            icon
            variant="text"
            @click="$router.back()"
            class="mr-2"
          >
            <v-icon>mdi-arrow-left</v-icon>
          </v-btn>

          <div>
            <h1 class="text-h4 font-weight-bold text-primary">{{ course.name }}</h1>
            <p class="text-subtitle-1 text-medium-emphasis">
              {{ course.code }} • {{ course.credits }} SKS • Semester {{ course.semester }}
            </p>
          </div>
        </div>

        <div class="d-flex align-center gap-2">
          <v-chip
            :color="course.course_type === 'wajib' ? 'warning' : 'info'"
            variant="tonal"
          >
            {{ course.course_type === 'wajib' ? 'Mandatory' : 'Elective' }}
          </v-chip>

          <v-chip
            :color="course.is_active ? 'success' : 'error'"
            variant="tonal"
          >
            {{ course.is_active ? 'Active' : 'Inactive' }}
          </v-chip>
        </div>
      </v-col>
    </v-row>

    <!-- Course Information -->
    <v-row class="mb-6">
      <v-col cols="12" md="8">
        <v-card class="mb-4">
          <v-card-title class="text-h6 font-weight-bold">
            <v-icon class="mr-2">mdi-information</v-icon>
            Course Information
          </v-card-title>

          <v-card-text>
            <v-row>
              <v-col cols="12" md="6">
                <div class="mb-4">
                  <h4 class="text-subtitle-2 font-weight-bold mb-1">Course Code</h4>
                  <p class="text-body-1">{{ course.code }}</p>
                </div>

                <div class="mb-4">
                  <h4 class="text-subtitle-2 font-weight-bold mb-1">Credits (SKS)</h4>
                  <p class="text-body-1">{{ course.credits }}</p>
                </div>

                <div class="mb-4">
                  <h4 class="text-subtitle-2 font-weight-bold mb-1">Semester</h4>
                  <p class="text-body-1">{{ course.semester }}</p>
                </div>
              </v-col>

              <v-col cols="12" md="6">
                <div class="mb-4">
                  <h4 class="text-subtitle-2 font-weight-bold mb-1">Course Type</h4>
                  <p class="text-body-1">{{ course.course_type === 'wajib' ? 'Mandatory' : 'Elective' }}</p>
                </div>

                <div class="mb-4">
                  <h4 class="text-subtitle-2 font-weight-bold mb-1">Coordinator</h4>
                  <p class="text-body-1">{{ course.coordinator_name || 'Not assigned' }}</p>
                </div>

                <div class="mb-4">
                  <h4 class="text-subtitle-2 font-weight-bold mb-1">Status</h4>
                  <p class="text-body-1">{{ course.is_active ? 'Active' : 'Inactive' }}</p>
                </div>
              </v-col>
            </v-row>

            <div v-if="course.description" class="mb-4">
              <h4 class="text-subtitle-2 font-weight-bold mb-1">Description</h4>
              <p class="text-body-1">{{ course.description }}</p>
            </div>

            <div v-if="course.learning_objectives" class="mb-4">
              <h4 class="text-subtitle-2 font-weight-bold mb-1">Learning Objectives</h4>
              <p class="text-body-1">{{ course.learning_objectives }}</p>
            </div>

            <div v-if="prerequisites.length > 0">
              <h4 class="text-subtitle-2 font-weight-bold mb-2">Prerequisites</h4>
              <div class="d-flex flex-wrap gap-2">
                <v-chip
                  v-for="prereq in prerequisites"
                  :key="prereq.id"
                  size="small"
                  variant="outlined"
                  @click="$router.push(`/courses/${prereq.id}`)"
                  class="cursor-pointer"
                >
                  {{ prereq.code }} - {{ prereq.name }}
                </v-chip>
              </div>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" md="4">
        <v-card>
          <v-card-title class="text-h6 font-weight-bold">
            <v-icon class="mr-2">mdi-lightning-bolt</v-icon>
            Quick Actions
          </v-card-title>

          <v-card-text>
            <v-list>
              <v-list-item @click="$router.push(`/courses/${course.id}/edit`)">
                <v-list-item-prepend>
                  <v-avatar color="primary" size="40">
                    <v-icon color="white">mdi-pencil</v-icon>
                  </v-avatar>
                </v-list-item-prepend>
                <v-list-item-title>Edit Course</v-list-item-title>
                <v-list-item-subtitle>Update course information</v-list-item-subtitle>
              </v-list-item>

              <v-list-item @click="showReferencesModal = true">
                <v-list-item-prepend>
                  <v-avatar color="success" size="40">
                    <v-icon color="white">mdi-book-multiple</v-icon>
                  </v-avatar>
                </v-list-item-prepend>
                <v-list-item-title>Manage References</v-list-item-title>
                <v-list-item-subtitle>{{ references.length }} references</v-list-item-subtitle>
              </v-list-item>

              <v-list-item @click="showTopicsModal = true">
                <v-list-item-prepend>
                  <v-avatar color="warning" size="40">
                    <v-icon color="white">mdi-format-list-numbered</v-icon>
                  </v-avatar>
                </v-list-item-prepend>
                <v-list-item-title>Manage Topics</v-list-item-title>
                <v-list-item-subtitle>{{ topics.length }} topics planned</v-list-item-subtitle>
              </v-list-item>

              <v-list-item @click="$router.push(`/courses/${course.id}/cpmk`)">
                <v-list-item-prepend>
                  <v-avatar color="info" size="40">
                    <v-icon color="white">mdi-bullseye-arrow</v-icon>
                  </v-avatar>
                </v-list-item-prepend>
                <v-list-item-title>Manage CPMK</v-list-item-title>
                <v-list-item-subtitle>Learning outcomes</v-list-item-subtitle>
              </v-list-item>
            </v-list>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Tabs for detailed content -->
    <v-card>
      <v-tabs v-model="activeTab" bg-color="primary" dark>
        <v-tab value="references">
          <v-icon start>mdi-book-multiple</v-icon>
          References ({{ references.length }})
        </v-tab>

        <v-tab value="topics">
          <v-icon start>mdi-format-list-numbered</v-icon>
          Topics ({{ topics.length }})
        </v-tab>

        <v-tab value="cpmk">
          <v-icon start>mdi-bullseye-arrow</v-icon>
          CPMK
        </v-tab>
      </v-tabs>

      <v-card-text>
        <v-tabs-window v-model="activeTab">
          <v-tabs-window-item value="references">
            <CourseReferences
              :course="course"
              @updated="loadReferences"
            />
          </v-tabs-window-item>

          <v-tabs-window-item value="topics">
            <CourseTopics
              :course="course"
              @updated="loadTopics"
            />
          </v-tabs-window-item>

          <v-tabs-window-item value="cpmk">
            <div class="text-center py-8">
              <v-icon size="64" color="grey-lighten-2">mdi-bullseye-arrow</v-icon>
              <p class="text-h6 text-medium-emphasis mt-4">CPMK Management</p>
              <p class="text-body-2 text-medium-emphasis">
                Course Learning Outcomes management will be available in the next phase
              </p>
            </div>
          </v-tabs-window-item>
        </v-tabs-window>
      </v-card-text>
    </v-card>

    <!-- Course References Modal -->
    <v-dialog v-model="showReferencesModal" max-width="1000" scrollable>
      <v-card>
        <v-card-title class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <v-icon class="mr-2">mdi-book-multiple</v-icon>
            <span class="text-h6 font-weight-bold">Course References</span>
          </div>
          <v-btn icon variant="text" @click="showReferencesModal = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>

        <v-divider />

        <v-card-text class="pa-0">
          <CourseReferences
            :course="course"
            @close="showReferencesModal = false"
            @updated="loadReferences"
          />
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- Course Topics Modal -->
    <v-dialog v-model="showTopicsModal" max-width="1200" scrollable>
      <v-card>
        <v-card-title class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <v-icon class="mr-2">mdi-format-list-numbered</v-icon>
            <span class="text-h6 font-weight-bold">Course Topics</span>
          </div>
          <v-btn icon variant="text" @click="showTopicsModal = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>

        <v-divider />

        <v-card-text class="pa-0">
          <CourseTopics
            :course="course"
            @close="showTopicsModal = false"
            @updated="loadTopics"
          />
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>

  <!-- Loading State -->
  <div v-else class="d-flex align-center justify-center" style="height: 400px;">
    <v-progress-circular indeterminate color="primary" size="64" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import CourseReferences from '@/components/courses/CourseReferences.vue'
import CourseTopics from '@/components/courses/CourseTopics.vue'
import { coursesAPI } from '@/services/api'
import type { Course, CourseReference, CourseTopic } from '@/types/auth'

const route = useRoute()

// State
const loading = ref(false)
const course = ref<Course | null>(null)
const references = ref<CourseReference[]>([])
const topics = ref<CourseTopic[]>([])
const prerequisites = ref<Course[]>([])
const activeTab = ref('references')
const showReferencesModal = ref(false)
const showTopicsModal = ref(false)

// Methods
const loadCourse = async () => {
  const courseId = parseInt(route.params.id as string)
  if (!courseId) return

  loading.value = true
  try {
    const response = await coursesAPI.getById(courseId)
    course.value = response.data.data

    // Load related data
    await Promise.all([
      loadReferences(),
      loadTopics(),
      loadPrerequisites()
    ])
  } catch (error) {
    console.error('Failed to load course:', error)
  } finally {
    loading.value = false
  }
}

const loadReferences = async () => {
  if (!course.value) return

  try {
    const response = await coursesAPI.getReferences(course.value.id)
    references.value = response.data.data
  } catch (error) {
    console.error('Failed to load references:', error)
  }
}

const loadTopics = async () => {
  if (!course.value) return

  try {
    const response = await coursesAPI.getTopics(course.value.id)
    topics.value = response.data.data
  } catch (error) {
    console.error('Failed to load topics:', error)
  }
}

const loadPrerequisites = async () => {
  if (!course.value?.prerequisite_courses?.length) return

  try {
    // Load prerequisite course details
    const prereqPromises = course.value.prerequisite_courses.map(id =>
      coursesAPI.getById(id)
    )
    const responses = await Promise.all(prereqPromises)
    prerequisites.value = responses.map(response => response.data.data)
  } catch (error) {
    console.error('Failed to load prerequisites:', error)
  }
}

// Lifecycle
onMounted(() => {
  loadCourse()
})
</script>

<style scoped>
.cursor-pointer {
  cursor: pointer;
}

.v-chip.cursor-pointer:hover {
  opacity: 0.8;
}
</style>
