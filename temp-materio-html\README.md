<p align="center">
   <a href="https://themeselection.com/item/materio-dashboard-free-bootstrap/" target="_blank">
      <img src="https://cdn.themeselection.com/ts-assets/materio/logo/logo.png" alt="materio-logo" width="40px" height="auto">
   </a>
</p>

<h1 align="center">
   <a href="https://themeselection.com/item/materio-dashboard-free-bootstrap/" target="_blank" align="center">
      Materio - Free Bootstrap 5 HTML Admin Template
   </a>
</h1>

<p align="center">Most Powerful & Comprehensive Free Bootstrap 5 HTML Admin Dashboard Template built for developers!</p>

![GitHub](https://img.shields.io/github/license/themeselection/materio-bootstrap-html-admin-template-free) ![GitHub release (latest by date)](https://img.shields.io/github/v/release/themeselection/materio-bootstrap-html-admin-template-free) ![GitHub issues](https://img.shields.io/github/issues/themeselection/materio-bootstrap-html-admin-template-free) ![GitHub closed issues](https://img.shields.io/github/issues-closed/themeselection/materio-bootstrap-html-admin-template-free) ![Twitter Follow](https://img.shields.io/twitter/follow/Theme_Selection?style=social)

[![Materio - Bootstrap 5 HTML Admin Template Demo Screenshot](https://cdn.themeselection.com/ts-assets/materio/materio-bootstrap-html-admin-template-free/banner/banner.png)](https://themeselection.com/item/materio-dashboard-free-bootstrap/)

## Introduction 🚀

If you’re a developer looking for the most Powerful & comprehensive [**Free Bootstrap 5 HTML Admin Template**](https://themeselection.com/item/materio-dashboard-free-bootstrap/) built for developers, rich with features, and highly customizable look no further than Materio. We’ve followed the highest industry standards to bring you the very best admin template that is not only fast and easy to use but highly scalable. Offering ultimate convenience and flexibility, you’ll be able to build whatever application you want with very little hassle.

Build premium quality applications with ease. Use our innovative [bootstrap admin template](https://themeselection.com/item/category/bootstrap-admin-templates/) to create eye-catching, high-quality WebApps. Your apps will be completely responsive, ensuring they’ll look stunning and function flawlessly on desktops, tablets, and mobile devices.

[View Demo](https://demos.themeselection.com/materio-bootstrap-html-admin-template-free/html/index.html)

## Installation ⚒️

Please [visit](https://demos.themeselection.com/materio-bootstrap-html-admin-template/documentation/installation-build.html) our docs for installation guide.

## What's Included 📦

- Dashboard
  - Analytics
- Layouts
  - Without menu
  - Without Navbar
  - Container
  - Fluid
  - Blank
- Pages
  - Account Settings
  - Login
  - Register
  - Forgot Password
  - Error
  - Under Maintenance
- Cards
- User Interface
  - **All Bootstrap Components**
- Extended UI
  - Perfect Scrollbar
  - Text Divider
- Remix Icons (RI)
- Form Elements
  - Basic Inputs
  - Input Groups
- Form Layout
  - Vertical Form
  - Horizontal Form
- Tables

## What's in Premium Version 💎

| Materio Free Version                                                                     | Materio Premium Version                                                                                                              |
| ---------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------ |
| [Demo](https://themeselection.com/demo/materio-bootstrap-html-admin-template-free/html/) | [Demo](https://themeselection.com/demo/materio-bootstrap-html-admin-template/html/vertical-menu-template/)                           |
| [Download](https://themeselection.com/item/materio-dashboard-free-bootstrap/)            | [Purchase](https://themeselection.com/item/materio-dashboard-pro-bootstrap/)                                                         |
| Single vertical Menu                                                                     | Vertical Menu + Horizontal Menu                                                                                                      |
| Simple Light Style                                                                       | Light & Dark Style                                                                                                                   |
| Default Theme                                                                            | Default, Semi Dark & Bordered Themes                                                                                                 |
| Fixed Layout(Menu)                                                                       | Fixed & Static Layout(Menu)                                                                                                          |
| -                                                                                        | Template Customizer                                                                                                                  |
| 1 Simple Dashboard                                                                       | 6 Niche Dashboards                                                                                                                   |
| -                                                                                        | Multiple Ready Applications like Calendar, Invoice, Users List, Users View, Roles and Permission, eCommerce, Academy, Logistics etc. |
| Simple From Elements                                                                     | Advance form elements, validation & form wizard                                                                                      |
| Basic Cards                                                                              | Basic, Advance, Statistics, Analytics, Gamification and Actions Cards                                                                |
| Basic User Interface(Components)                                                         | Advance and Custom User Interfaces(Components)                                                                                       |
| Two Extended Components                                                                  | Twelve Ready to use Extended Components                                                                                              |
| -                                                                                        | Quick Search - Quickly navigate between pages (w/ hotkey support)                                                                    |
| Basic Pages                                                                              | Authentication Pages in 2 Variants + Ready-to-use pages like User Profile, Account Settings, FAQ, Help Center, Pricing, Misc, etc.   |
| -                                                                                        | 3D Characters + Illustrations                                                                                                        |
| Basic tables                                                                             | Advanced tables                                                                                                                      |
| -                                                                                        | Quick customization using theme config file                                                                                          |
| -                                                                                        | Leaflet Maps                                                                                                                         |
| 1 Chart Library                                                                          | 2 Chart Libraries                                                                                                                    |
| -                                                                                        | Multiple Navbar & Menu Options                                                                                                       |
| -                                                                                        | Starter-kit                                                                                                                          |
| -                                                                                        | Internationalization support                                                                                                         |
| -                                                                                        | RTL Support                                                                                                                          |
| Regular Support                                                                          | Priority Support                                                                                                                     |
| Detailed Documentation                                                                   | Detailed Documentation                                                                                                               |

## Documentation 📜

<!-- If you have docs in wiki then use below line -->

Check GitHub [Wiki](https://github.com/themeselection/materio-bootstrap-html-admin-template-free/wiki) of this repository.

Check out our live [Documentation](https://demos.themeselection.com/materio-bootstrap-html-admin-template/documentation/)

## Browser Support 🖥️

![chrome](https://github.com/nuxt/nuxt/assets/47495003/bbb6d7b0-2db6-4af4-abdc-a73de71dd287)
&nbsp;&nbsp;![firefox](https://github.com/nuxt/nuxt/assets/47495003/bca1f2d0-d597-453b-8525-5c94e36bfc33)
&nbsp;&nbsp;![safari](https://github.com/nuxt/nuxt/assets/47495003/8ecbb395-78fb-40fb-bb59-7301bf8a7e5d)
&nbsp;&nbsp;![Microsoft Edge](https://github.com/nuxt/nuxt/assets/47495003/f945821b-0cbd-464d-8103-824d4d5c4e9a)

\*_It also supports other browser which implemented latest CSS standards_

## Contributing 🦸

Contribution are always welcome and recommended! Here is how:

- Fork the repository ([here is the guide](https://docs.github.com/en/get-started/quickstart/fork-a-repo)).
- Clone to your machine `git clone https://github.com/themeselection/materio-bootstrap-html-admin-template-free` Make your changes
- Create a pull request

### Contribution Requirements 🧰

- When you contribute, you agree to give a non-exclusive license to ThemeSelection to use that contribution in any context as we (ThemeSelection) see appropriate.
- If you use content provided by another party, it must be appropriately licensed using an open source license.
- Contributions are only accepted through Github pull requests.
- Finally, contributed code must work in all supported browsers (see above for browser support).

## Changelog 📆

Please refer to the [CHANGELOG](CHANGELOG.md) file. We will add a detailed release notes to each new release.

## Support 🧑🏻‍💻

For free products, enjoy community support via GitHub issues. Upgrade to Premium for dedicated support from our expert team.

## License &copy;

- Copyright © [ThemeSelection](https://themeselection.com/)
- Licensed under [MIT](LICENSE)
- All our free items are Open Source and licensed under MIT. You can use our free items for personal as well as commercial purposes. We just need an attribution from your end. Copy the below link and paste it at the footer of your web application or project.

  ```html
  <a href="https://themeselection.com/">ThemeSelection</a>
  ```

## Also Available In

<p>
   <!-- Figma -->
   <a href="https://themeselection.com/item/materio-figma-admin-dashboard-ui-kit/" target="_blank"><picture><source width="auto" height="74px" media="(prefers-color-scheme: dark)" srcset="https://github.com/microsoft/vscode/assets/47495003/0318a6c8-4f9b-4cf6-af5e-d357f909ea2b"><source width="auto" height="74px" media="(prefers-color-scheme: light)" srcset="https://github.com/microsoft/vscode/assets/47495003/47f21dfe-c1fc-4a7d-859e-4d98f8cdded1"><img width="auto" height="74px" alt="html" src="https://github.com/microsoft/vscode/assets/47495003/47f21dfe-c1fc-4a7d-859e-4d98f8cdded1"></picture></img></a>&nbsp;&nbsp;
   <!-- HTML -->
   <a href="https://themeselection.com/item/materio-bootstrap-html-admin-template/" target="_blank"><picture><source width="auto" height="74px" media="(prefers-color-scheme: dark)" srcset="https://github.com/microsoft/vscode/assets/47495003/5fe77c46-2e4c-475a-8dec-e30e2badddee"><source width="auto" height="74px" media="(prefers-color-scheme: light)" srcset="https://github.com/microsoft/vscode/assets/47495003/3f5decd8-cd99-4ed3-aa76-528ca061385b"><img width="auto" height="74px" alt="html" src="https://github.com/microsoft/vscode/assets/47495003/3f5decd8-cd99-4ed3-aa76-528ca061385b"></picture></img></a>&nbsp;&nbsp;
   <!-- HTML + Laravel -->
   <a href="https://themeselection.com/item/materio-bootstrap-laravel-admin-template/" target="_blank"><picture><source width="auto" height="74px" media="(prefers-color-scheme: dark)" srcset="https://github.com/microsoft/vscode/assets/47495003/efe420e4-9863-41b7-9eda-47ea94f21a62"><source width="auto" height="74px" media="(prefers-color-scheme: light)" srcset="https://github.com/microsoft/vscode/assets/47495003/be3b86e0-4d5e-4736-bf89-4267fb4d6710"><img width="auto" height="74px" alt="html_laravel" src="https://github.com/microsoft/vscode/assets/47495003/be3b86e0-4d5e-4736-bf89-4267fb4d6710"></picture></img></a>&nbsp;&nbsp;
   <!-- HTML + Django -->
   <a href="https://themeselection.com/item/materio-bootstrap-django-admin-template/" target="_blank"><picture><source width="auto" height="74px" media="(prefers-color-scheme: dark)" srcset="https://github.com/microsoft/vscode/assets/47495003/3c87d33b-1223-4aaa-a652-388dcb714c98"><source width="auto" height="74px" media="(prefers-color-scheme: light)" srcset="https://github.com/microsoft/vscode/assets/47495003/51db1947-eac1-466f-87fd-5a209010fe9c"><img width="auto" height="74px" alt="html_django" src="https://github.com/microsoft/vscode/assets/47495003/51db1947-eac1-466f-87fd-5a209010fe9c"></picture></img></a>&nbsp;&nbsp;
   <!-- .Net Core -->
   <a href="https://themeselection.com/item/materio-aspnet-core-mvc-admin-template/" target="_blank"><picture><source width="auto" height="74px" media="(prefers-color-scheme: dark)" srcset="https://github.com/microsoft/vscode/assets/47495003/6327fd7b-9c54-4189-a852-28551ad0e002"><source width="auto" height="74px" media="(prefers-color-scheme: light)" srcset="https://github.com/microsoft/vscode/assets/47495003/9856e9d5-021f-4573-902a-702e80dd0102"><img width="auto" height="74px" alt="net_core" src="https://github.com/microsoft/vscode/assets/47495003/9856e9d5-021f-4573-902a-702e80dd0102"></picture></img></a>&nbsp;&nbsp;
   <!-- NextJS -->
   <a href="https://themeselection.com/item/materio-mui-react-nextjs-admin-template/" target="_blank"><picture><source width="auto" height="74px" media="(prefers-color-scheme: dark)" srcset="https://github.com/microsoft/vscode/assets/47495003/66344629-6d21-4f92-9078-f479b39cb34e"><source width="auto" height="74px" media="(prefers-color-scheme: light)" srcset="https://github.com/microsoft/vscode/assets/47495003/e1daf4e1-3fa5-4a44-969a-6143ddd67310"><img width="auto" height="74px" alt="next.js" src="https://github.com/microsoft/vscode/assets/47495003/e1daf4e1-3fa5-4a44-969a-6143ddd67310"></picture></img></a>&nbsp;&nbsp;
   <!-- React -->
   <a href="https://themeselection.com/item/materio-mui-react-nextjs-admin-template/" target="_blank"><picture><source width="auto" height="74px" media="(prefers-color-scheme: dark)" srcset="https://github.com/microsoft/vscode/assets/47495003/3877046e-c652-4b3d-99e9-2e134da1d6cf"><source width="auto" height="74px" media="(prefers-color-scheme: light)" srcset="https://github.com/microsoft/vscode/assets/47495003/8c8c940e-d8f9-4213-a7f7-f8bc4968f169"><img width="auto" height="74px" alt="react" src="https://github.com/microsoft/vscode/assets/47495003/8c8c940e-d8f9-4213-a7f7-f8bc4968f169"></picture></img></a>&nbsp;&nbsp;
   <!-- Vue -->
   <a href="https://themeselection.com/item/materio-vuetify-vuejs-admin-template/" target="_blank"><picture><source width="auto" height="74px" media="(prefers-color-scheme: dark)" srcset="https://github.com/microsoft/vscode/assets/47495003/881bbbb8-d1c9-421c-9bce-4ea43dfa9e6e"><source width="auto" height="74px" media="(prefers-color-scheme: light)" srcset="https://github.com/microsoft/vscode/assets/47495003/b02d6473-0345-42c2-be58-e648806104fa"><img width="auto" height="74px" alt="vue" src="https://github.com/microsoft/vscode/assets/47495003/b02d6473-0345-42c2-be58-e648806104fa"></picture></img></a>&nbsp;&nbsp;
   <!-- Vue + Laravel -->
   <a href="https://themeselection.com/item/materio-vuetify-vuejs-laravel-admin-template/" target="_blank"><picture><source width="auto" height="74px" media="(prefers-color-scheme: dark)" srcset="https://github.com/microsoft/vscode/assets/47495003/20b6428e-3fa5-4f80-a389-9e4cd732c2de"><source width="auto" height="74px" media="(prefers-color-scheme: light)" srcset="https://github.com/microsoft/vscode/assets/47495003/3008d3eb-7b5b-4d9c-8563-837744a901da"><img width="auto" height="74px" alt="vue_laravel" src="https://github.com/microsoft/vscode/assets/47495003/3008d3eb-7b5b-4d9c-8563-837744a901da"></picture></img></a>&nbsp;&nbsp;
</p>

<!-- Add other pro variants here. You can get the logo URL from here: https://icones.js.org/collection/logos -->

## Looking For Premium Admin Templates ?? 👀

**[ThemeSelection](https://themeselection.com/)** provides Selected high quality, modern design, professional and easy-to-use **Fully Coded Dashboard Templates & UI Kits** to create your applications faster!

- [Bootstrap Admin Templates](https://themeselection.com/item/category/bootstrap-admin-templates/)
- [VueJS Admin Templates](https://themeselection.com/item/category/vuejs-admin-templates/)
- [Laravel Admin Templates](https://themeselection.com/item/category/laravel-admin-templates/)
- [Django Admin Templates](https://themeselection.com/item/category/django-admin-template/)
- [React (NextJS) Admin Templates](https://themeselection.com/item/category/next-js-admin-template/)
- [ASP.Net Core Admin Templates](https://themeselection.com/item/category/asp-net-dashboard/)
- [Free UI Kits](https://themeselection.com/item/category/free-ui-kits/)

If you want to [Free Admin Templates](https://themeselection.com/item/category/free-admin-templates/) like Materio then do visit [ThemeSelection](https://themeselection.com/).

## Useful Links 🎁

- [Vue CheatSheet](https://vue-cheatsheet.themeselection.com/)
- [Freebies](https://themeselection.com/item/category/freebies/)
- [Free Admin Templates](https://themeselection.com/item/category/free-admin-templates/)
- [Bootstrap 5 CheatSheet](https://bootstrap-cheatsheet.themeselection.com/)
- [FlyonUI](https://flyonui.com/)
- [JetShip](https://demos.themeselection.com/jetship-laravel-starter-kit/)

## Social Media :earth_africa:

- [x](https://x.com/Theme_Selection)
- [Facebook](https://www.facebook.com/ThemeSelections/)
- [Pinterest](https://www.pinterest.com/themeselection/)
- [Instagram](https://www.instagram.com/themeselection/)
- [Discord](https://discord.com/invite/kBHkY7DekX)
- [YouTube](https://www.youtube.com/channel/UCuryo5s0CW4aP83itLjIdZg)
