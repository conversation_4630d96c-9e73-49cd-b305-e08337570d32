<template>
  <div>
    <!-- <PERSON>er -->
    <v-row class="mb-6">
      <v-col>
        <h1 class="text-h4 font-weight-bold text-primary">CPMK Management</h1>
        <p class="text-subtitle-1 text-medium-emphasis">
          Manage Course Learning Outcomes (Capaian Pembelajaran Mata Kuliah)
        </p>
      </v-col>
    </v-row>

    <!-- Statistics Cards -->
    <v-row class="mb-6">
      <v-col cols="12" sm="6" md="3">
        <v-card color="primary" variant="tonal">
          <v-card-text>
            <div class="d-flex align-center justify-space-between">
              <div>
                <p class="text-subtitle-2 text-medium-emphasis mb-1">Total CPMK</p>
                <h2 class="text-h4 font-weight-bold">{{ totalCPMK }}</h2>
              </div>
              <v-icon size="48" color="primary">mdi-bullseye-arrow</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card color="success" variant="tonal">
          <v-card-text>
            <div class="d-flex align-center justify-space-between">
              <div>
                <p class="text-subtitle-2 text-medium-emphasis mb-1">Active CPMK</p>
                <h2 class="text-h4 font-weight-bold">{{ activeCPMK }}</h2>
              </div>
              <v-icon size="48" color="success">mdi-check-circle</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card color="warning" variant="tonal">
          <v-card-text>
            <div class="d-flex align-center justify-space-between">
              <div>
                <p class="text-subtitle-2 text-medium-emphasis mb-1">Avg Weight</p>
                <h2 class="text-h4 font-weight-bold">{{ averageWeight }}%</h2>
              </div>
              <v-icon size="48" color="warning">mdi-scale-balance</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card color="info" variant="tonal">
          <v-card-text>
            <div class="d-flex align-center justify-space-between">
              <div>
                <p class="text-subtitle-2 text-medium-emphasis mb-1">CPL Mapped</p>
                <h2 class="text-h4 font-weight-bold">{{ mappedCPMK }}</h2>
              </div>
              <v-icon size="48" color="info">mdi-vector-link</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Course Filter -->
    <v-row class="mb-4">
      <v-col cols="12" md="6">
        <v-select
          v-model="selectedCourse"
          :items="courseOptions"
          label="Select Course"
          variant="outlined"
          prepend-inner-icon="mdi-book-open-page-variant"
          clearable
          @update:model-value="handleCourseChange"
        />
      </v-col>

      <v-col cols="12" md="6" class="d-flex align-center gap-2">
        <v-btn
          color="secondary"
          variant="outlined"
          @click="showWeightValidation = true"
          :disabled="!selectedCourse"
        >
          <v-icon start>mdi-scale-balance</v-icon>
          Validate Weights
        </v-btn>

        <v-btn
          color="info"
          variant="outlined"
          @click="showCPLMapping = true"
          :disabled="!selectedCourse"
        >
          <v-icon start>mdi-vector-link</v-icon>
          CPL Mapping
        </v-btn>
      </v-col>
    </v-row>

    <!-- Data Table -->
    <DataTable
      title="Course Learning Outcomes (CPMK)"
      icon="mdi-bullseye-arrow"
      item-name="CPMK"
      :headers="tableHeaders"
      :items="cpmks"
      :loading="loading"
      :total-items="totalItems"
      :filters="tableFilters"
      @add="openCreateModal"
      @edit="openEditModal"
      @delete="openDeleteDialog"
      @view="openViewModal"
      @refresh="loadCPMKs"
      @search="handleSearch"
      @filter="handleFilter"
      @update:options="handleTableOptions"
    >
      <!-- Custom slots for specific columns -->
      <template #item.code="{ item }">
        <v-chip
          color="primary"
          variant="tonal"
          size="small"
          class="font-weight-bold"
        >
          {{ item.code }}
        </v-chip>
      </template>

      <template #item.cognitive_level="{ item }">
        <v-chip
          :color="getCognitiveLevelColor(item.cognitive_level)"
          variant="tonal"
          size="small"
        >
          {{ item.cognitive_level }}
        </v-chip>
      </template>

      <template #item.weight_percentage="{ item }">
        <div class="d-flex align-center">
          <v-progress-linear
            :model-value="item.weight_percentage"
            :color="getWeightColor(item.weight_percentage)"
            height="6"
            rounded
            class="mr-2"
            style="min-width: 60px;"
          />
          <span class="text-caption font-weight-medium">{{ item.weight_percentage }}%</span>
        </div>
      </template>

      <template #item.course="{ item }">
        <div v-if="item.course_name">
          <div class="font-weight-medium">{{ item.course_name }}</div>
          <div class="text-caption text-medium-emphasis">{{ item.course_code }}</div>
        </div>
        <span v-else class="text-medium-emphasis">-</span>
      </template>

      <template #item.description="{ item }">
        <div class="text-truncate" style="max-width: 300px;">
          {{ item.description }}
        </div>
      </template>

      <template #item.actions="{ item }">
        <div class="d-flex align-center gap-1">
          <v-btn
            icon
            size="small"
            variant="text"
            @click="openViewModal(item)"
          >
            <v-icon size="small">mdi-eye</v-icon>
            <v-tooltip activator="parent">View Details</v-tooltip>
          </v-btn>

          <v-btn
            icon
            size="small"
            variant="text"
            @click="openEditModal(item)"
          >
            <v-icon size="small">mdi-pencil</v-icon>
            <v-tooltip activator="parent">Edit CPMK</v-tooltip>
          </v-btn>

          <v-btn
            icon
            size="small"
            variant="text"
            @click="openSubCPMKModal(item)"
          >
            <v-icon size="small">mdi-format-list-bulleted</v-icon>
            <v-tooltip activator="parent">Manage Sub-CPMK</v-tooltip>
          </v-btn>

          <v-btn
            icon
            size="small"
            variant="text"
            @click="openCPLRelationsModal(item)"
          >
            <v-icon size="small">mdi-vector-link</v-icon>
            <v-tooltip activator="parent">CPL Relations</v-tooltip>
          </v-btn>

          <v-btn
            icon
            size="small"
            variant="text"
            color="error"
            @click="openDeleteDialog(item)"
          >
            <v-icon size="small">mdi-delete</v-icon>
            <v-tooltip activator="parent">Delete CPMK</v-tooltip>
          </v-btn>
        </div>
      </template>
    </DataTable>

    <!-- CPMK Form Modal -->
    <FormModal
      v-model="showModal"
      :title="modalTitle"
      :icon="modalIcon"
      :mode="modalMode"
      :loading="modalLoading"
      max-width="900"
      @submit="handleSubmit"
      @close="closeModal"
    >
      <v-row>
        <v-col cols="12" md="6">
          <v-text-field
            v-model="formData.code"
            :rules="codeRules"
            label="CPMK Code *"
            variant="outlined"
            prepend-inner-icon="mdi-identifier"
            :disabled="modalLoading || modalMode === 'view'"
            placeholder="e.g., CPMK-01"
          />
        </v-col>

        <v-col cols="12" md="6">
          <v-select
            v-model="formData.course_id"
            :items="courseOptions"
            :rules="courseRules"
            label="Course *"
            variant="outlined"
            prepend-inner-icon="mdi-book-open-page-variant"
            :disabled="modalLoading || modalMode === 'view'"
          />
        </v-col>

        <v-col cols="12" md="6">
          <v-select
            v-model="formData.cognitive_level"
            :items="cognitiveLevelOptions"
            :rules="cognitiveLevelRules"
            label="Cognitive Level *"
            variant="outlined"
            prepend-inner-icon="mdi-brain"
            :disabled="modalLoading || modalMode === 'view'"
          />
        </v-col>

        <v-col cols="12" md="6">
          <v-text-field
            v-model="formData.weight_percentage"
            :rules="weightRules"
            label="Weight Percentage *"
            type="number"
            variant="outlined"
            prepend-inner-icon="mdi-scale-balance"
            suffix="%"
            :disabled="modalLoading || modalMode === 'view'"
            min="0"
            max="100"
          />
        </v-col>

        <v-col cols="12">
          <v-textarea
            v-model="formData.description"
            :rules="descriptionRules"
            label="Description *"
            variant="outlined"
            prepend-inner-icon="mdi-text"
            rows="3"
            :disabled="modalLoading || modalMode === 'view'"
            hint="Brief description of the learning outcome"
          />
        </v-col>

        <v-col cols="12">
          <v-textarea
            v-model="formData.learning_outcome"
            :rules="learningOutcomeRules"
            label="Learning Outcome Statement *"
            variant="outlined"
            prepend-inner-icon="mdi-target"
            rows="4"
            :disabled="modalLoading || modalMode === 'view'"
            hint="Detailed, measurable learning outcome statement"
          />
        </v-col>

        <v-col cols="12" md="6">
          <v-switch
            v-model="formData.is_active"
            label="Active"
            color="primary"
            :disabled="modalLoading || modalMode === 'view'"
          />
        </v-col>
      </v-row>
    </FormModal>

    <!-- Sub-CPMK Management Modal -->
    <v-dialog v-model="showSubCPMKModal" max-width="1200" scrollable>
      <v-card>
        <v-card-title class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <v-icon class="mr-2">mdi-format-list-bulleted</v-icon>
            <span class="text-h6 font-weight-bold">Sub-CPMK for {{ selectedCPMK?.code }}</span>
          </div>
          <v-btn icon variant="text" @click="showSubCPMKModal = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>

        <v-divider />

        <v-card-text>
          <SubCPMKManagement
            v-if="selectedCPMK"
            :cpmk="selectedCPMK"
            @close="showSubCPMKModal = false"
          />
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- CPL Relations Modal -->
    <v-dialog v-model="showCPLRelationsModal" max-width="1000" scrollable>
      <v-card>
        <v-card-title class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <v-icon class="mr-2">mdi-vector-link</v-icon>
            <span class="text-h6 font-weight-bold">CPL Relations for {{ selectedCPMK?.code }}</span>
          </div>
          <v-btn icon variant="text" @click="showCPLRelationsModal = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>

        <v-divider />

        <v-card-text>
          <CPMKCPLRelations
            v-if="selectedCPMK"
            :cpmk="selectedCPMK"
            @close="showCPLRelationsModal = false"
          />
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- Weight Validation Modal -->
    <v-dialog v-model="showWeightValidation" max-width="800">
      <v-card>
        <v-card-title class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <v-icon class="mr-2">mdi-scale-balance</v-icon>
            <span class="text-h6 font-weight-bold">Weight Validation</span>
          </div>
          <v-btn icon variant="text" @click="showWeightValidation = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>

        <v-divider />

        <v-card-text>
          <CPMKWeightValidation
            v-if="selectedCourse"
            :course-id="selectedCourse"
            @close="showWeightValidation = false"
          />
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- CPL Mapping Modal -->
    <v-dialog v-model="showCPLMapping" max-width="1400" scrollable>
      <v-card>
        <v-card-title class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <v-icon class="mr-2">mdi-vector-link</v-icon>
            <span class="text-h6 font-weight-bold">CPMK-CPL Mapping</span>
          </div>
          <v-btn icon variant="text" @click="showCPLMapping = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>

        <v-divider />

        <v-card-text>
          <CPMKCPLMapping
            v-if="selectedCourse"
            :course-id="selectedCourse"
            @close="showCPLMapping = false"
          />
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- Delete Confirmation Dialog -->
    <v-dialog v-model="showDeleteDialog" max-width="400">
      <v-card>
        <v-card-title class="text-h6">Confirm Delete</v-card-title>
        <v-card-text>
          Are you sure you want to delete CPMK "{{ selectedCPMK?.code }}"?
          This action cannot be undone and will also remove all sub-CPMK and CPL relations.
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn @click="showDeleteDialog = false">Cancel</v-btn>
          <v-btn
            color="error"
            :loading="deleteLoading"
            @click="confirmDelete"
          >
            Delete
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Success/Error Snackbars -->
    <v-snackbar
      v-model="showSuccess"
      color="success"
      timeout="3000"
    >
      {{ successMessage }}
      <template v-slot:actions>
        <v-btn @click="showSuccess = false">Close</v-btn>
      </template>
    </v-snackbar>

    <v-snackbar
      v-model="showError"
      color="error"
      timeout="5000"
    >
      {{ errorMessage }}
      <template v-slot:actions>
        <v-btn @click="showError = false">Close</v-btn>
      </template>
    </v-snackbar>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import DataTable from '@/components/common/DataTable.vue'
import FormModal from '@/components/common/FormModal.vue'
import SubCPMKManagement from '@/components/cpmk/SubCPMKManagement.vue'
import CPMKCPLRelations from '@/components/cpmk/CPMKCPLRelations.vue'
import CPMKWeightValidation from '@/components/cpmk/CPMKWeightValidation.vue'
import CPMKCPLMapping from '@/components/cpmk/CPMKCPLMapping.vue'
import { cpmkAPI, coursesAPI } from '@/services/api'
import type { CPMK, Course, PaginationParams } from '@/types/auth'

// State
const loading = ref(false)
const modalLoading = ref(false)
const deleteLoading = ref(false)
const showModal = ref(false)
const showSubCPMKModal = ref(false)
const showCPLRelationsModal = ref(false)
const showWeightValidation = ref(false)
const showCPLMapping = ref(false)
const showDeleteDialog = ref(false)
const showSuccess = ref(false)
const showError = ref(false)
const successMessage = ref('')
const errorMessage = ref('')

const cpmks = ref<CPMK[]>([])
const courses = ref<Course[]>([])
const totalItems = ref(0)
const selectedCPMK = ref<CPMK | null>(null)
const selectedCourse = ref<number | null>(null)
const modalMode = ref<'create' | 'edit' | 'view'>('create')

// Form data
const formData = reactive({
  code: '',
  course_id: null as number | null,
  description: '',
  learning_outcome: '',
  cognitive_level: 'C1' as 'C1' | 'C2' | 'C3' | 'C4' | 'C5' | 'C6',
  weight_percentage: null as number | null,
  is_active: true
})

// Pagination and filtering
const searchQuery = ref('')
const filters = ref<Record<string, any>>({})
const pagination = ref<PaginationParams>({
  page: 1,
  per_page: 20,
  sort_by: 'created_at',
  sort_order: 'desc'
})

// Statistics
const totalCPMK = computed(() => cpmks.value.length)
const activeCPMK = computed(() => cpmks.value.filter(c => c.is_active).length)
const averageWeight = computed(() => {
  if (cpmks.value.length === 0) return 0
  const total = cpmks.value.reduce((sum, c) => sum + c.weight_percentage, 0)
  return Math.round(total / cpmks.value.length)
})
const mappedCPMK = computed(() =>
  cpmks.value.filter(c => c.cpl_relations && c.cpl_relations.length > 0).length
)

// Table configuration
const tableHeaders = [
  { key: 'code', title: 'Code', sortable: true, type: 'text' as const },
  { key: 'course', title: 'Course', sortable: false, type: 'text' as const },
  { key: 'description', title: 'Description', sortable: false, type: 'text' as const },
  { key: 'cognitive_level', title: 'Cognitive Level', sortable: true, type: 'text' as const },
  { key: 'weight_percentage', title: 'Weight', sortable: true, type: 'text' as const },
  { key: 'is_active', title: 'Status', sortable: true, type: 'boolean' as const },
  { key: 'created_at', title: 'Created', sortable: true, type: 'date' as const }
]

const tableFilters = [
  {
    key: 'cognitive_level',
    label: 'Cognitive Level',
    options: [
      { title: 'C1 - Remember', value: 'C1' },
      { title: 'C2 - Understand', value: 'C2' },
      { title: 'C3 - Apply', value: 'C3' },
      { title: 'C4 - Analyze', value: 'C4' },
      { title: 'C5 - Evaluate', value: 'C5' },
      { title: 'C6 - Create', value: 'C6' }
    ]
  },
  {
    key: 'course_id',
    label: 'Course',
    options: [] as Array<{ title: string; value: number }>
  },
  {
    key: 'is_active',
    label: 'Status',
    options: [
      { title: 'Active', value: true },
      { title: 'Inactive', value: false }
    ]
  }
]

// Computed options
const modalTitle = computed(() => {
  switch (modalMode.value) {
    case 'create':
      return 'Create New CPMK'
    case 'edit':
      return 'Edit CPMK'
    case 'view':
      return 'View CPMK Details'
    default:
      return 'CPMK Form'
  }
})

const modalIcon = computed(() => {
  switch (modalMode.value) {
    case 'create':
      return 'mdi-bullseye-plus'
    case 'edit':
      return 'mdi-bullseye-edit'
    case 'view':
      return 'mdi-bullseye-arrow'
    default:
      return 'mdi-bullseye-arrow'
  }
})

const courseOptions = computed(() =>
  courses.value.map(course => ({
    title: `${course.code} - ${course.name}`,
    value: course.id
  }))
)

const cognitiveLevelOptions = [
  { title: 'C1 - Remember (Mengingat)', value: 'C1' },
  { title: 'C2 - Understand (Memahami)', value: 'C2' },
  { title: 'C3 - Apply (Menerapkan)', value: 'C3' },
  { title: 'C4 - Analyze (Menganalisis)', value: 'C4' },
  { title: 'C5 - Evaluate (Mengevaluasi)', value: 'C5' },
  { title: 'C6 - Create (Mencipta)', value: 'C6' }
]

// Validation rules
const codeRules = [
  (v: string) => !!v || 'CPMK code is required',
  (v: string) => v.length >= 3 || 'CPMK code must be at least 3 characters',
  (v: string) => /^CPMK-\d{2}$/.test(v) || 'CPMK code format: CPMK-01, CPMK-02, etc.'
]

const courseRules = [
  (v: number) => !!v || 'Course is required'
]

const cognitiveLevelRules = [
  (v: string) => !!v || 'Cognitive level is required'
]

const weightRules = [
  (v: number) => v !== null && v !== undefined || 'Weight percentage is required',
  (v: number) => v >= 0 && v <= 100 || 'Weight must be between 0 and 100'
]

const descriptionRules = [
  (v: string) => !!v || 'Description is required',
  (v: string) => v.length >= 10 || 'Description must be at least 10 characters'
]

const learningOutcomeRules = [
  (v: string) => !!v || 'Learning outcome statement is required',
  (v: string) => v.length >= 20 || 'Learning outcome must be at least 20 characters'
]

// Methods
const loadCPMKs = async () => {
  loading.value = true
  try {
    const params = {
      ...pagination.value,
      search: searchQuery.value,
      ...filters.value
    }

    const response = await cpmkAPI.getAll(params)
    cpmks.value = response.data.data
    totalItems.value = response.data.meta?.total || 0
  } catch (error: any) {
    errorMessage.value = 'Failed to load CPMKs'
    showError.value = true
    console.error('Load CPMKs error:', error)
  } finally {
    loading.value = false
  }
}

const loadCourses = async () => {
  try {
    const response = await coursesAPI.getAll({ per_page: 1000 })
    courses.value = response.data.data

    // Update filter options
    tableFilters[1].options = courses.value.map(course => ({
      title: `${course.code} - ${course.name}`,
      value: course.id
    }))
  } catch (error) {
    console.error('Load courses error:', error)
  }
}

const handleCourseChange = () => {
  if (selectedCourse.value) {
    filters.value.course_id = selectedCourse.value
    loadCPMKs()
  } else {
    delete filters.value.course_id
    loadCPMKs()
  }
}

const openCreateModal = () => {
  modalMode.value = 'create'
  resetForm()
  showModal.value = true
}

const openEditModal = (cpmk: CPMK) => {
  modalMode.value = 'edit'
  selectedCPMK.value = cpmk
  populateForm(cpmk)
  showModal.value = true
}

const openViewModal = (cpmk: CPMK) => {
  modalMode.value = 'view'
  selectedCPMK.value = cpmk
  populateForm(cpmk)
  showModal.value = true
}

const openSubCPMKModal = (cpmk: CPMK) => {
  selectedCPMK.value = cpmk
  showSubCPMKModal.value = true
}

const openCPLRelationsModal = (cpmk: CPMK) => {
  selectedCPMK.value = cpmk
  showCPLRelationsModal.value = true
}

const openDeleteDialog = (cpmk: CPMK) => {
  selectedCPMK.value = cpmk
  showDeleteDialog.value = true
}

const resetForm = () => {
  Object.assign(formData, {
    code: '',
    course_id: selectedCourse.value,
    description: '',
    learning_outcome: '',
    cognitive_level: 'C1',
    weight_percentage: null,
    is_active: true
  })
}

const populateForm = (cpmk: CPMK) => {
  Object.assign(formData, {
    code: cpmk.code,
    course_id: cpmk.course_id,
    description: cpmk.description,
    learning_outcome: cpmk.learning_outcome,
    cognitive_level: cpmk.cognitive_level,
    weight_percentage: cpmk.weight_percentage,
    is_active: cpmk.is_active
  })
}

const handleSubmit = async () => {
  modalLoading.value = true
  try {
    if (modalMode.value === 'create') {
      await cpmkAPI.create(formData)
      successMessage.value = 'CPMK created successfully!'
    } else if (modalMode.value === 'edit' && selectedCPMK.value) {
      await cpmkAPI.update(selectedCPMK.value.id, formData)
      successMessage.value = 'CPMK updated successfully!'
    }

    showSuccess.value = true
    closeModal()
    await loadCPMKs()
  } catch (error: any) {
    errorMessage.value = error.response?.data?.message || 'Operation failed'
    showError.value = true
  } finally {
    modalLoading.value = false
  }
}

const confirmDelete = async () => {
  if (!selectedCPMK.value) return

  deleteLoading.value = true
  try {
    await cpmkAPI.delete(selectedCPMK.value.id)
    successMessage.value = 'CPMK deleted successfully!'
    showSuccess.value = true
    showDeleteDialog.value = false
    await loadCPMKs()
  } catch (error: any) {
    errorMessage.value = error.response?.data?.message || 'Delete failed'
    showError.value = true
  } finally {
    deleteLoading.value = false
  }
}

const closeModal = () => {
  showModal.value = false
  selectedCPMK.value = null
  resetForm()
}

const handleSearch = (query: string) => {
  searchQuery.value = query
  pagination.value.page = 1
  loadCPMKs()
}

const handleFilter = (filterValues: Record<string, any>) => {
  filters.value = filterValues
  pagination.value.page = 1
  loadCPMKs()
}

const handleTableOptions = (options: any) => {
  pagination.value = {
    ...pagination.value,
    page: options.page,
    per_page: options.itemsPerPage,
    sort_by: options.sortBy?.[0]?.key || 'created_at',
    sort_order: options.sortBy?.[0]?.order || 'desc'
  }
  loadCPMKs()
}

// Utility functions
const getCognitiveLevelColor = (level: string) => {
  const colors: Record<string, string> = {
    C1: 'blue-grey',
    C2: 'blue',
    C3: 'green',
    C4: 'orange',
    C5: 'deep-orange',
    C6: 'red'
  }
  return colors[level] || 'primary'
}

const getWeightColor = (weight: number) => {
  if (weight >= 80) return 'success'
  if (weight >= 60) return 'warning'
  if (weight >= 40) return 'orange'
  return 'error'
}

// Lifecycle
onMounted(async () => {
  await Promise.all([
    loadCourses(),
    loadCPMKs()
  ])
})
</script>
