<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class AssessmentMethodSeeder extends Seeder
{
    public function run()
    {
        // Get user ID for created_by
        $user = $this->db->table('users')
            ->where('username', 'superadmin')
            ->get()
            ->getRowArray();

        $data = [
            [
                'name' => 'Tugas Individu',
                'description' => 'Tugas yang dikerjakan secara individu oleh mahasiswa',
                'type' => 'formatif',
                'category' => 'tugas',
                'is_active' => 1,
                'created_by' => $user['id'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Tugas Kelompok',
                'description' => 'Tugas yang dikerjakan secara berkelompok',
                'type' => 'formatif',
                'category' => 'tugas',
                'is_active' => 1,
                'created_by' => $user['id'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Kuis Mingguan',
                'description' => 'Kuis singkat yang dilakukan setiap minggu',
                'type' => 'formatif',
                'category' => 'kuis',
                'is_active' => 1,
                'created_by' => $user['id'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Kuis Online',
                'description' => 'Kuis yang dilakukan secara online melalui platform e-learning',
                'type' => 'formatif',
                'category' => 'kuis',
                'is_active' => 1,
                'created_by' => $user['id'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Ujian Tengah Semester',
                'description' => 'Ujian yang dilakukan pada pertengahan semester',
                'type' => 'sumatif',
                'category' => 'uts',
                'is_active' => 1,
                'created_by' => $user['id'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Ujian Akhir Semester',
                'description' => 'Ujian yang dilakukan pada akhir semester',
                'type' => 'sumatif',
                'category' => 'uas',
                'is_active' => 1,
                'created_by' => $user['id'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Praktikum Laboratorium',
                'description' => 'Penilaian praktikum yang dilakukan di laboratorium',
                'type' => 'formatif',
                'category' => 'praktikum',
                'is_active' => 1,
                'created_by' => $user['id'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Praktikum Mandiri',
                'description' => 'Praktikum yang dikerjakan secara mandiri di luar jam kuliah',
                'type' => 'formatif',
                'category' => 'praktikum',
                'is_active' => 1,
                'created_by' => $user['id'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Proyek Akhir',
                'description' => 'Proyek besar yang dikerjakan di akhir semester',
                'type' => 'sumatif',
                'category' => 'proyek',
                'is_active' => 1,
                'created_by' => $user['id'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Proyek Mini',
                'description' => 'Proyek kecil yang dikerjakan dalam beberapa minggu',
                'type' => 'formatif',
                'category' => 'proyek',
                'is_active' => 1,
                'created_by' => $user['id'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Presentasi Individu',
                'description' => 'Presentasi yang dilakukan secara individu',
                'type' => 'formatif',
                'category' => 'presentasi',
                'is_active' => 1,
                'created_by' => $user['id'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Presentasi Kelompok',
                'description' => 'Presentasi yang dilakukan secara berkelompok',
                'type' => 'formatif',
                'category' => 'presentasi',
                'is_active' => 1,
                'created_by' => $user['id'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Diskusi Forum',
                'description' => 'Penilaian partisipasi dalam diskusi forum online',
                'type' => 'formatif',
                'category' => 'lainnya',
                'is_active' => 1,
                'created_by' => $user['id'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Peer Review',
                'description' => 'Penilaian yang dilakukan oleh sesama mahasiswa',
                'type' => 'formatif',
                'category' => 'lainnya',
                'is_active' => 1,
                'created_by' => $user['id'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Portfolio',
                'description' => 'Kumpulan karya mahasiswa selama semester',
                'type' => 'sumatif',
                'category' => 'lainnya',
                'is_active' => 1,
                'created_by' => $user['id'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ];

        // Insert data
        $this->db->table('assessment_methods')->insertBatch($data);
        
        echo "Assessment methods seeded successfully! Total records: " . count($data) . PHP_EOL;
    }
}
