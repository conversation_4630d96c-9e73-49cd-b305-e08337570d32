import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import axios from 'axios'
import type { User, LoginCredentials, AuthResponse } from '@/types/auth'

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('rps_auth_token'))
  const refreshToken = ref<string | null>(localStorage.getItem('rps_refresh_token'))
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const userRoles = computed(() => user.value?.roles || [])
  const hasRole = computed(() => (role: string) => userRoles.value.includes(role))

  // Actions
  const login = async (credentials: LoginCredentials): Promise<void> => {
    loading.value = true
    error.value = null
    
    try {
      const response = await axios.post<AuthResponse>('/api/v1/auth/login', credentials)
      const { user: userData, token: authToken, refresh_token } = response.data.data
      
      // Store tokens
      token.value = authToken
      refreshToken.value = refresh_token
      user.value = userData
      
      // Persist to localStorage
      localStorage.setItem('rps_auth_token', authToken)
      localStorage.setItem('rps_refresh_token', refresh_token)
      
      // Set default authorization header
      axios.defaults.headers.common['Authorization'] = `Bearer ${authToken}`
      
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Login failed'
      throw err
    } finally {
      loading.value = false
    }
  }

  const logout = async (): Promise<void> => {
    loading.value = true
    
    try {
      if (token.value) {
        await axios.post('/api/v1/auth/logout')
      }
    } catch (err) {
      console.error('Logout error:', err)
    } finally {
      // Clear state
      user.value = null
      token.value = null
      refreshToken.value = null
      
      // Clear localStorage
      localStorage.removeItem('rps_auth_token')
      localStorage.removeItem('rps_refresh_token')
      
      // Remove authorization header
      delete axios.defaults.headers.common['Authorization']
      
      loading.value = false
    }
  }

  const refreshAuthToken = async (): Promise<void> => {
    if (!refreshToken.value) {
      throw new Error('No refresh token available')
    }
    
    try {
      const response = await axios.post<AuthResponse>('/api/v1/auth/refresh', {
        refresh_token: refreshToken.value
      })
      
      const { token: newToken, refresh_token: newRefreshToken } = response.data.data
      
      token.value = newToken
      refreshToken.value = newRefreshToken
      
      localStorage.setItem('rps_auth_token', newToken)
      localStorage.setItem('rps_refresh_token', newRefreshToken)
      
      axios.defaults.headers.common['Authorization'] = `Bearer ${newToken}`
      
    } catch (err) {
      await logout()
      throw err
    }
  }

  const fetchProfile = async (): Promise<void> => {
    if (!token.value) return
    
    try {
      const response = await axios.get<{ data: User }>('/api/v1/auth/profile')
      user.value = response.data.data
    } catch (err) {
      console.error('Failed to fetch profile:', err)
      await logout()
    }
  }

  const updateProfile = async (profileData: Partial<User>): Promise<void> => {
    loading.value = true
    error.value = null
    
    try {
      const response = await axios.put<{ data: User }>('/api/v1/auth/profile', profileData)
      user.value = response.data.data
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Profile update failed'
      throw err
    } finally {
      loading.value = false
    }
  }

  const changePassword = async (passwordData: {
    current_password: string
    new_password: string
    confirm_password: string
  }): Promise<void> => {
    loading.value = true
    error.value = null
    
    try {
      await axios.put('/api/v1/auth/change-password', passwordData)
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Password change failed'
      throw err
    } finally {
      loading.value = false
    }
  }

  // Initialize auth state
  const initialize = async (): Promise<void> => {
    if (token.value) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${token.value}`
      await fetchProfile()
    }
  }

  return {
    // State
    user,
    token,
    loading,
    error,
    
    // Getters
    isAuthenticated,
    userRoles,
    hasRole,
    
    // Actions
    login,
    logout,
    refreshAuthToken,
    fetchProfile,
    updateProfile,
    changePassword,
    initialize
  }
})
