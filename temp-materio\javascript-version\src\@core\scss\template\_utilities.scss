.v-timeline{
  .v-timeline-item {
    .app-timeline-title {
      color: rgba(var(--v-theme-on-surface), var(--v-high-emphasis-opacity));
      font-size: 15px;
      font-weight: 500;
      letter-spacing: 0.15px;
      line-height: 1.375rem;
    }

    .app-timeline-meta {
      color: rgba(var(--v-theme-on-surface), var(--v-disabled-opacity));
      font-size: 13px;
      letter-spacing: 0.4px;
      line-height: 1.125rem;
    }

    .app-timeline-text {
      color: rgba(var(--v-theme-on-surface), var(--v-medium-emphasis-opacity));
      font-size: .9375rem;
      line-height: 1.375rem;
    }
  }
}

.per-page-select {
  margin-block: auto;

  .v-field__input {
    align-items: center;
    padding: 2px;
  }

  .v-field__append-inner {
    align-items: center;
    padding: 0;

    .v-icon {
      margin-inline-start: 0 !important;
    }
  }
}

.leading-normal {
  font-weight: 600;
  letter-spacing: .0094rem;
}

.bg-custom-background{
  background-color: rgb(var(--v-table-header-color)) !important;
}
