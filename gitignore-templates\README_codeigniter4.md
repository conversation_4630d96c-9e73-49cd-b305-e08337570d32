# CodeIgniter 4 .gitignore Template

## 📋 Overview

Template `.gitignore` yang dioptimasi khusus untuk **CodeIgniter 4** backend development dengan coverage lengkap untuk semua aspek development, testing, dan deployment.

## 🎯 **CodeIgniter 4 Specific Features**

### **Framework Structure Support:**
- **Writable Directory**: Complete coverage untuk `/writable/` directory
- **Public Directory**: Optimized untuk public assets dan uploads
- **App Structure**: Support untuk MVC pattern CodeIgniter 4
- **System Files**: Proper handling untuk framework files

### **Key Directories Handled:**
```
project/
├── app/
│   ├── Config/ (sensitive configs ignored)
│   ├── Controllers/
│   ├── Models/
│   └── Views/
├── public/
│   └── uploads/ (ignored)
├── writable/
│   ├── cache/ (ignored)
│   ├── logs/ (ignored)
│   ├── session/ (ignored)
│   └── uploads/ (ignored)
├── system/ (ignored)
└── vendor/ (ignored)
```

## 🔧 **CodeIgniter 4 Specific Ignores**

### **1. Writable Directory (Complete Coverage):**
```gitignore
# CodeIgniter 4 Application Cache
/writable/cache/*
!/writable/cache/index.html
!/writable/cache/.htaccess

# CodeIgniter 4 Logs
/writable/logs/*
!/writable/logs/index.html
!/writable/logs/.htaccess

# CodeIgniter 4 Session Files
/writable/session/*
!/writable/session/index.html
!/writable/session/.htaccess

# CodeIgniter 4 Uploads
/writable/uploads/*
!/writable/uploads/index.html
!/writable/uploads/.htaccess

# CodeIgniter 4 Debugbar
/writable/debugbar/*
!/writable/debugbar/.htaccess
```

### **2. Environment & Configuration:**
```gitignore
# Environment Files
.env
.env.local
.env.development
.env.testing
.env.production

# Sensitive Configuration
/app/Config/Database.local.php
/app/Config/App.local.php
/app/Config/Email.local.php
/app/Config/Encryption.local.php
/app/Config/Secrets.php
/app/Config/ApiKeys.php
```

### **3. Public Directory:**
```gitignore
# Public Uploads
/public/uploads/*
!/public/uploads/index.html
!/public/uploads/.htaccess

# Compiled Assets
/public/assets/dist/
/public/css/compiled/
/public/js/compiled/
/public/build/
```

### **4. Testing & Development:**
```gitignore
# CodeIgniter 4 Testing
/tests/_output/*
/tests/_support/_generated/*
/tests/writable/*

# PHPUnit
.phpunit.result.cache
coverage/
phpunit.xml

# Development Tools
/app/Views/errors/development/
spark
```

## 🚀 **Installation & Usage**

### **For New CodeIgniter 4 Project:**
```bash
# Copy CodeIgniter 4 template
cp gitignore-templates/php-codeigniter4.gitignore .gitignore

# Verify structure
git status
```

### **For Existing Project:**
```bash
# Backup current .gitignore
cp .gitignore .gitignore.backup

# Apply CodeIgniter 4 template
cp gitignore-templates/php-codeigniter4.gitignore .gitignore

# Review and merge custom rules if needed
```

### **For RPS Management System (CodeIgniter 4):**
```bash
# Use CodeIgniter 4 template
cp gitignore-templates/php-codeigniter4.gitignore .gitignore

# Add RPS-specific rules (already included in template)
# - /writable/rps-documents/*
# - /writable/reports/*
# - /writable/exports/*
# - /public/qr-codes/*
```

## 📊 **Security Features**

### **Environment Protection:**
```gitignore
# All environment files
.env*
!.env.example

# Sensitive configurations
/app/Config/Database.local.php
/app/Config/Secrets.php
/app/Config/ApiKeys.php
```

### **API Keys & Certificates:**
```gitignore
# SSL Certificates
*.pem
*.key
*.crt
*.csr

# JWT Keys
/writable/jwt/
jwt-private.key
jwt-public.key

# OAuth Keys
oauth-private.key
oauth-public.key
```

### **Database Security:**
```gitignore
# Database files
*.sqlite
*.sqlite3
*.db

# Database dumps
*.sql
!app/Database/Migrations/*.sql
!app/Database/Seeds/*.sql
```

## 🔄 **Development Workflow**

### **1. Initial Setup:**
```bash
# Clone project
git clone <repository>
cd project

# Copy environment
cp .env.example .env

# Install dependencies
composer install

# Set permissions (Linux/Mac)
chmod -R 755 writable/
```

### **2. Development:**
```bash
# Check ignored files
git status

# Should not see:
# - writable/cache/*
# - writable/logs/*
# - writable/session/*
# - .env files
# - vendor/
```

### **3. Before Commit:**
```bash
# Verify no sensitive data
git diff --cached

# Check for accidentally tracked files
git ls-files | grep -E '\.(env|key|pem|crt)$'
```

## 📁 **Directory Structure Best Practices**

### **Writable Directory:**
```
writable/
├── cache/
│   ├── .htaccess ✅ (keep)
│   ├── index.html ✅ (keep)
│   └── *.cache ❌ (ignore)
├── logs/
│   ├── .htaccess ✅ (keep)
│   ├── index.html ✅ (keep)
│   └── *.log ❌ (ignore)
├── session/
│   ├── .htaccess ✅ (keep)
│   ├── index.html ✅ (keep)
│   └── sess_* ❌ (ignore)
└── uploads/
    ├── .htaccess ✅ (keep)
    ├── index.html ✅ (keep)
    └── user-files/ ❌ (ignore)
```

### **Public Directory:**
```
public/
├── index.php ✅ (keep)
├── .htaccess ✅ (keep)
├── assets/
│   ├── css/ ✅ (keep source)
│   ├── js/ ✅ (keep source)
│   └── dist/ ❌ (ignore compiled)
└── uploads/
    ├── .htaccess ✅ (keep)
    ├── index.html ✅ (keep)
    └── files/ ❌ (ignore)
```

## 🛠️ **Customization Examples**

### **Add Custom Upload Directories:**
```gitignore
# Custom upload directories
/public/documents/*
!/public/documents/.htaccess
/public/avatars/*
!/public/avatars/.htaccess
/writable/temp-files/*
!/writable/temp-files/.htaccess
```

### **Add Custom Cache Directories:**
```gitignore
# Custom cache
/writable/cache/custom/*
/writable/cache/api/*
/writable/cache/reports/*
```

### **Add Development Tools:**
```gitignore
# Development tools
/tools/
/scripts/dev/
.vscode/settings.json
.idea/workspace.xml
```

## 🔍 **Testing & Verification**

### **Verify Template Works:**
```bash
# Create test files
touch .env
touch writable/logs/test.log
touch writable/cache/test.cache
touch public/uploads/test.jpg
touch vendor/test.txt

# Check git status
git status

# Should show:
# - .gitignore (if new)
# Should NOT show:
# - .env
# - writable/logs/test.log
# - writable/cache/test.cache
# - public/uploads/test.jpg
# - vendor/test.txt

# Clean up
rm .env writable/logs/test.log writable/cache/test.cache public/uploads/test.jpg vendor/test.txt
```

### **Check for Leaks:**
```bash
# Check for accidentally tracked sensitive files
git ls-files | grep -E '\.(env|key|pem|log|cache)$'

# Should return empty result
```

## 📈 **Performance Considerations**

### **Cache Management:**
```gitignore
# Framework cache (regenerated)
/writable/cache/routes.php
/writable/cache/views/
/writable/cache/models/
/writable/cache/config/

# Application cache (custom)
/writable/cache/api/*
/writable/cache/reports/*
```

### **Asset Optimization:**
```gitignore
# Source files (keep)
/public/assets/src/

# Compiled files (ignore)
/public/assets/dist/
/public/build/
webpack-stats.json
```

## 🎯 **Production Deployment**

### **Pre-deployment Checklist:**
- [ ] `.env` file not in repository
- [ ] Database credentials not exposed
- [ ] API keys secured
- [ ] SSL certificates not tracked
- [ ] Log files not committed
- [ ] Cache files cleared
- [ ] Upload directories empty in repo

### **Deployment-specific Ignores:**
```gitignore
# Deployment configs
deploy.sh
deploy-prod.sh
.env.production
docker-compose.prod.yml
```

## 🔧 **Integration with CI/CD**

### **GitHub Actions Example:**
```yaml
# .github/workflows/ci.yml
name: CI
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.1'
      - name: Install dependencies
        run: composer install
      - name: Copy environment
        run: cp .env.example .env
      - name: Run tests
        run: vendor/bin/phpunit
```

## 📚 **Additional Resources**

### **CodeIgniter 4 Documentation:**
- [CodeIgniter 4 User Guide](https://codeigniter.com/user_guide/)
- [CodeIgniter 4 Installation](https://codeigniter.com/user_guide/installation/index.html)
- [CodeIgniter 4 Configuration](https://codeigniter.com/user_guide/general/configuration.html)

### **Security Best Practices:**
- [CodeIgniter 4 Security](https://codeigniter.com/user_guide/concepts/security.html)
- [Environment Configuration](https://codeigniter.com/user_guide/general/configuration.html#environment-variables)

---

**Note:** Template ini dioptimasi khusus untuk CodeIgniter 4 dan RPS Management System dengan security dan performance yang terjamin untuk production deployment.
