<template>
  <v-dialog
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    :max-width="maxWidth"
    :persistent="persistent"
    :scrollable="scrollable"
  >
    <v-card>
      <!-- Header -->
      <v-card-title class="d-flex align-center justify-space-between">
        <div class="d-flex align-center">
          <v-icon v-if="icon" class="mr-2">{{ icon }}</v-icon>
          <span class="text-h6 font-weight-bold">{{ title }}</span>
        </div>
        
        <v-btn
          icon
          variant="text"
          @click="handleClose"
          :disabled="loading"
        >
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>

      <v-divider />

      <!-- Content -->
      <v-card-text class="pa-6">
        <v-form ref="form" v-model="valid" @submit.prevent="handleSubmit">
          <slot :loading="loading" :valid="valid" />
        </v-form>
      </v-card-text>

      <!-- Actions -->
      <v-card-actions class="px-6 pb-6">
        <v-spacer />
        
        <v-btn
          variant="text"
          @click="handleClose"
          :disabled="loading"
        >
          Cancel
        </v-btn>
        
        <v-btn
          color="primary"
          :loading="loading"
          :disabled="!valid || loading"
          @click="handleSubmit"
        >
          <v-icon start>{{ submitIcon }}</v-icon>
          {{ submitText }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Props {
  modelValue: boolean
  title: string
  icon?: string
  maxWidth?: string | number
  persistent?: boolean
  scrollable?: boolean
  loading?: boolean
  mode?: 'create' | 'edit' | 'view'
}

const props = withDefaults(defineProps<Props>(), {
  maxWidth: 600,
  persistent: false,
  scrollable: true,
  loading: false,
  mode: 'create'
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  submit: []
  close: []
}>()

// State
const form = ref()
const valid = ref(false)

// Computed
const submitText = computed(() => {
  switch (props.mode) {
    case 'create':
      return 'Create'
    case 'edit':
      return 'Update'
    case 'view':
      return 'Close'
    default:
      return 'Save'
  }
})

const submitIcon = computed(() => {
  switch (props.mode) {
    case 'create':
      return 'mdi-plus'
    case 'edit':
      return 'mdi-content-save'
    case 'view':
      return 'mdi-close'
    default:
      return 'mdi-check'
  }
})

// Methods
const handleSubmit = async () => {
  if (props.mode === 'view') {
    handleClose()
    return
  }

  const { valid: isValid } = await form.value.validate()
  if (isValid) {
    emit('submit')
  }
}

const handleClose = () => {
  if (!props.loading) {
    emit('close')
    emit('update:modelValue', false)
  }
}

// Expose form methods
defineExpose({
  validate: () => form.value?.validate(),
  reset: () => form.value?.reset(),
  resetValidation: () => form.value?.resetValidation()
})
</script>

<style scoped>
.v-card-title {
  background-color: rgb(var(--v-theme-surface-variant));
}

.v-card-actions {
  background-color: rgb(var(--v-theme-surface-variant));
}
</style>
