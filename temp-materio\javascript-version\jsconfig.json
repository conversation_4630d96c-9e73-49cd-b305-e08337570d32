{"include": ["./vite.config.*", "./src/**/*", "./src/**/*.vue", "./themeConfig.js"], "exclude": ["./dist", "./node_modules"], "compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "<PERSON><PERSON><PERSON>", "jsx": "preserve", "paths": {"@/*": ["./src/*"], "@layouts/*": ["./src/@layouts/*"], "@layouts": ["./src/@layouts"], "@core/*": ["./src/@core/*"], "@core": ["./src/@core"], "@images/*": ["./src/assets/images/*"], "@styles/*": ["./src/styles/*"]}, "types": ["vite/client", "vite-plugin-vue-layouts/client"]}}