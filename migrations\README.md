# RPS Management System - Database Migrations

## 📋 Overview

Koleksi migration files untuk RPS Management System menggunakan CodeIgniter 4. Migration ini mengimplementasikan complete database schema yang telah didesain sebelumnya dengan proper foreign key relationships dan data seeding.

## 🗂️ Migration Files

### **Core Tables (13 Migrations)**

| No | File | Table | Description |
|----|------|-------|-------------|
| 001 | `create_roles_table.php` | `roles` | User roles dengan JSON permissions |
| 002 | `create_users_table.php` | `users` | User accounts dan profiles |
| 003 | `create_faculties_table.php` | `faculties` | Faculty management |
| 004 | `create_study_programs_table.php` | `study_programs` | Program studi details |
| 005 | `create_cpl_table.php` | `cpl` | Graduate Learning Outcomes |
| 006 | `create_courses_table.php` | `courses` | Course information |
| 007 | `create_course_references_table.php` | `course_references` | Course bibliography |
| 008 | `create_course_topics_table.php` | `course_topics` | Weekly course topics |
| 009 | `create_cpmk_table.php` | `cpmk` | Course Learning Outcomes |
| 010 | `create_cpmk_cpl_relations_table.php` | `cpmk_cpl_relations` | CPMK-CPL mappings |
| 011 | `create_sub_cpmk_table.php` | `sub_cpmk` | Sub-course learning outcomes |
| 012 | `create_assessment_methods_table.php` | `assessment_methods` | Assessment types library |
| 013 | `create_assessment_plans_table.php` | `assessment_plans` | Assessment planning |

## 🚀 **Running Migrations**

### **Prerequisites**
- CodeIgniter 4.3+
- PHP 7.4+
- MySQL 5.6+ or MySQL 8.0+
- Proper database configuration in `.env`

### **Environment Setup**
```bash
# Copy environment file
cp .env.example .env

# Configure database in .env
database.default.hostname = localhost
database.default.database = rps_management
database.default.username = your_username
database.default.password = your_password
database.default.DBDriver = MySQLi
database.default.DBPrefix = 
database.default.port = 3306
```

### **Run All Migrations**
```bash
# Run all migrations in order
php spark migrate

# Check migration status
php spark migrate:status

# Run specific migration
php spark migrate -n "App\Database\Migrations\CreateRolesTable"
```

### **Rollback Migrations**
```bash
# Rollback last migration
php spark migrate:rollback

# Rollback to specific version
php spark migrate:rollback -b 5

# Rollback all migrations
php spark migrate:rollback -b 0
```

### **Refresh Migrations**
```bash
# Rollback all and re-run
php spark migrate:refresh

# Refresh and run seeders
php spark migrate:refresh --seed
```

## 🔧 **Migration Features**

### **1. Proper Foreign Key Relationships**
```php
// Example from users table
$this->forge->addForeignKey('role_id', 'roles', 'id', 'SET NULL', 'CASCADE');

// Example from courses table
$this->forge->addForeignKey('study_program_id', 'study_programs', 'id', 'RESTRICT', 'CASCADE');
```

### **2. Comprehensive Indexing**
```php
// Primary keys
$this->forge->addKey('id', true);

// Unique constraints
$this->forge->addUniqueKey('username');
$this->forge->addUniqueKey(['code', 'study_program_id']);

// Regular indexes
$this->forge->addKey('role_id');
$this->forge->addKey('is_active');
```

### **3. Data Type Optimization**
```php
// Optimized data types for MySQL 5.6 compatibility
'is_active' => [
    'type'       => 'TINYINT',
    'constraint' => 1,
    'default'    => 1,
],
'permissions' => [
    'type' => 'TEXT',
    'null' => true,
    'comment' => 'JSON formatted permissions',
],
```

### **4. Built-in Data Seeding**
```php
// Roles seeding in migration
$data = [
    [
        'name' => 'Super Admin',
        'description' => 'Full system access',
        'permissions' => '{"all": true}',
    ],
    // ... more roles
];
$this->db->table('roles')->insertBatch($data);
```

## 📊 **Database Schema Overview**

### **Entity Relationships**
```
Users (1) ←→ (N) Roles
Users (1) ←→ (N) Faculties (dean_id)
Faculties (1) ←→ (N) Study Programs
Study Programs (1) ←→ (N) CPL
Study Programs (1) ←→ (N) Courses
Courses (1) ←→ (N) Course References
Courses (1) ←→ (N) Course Topics
Courses (1) ←→ (N) CPMK
CPMK (N) ←→ (N) CPL (via cpmk_cpl_relations)
CPMK (1) ←→ (N) Sub CPMK
CPMK (1) ←→ (N) Assessment Plans
Assessment Methods (1) ←→ (N) Assessment Plans
```

### **Key Features**
- **Hierarchical Structure**: University → Faculty → Study Program → Course
- **Learning Outcomes**: CPL → CPMK → Sub-CPMK → Assessment
- **Flexible Permissions**: JSON-based role permissions
- **Audit Trail**: created_by, created_at, updated_at
- **Soft Delete**: is_active flags

## 🔒 **Security Features**

### **1. Foreign Key Constraints**
- **RESTRICT**: Prevents deletion if referenced (study_programs → faculties)
- **CASCADE**: Deletes related records (courses → course_topics)
- **SET NULL**: Sets foreign key to NULL (users → roles)

### **2. Data Validation**
```php
// ENUM constraints
'degree_level' => [
    'type'       => 'ENUM',
    'constraint' => ['D3', 'S1', 'S2', 'S3'],
    'null'       => false,
],

// Decimal constraints
'weight_percentage' => [
    'type'       => 'DECIMAL',
    'constraint' => '5,2',
    'null'       => false,
],
```

### **3. Unique Constraints**
```php
// Prevent duplicate codes within same context
$this->forge->addUniqueKey(['code', 'study_program_id']);
$this->forge->addUniqueKey(['code', 'course_id']);
$this->forge->addUniqueKey(['cpmk_id', 'cpl_id']);
```

## 🛠️ **Customization Guide**

### **Adding New Migration**
```bash
# Create new migration
php spark make:migration CreateNewTable

# Edit the generated file
# app/Database/Migrations/YYYY-MM-DD-HHMMSS_CreateNewTable.php
```

### **Modifying Existing Migration**
```bash
# Create alter migration
php spark make:migration AlterExistingTable

# Example alter migration
public function up()
{
    $this->forge->addColumn('users', [
        'phone' => [
            'type'       => 'VARCHAR',
            'constraint' => 20,
            'null'       => true,
            'after'      => 'email'
        ]
    ]);
}
```

### **Adding Indexes**
```bash
# Create index migration
php spark make:migration AddIndexesToTable

public function up()
{
    $this->forge->addKey('users', 'email');
    $this->forge->addKey('courses', ['study_program_id', 'semester']);
}
```

## 📋 **Migration Checklist**

### **Before Running Migrations**
- [ ] Database connection configured in `.env`
- [ ] Database user has proper permissions
- [ ] Backup existing database (if any)
- [ ] Check migration order and dependencies

### **After Running Migrations**
- [ ] Verify all tables created successfully
- [ ] Check foreign key constraints
- [ ] Verify data seeding completed
- [ ] Test basic CRUD operations
- [ ] Run application tests

### **Production Deployment**
- [ ] Test migrations on staging environment
- [ ] Create database backup before deployment
- [ ] Run migrations during maintenance window
- [ ] Verify application functionality
- [ ] Monitor for any issues

## 🔍 **Troubleshooting**

### **Common Issues**

#### **Foreign Key Constraint Errors**
```bash
# Check if referenced table exists
SHOW TABLES;

# Check table structure
DESCRIBE table_name;

# Disable foreign key checks temporarily (development only)
SET FOREIGN_KEY_CHECKS = 0;
```

#### **Migration Already Exists**
```bash
# Check migration status
php spark migrate:status

# Force re-run specific migration
php spark migrate:rollback -b target_version
php spark migrate
```

#### **Data Type Errors**
```bash
# Check MySQL version compatibility
SELECT VERSION();

# For MySQL 5.6, ensure:
# - Use TINYINT(1) instead of BOOLEAN
# - Use TEXT instead of JSON
# - Use proper ENUM syntax
```

### **Debugging Commands**
```bash
# Enable debug mode in .env
CI_ENVIRONMENT = development

# Check database connection
php spark db:table users

# View migration history
SELECT * FROM migrations;
```

## 📚 **Additional Resources**

### **CodeIgniter 4 Documentation**
- [Database Migrations](https://codeigniter.com/user_guide/dbmgmt/migration.html)
- [Database Forge](https://codeigniter.com/user_guide/database/forge.html)
- [Query Builder](https://codeigniter.com/user_guide/database/query_builder.html)

### **Best Practices**
- Always test migrations on development environment first
- Use descriptive migration names and comments
- Include rollback logic in down() methods
- Keep migrations atomic and focused
- Use proper data types for your database version

---

**Note:** Migration ini dioptimasi untuk MySQL 5.6+ compatibility dan siap untuk production deployment dengan proper error handling dan rollback capabilities.
