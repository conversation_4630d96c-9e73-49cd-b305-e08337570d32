# Backend .gitignore Templates

## 📋 Overview

Koleksi template `.gitignore` yang komprehensif untuk berbagai teknologi backend development. Setiap template dioptimasi untuk framework dan teknologi spesifik dengan coverage yang lengkap.

## 🗂️ Available Templates

### **1. Main .gitignore (Root)**
- **File**: `../.gitignore`
- **Description**: Universal .gitignore untuk semua teknologi backend
- **Coverage**: PHP, Node.js, Python, Java, .NET, Go, dan lainnya
- **Use Case**: Project dengan multiple teknologi atau monorepo

### **2. PHP Laravel Template**
- **File**: `php-laravel.gitignore`
- **Framework**: Laravel 8+, PHP 7.4+
- **Features**: Laravel-specific directories, Composer, PHPUnit, storage optimization
- **Use Case**: Laravel API development, RPS Management System

### **3. PHP CodeIgniter 4 Template**
- **File**: `php-codeigniter4.gitignore`
- **Framework**: CodeIgniter 4.3+, PHP 7.4+
- **Features**: Writable directory, public uploads, CI4-specific cache, testing
- **Use Case**: CodeIgniter 4 API development, RPS Management System

### **4. Node.js Express Template**
- **File**: `nodejs-express.gitignore`
- **Framework**: Express.js, Node.js 14+
- **Features**: NPM/Yarn, TypeScript, testing frameworks, build tools
- **Use Case**: REST API, GraphQL, microservices

### **5. Python Django Template**
- **File**: `python-django.gitignore`
- **Framework**: Django 3+, Python 3.8+
- **Features**: Django-specific, virtual environments, Celery, DRF
- **Use Case**: Django REST API, admin panels, data processing

## 🚀 Quick Start

### **For Laravel Project:**
```bash
# Copy Laravel template
cp gitignore-templates/php-laravel.gitignore .gitignore

# Or use main universal template
cp .gitignore ./backend/.gitignore
```

### **For CodeIgniter 4 Project:**
```bash
# Copy CodeIgniter 4 template
cp gitignore-templates/php-codeigniter4.gitignore .gitignore
```

### **For Express.js Project:**
```bash
# Copy Node.js template
cp gitignore-templates/nodejs-express.gitignore .gitignore
```

### **For Django Project:**
```bash
# Copy Django template
cp gitignore-templates/python-django.gitignore .gitignore
```

## 📊 Template Comparison

| Feature | Universal | Laravel | CodeIgniter 4 | Express | Django |
|---------|-----------|---------|---------------|---------|---------|
| **Framework Specific** | ❌ | ✅ | ✅ | ✅ | ✅ |
| **Environment Files** | ✅ | ✅ | ✅ | ✅ | ✅ |
| **Dependencies** | ✅ | ✅ | ✅ | ✅ | ✅ |
| **Database Files** | ✅ | ✅ | ✅ | ✅ | ✅ |
| **Logs & Cache** | ✅ | ✅ | ✅ | ✅ | ✅ |
| **Security & Secrets** | ✅ | ✅ | ✅ | ✅ | ✅ |
| **Testing** | ✅ | ✅ | ✅ | ✅ | ✅ |
| **Build & Assets** | ✅ | ✅ | ✅ | ✅ | ✅ |
| **Deployment** | ✅ | ✅ | ✅ | ✅ | ✅ |
| **IDE Support** | ✅ | ✅ | ✅ | ✅ | ✅ |

## 🔧 Customization Guide

### **Adding Project-Specific Rules**

```bash
# Add to any .gitignore file
echo "" >> .gitignore
echo "# Project Specific" >> .gitignore
echo "/custom-directory/" >> .gitignore
echo "*.custom-extension" >> .gitignore
```

### **Common Additions for RPS Management:**

```gitignore
# RPS Management Specific
/storage/rps-documents/*
!/storage/rps-documents/.gitkeep
/public/qr-codes/*
!/public/qr-codes/.gitkeep
/exports/
*.pdf
*.xlsx
!templates/*.pdf
!templates/*.xlsx
```

## 📁 Directory Structure Examples

### **Laravel Project Structure:**
```
project/
├── .gitignore (php-laravel.gitignore)
├── app/
├── config/
├── database/
├── public/
├── storage/
│   ├── app/
│   ├── framework/
│   └── logs/
└── vendor/ (ignored)
```

### **Express.js Project Structure:**
```
project/
├── .gitignore (nodejs-express.gitignore)
├── src/
├── dist/ (ignored)
├── uploads/ (ignored)
├── logs/ (ignored)
└── node_modules/ (ignored)
```

### **Django Project Structure:**
```
project/
├── .gitignore (python-django.gitignore)
├── myproject/
├── apps/
├── static/ (ignored)
├── media/ (ignored)
├── venv/ (ignored)
└── __pycache__/ (ignored)
```

## 🛡️ Security Best Practices

### **Always Ignore:**
- Environment files (`.env`, `.env.local`)
- Database credentials
- API keys and secrets
- SSL certificates
- Private keys
- Configuration files with sensitive data

### **Keep Structure:**
```gitignore
# Keep directory structure but ignore contents
/uploads/*
!/uploads/.gitkeep

/storage/logs/*
!/storage/logs/.gitkeep
```

### **Never Commit:**
- Production database dumps
- User uploaded files
- Generated reports with sensitive data
- Backup files with credentials
- Development-only configuration

## 🔄 Template Updates

### **Laravel Template Updates:**
- Laravel 9+ compatibility
- Octane support
- Sail configuration
- Telescope and Horizon

### **Express.js Template Updates:**
- TypeScript support
- Modern build tools (Vite, esbuild)
- Microservice patterns
- Container optimization

### **Django Template Updates:**
- Django 4+ compatibility
- Async views support
- Modern deployment patterns
- Container optimization

## 📋 Checklist for New Projects

### **Before First Commit:**
- [ ] Choose appropriate .gitignore template
- [ ] Add project-specific rules
- [ ] Verify environment files are ignored
- [ ] Check sensitive data is not tracked
- [ ] Test with `git status` to ensure correct ignoring

### **After Setup:**
- [ ] Commit .gitignore as first commit
- [ ] Document any custom rules in README
- [ ] Share template with team members
- [ ] Set up pre-commit hooks if needed

## 🎯 Best Practices

### **1. Template Selection:**
- Use framework-specific template for single-tech projects
- Use universal template for multi-tech or monorepo projects
- Combine templates if using multiple frameworks

### **2. Maintenance:**
- Review .gitignore regularly
- Update when adding new tools or dependencies
- Remove obsolete rules
- Keep comments for clarity

### **3. Team Collaboration:**
- Standardize .gitignore across team
- Document custom rules
- Use .gitignore_global for personal preferences
- Review in code reviews

## 📚 Additional Resources

### **Official Documentation:**
- [Git .gitignore Documentation](https://git-scm.com/docs/gitignore)
- [GitHub .gitignore Templates](https://github.com/github/gitignore)

### **Framework-Specific:**
- [Laravel Deployment](https://laravel.com/docs/deployment)
- [Express.js Best Practices](https://expressjs.com/en/advanced/best-practice-security.html)
- [Django Deployment](https://docs.djangoproject.com/en/stable/howto/deployment/)

### **Tools:**
- [gitignore.io](https://gitignore.io) - Generate .gitignore files
- [git check-ignore](https://git-scm.com/docs/git-check-ignore) - Debug .gitignore rules

---

**Note:** Template ini dioptimasi untuk RPS Management System dan dapat diadaptasi untuk project lain dengan modifikasi sesuai kebutuhan.
