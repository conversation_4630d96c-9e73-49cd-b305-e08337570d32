export interface User {
  id: number
  username: string
  email: string
  full_name: string
  phone?: string
  avatar?: string
  roles: string[]
  faculty_id?: number
  study_program_id?: number
  is_active: boolean
  last_login?: string
  created_at: string
  updated_at: string
}

export interface LoginCredentials {
  username: string
  password: string
  remember_me?: boolean
}

export interface AuthResponse {
  success: boolean
  message: string
  data: {
    user: User
    token: string
    refresh_token: string
    expires_in: number
  }
}

export interface Faculty {
  id: number
  code: string
  name: string
  description?: string
  dean_id?: number
  vice_dean_id?: number
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface StudyProgram {
  id: number
  faculty_id: number
  code: string
  name: string
  degree_level: 'D3' | 'S1' | 'S2' | 'S3'
  study_type: 'regular' | 'international' | 'distance'
  accreditation_status?: string
  accreditation_date?: string
  total_credits: number
  study_period: number
  head_id?: number
  secretary_id?: number
  vision?: string
  mission?: string
  objectives?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Course {
  id: number
  study_program_id: number
  code: string
  name: string
  credits: number
  semester: number
  course_type: 'wajib' | 'pilihan'
  description?: string
  learning_objectives?: string
  coordinator_id?: number
  coordinator_name?: string
  prerequisite_courses?: number[]
  is_active: boolean
  created_by?: number
  created_at: string
  updated_at: string
}

export interface CourseReference {
  id: number
  course_id: number
  type: 'utama' | 'pendukung' | 'tambahan'
  title: string
  author?: string
  publisher?: string
  year?: number
  isbn?: string
  url?: string
  notes?: string
  created_by?: number
  created_at: string
  updated_at: string
}

export interface CourseTopic {
  id: number
  course_id: number
  week_number: number
  topic_title: string
  description?: string
  learning_materials?: string
  learning_methods?: string
  estimated_time?: number
  created_by?: number
  created_at: string
  updated_at: string
}

export interface CPL {
  id: number
  study_program_id: number
  code: string
  category: 'attitude' | 'knowledge' | 'general_skills' | 'specific_skills'
  description: string
  learning_outcome: string
  achievement_target?: number
  measurement_criteria?: string
  is_active: boolean
  created_by?: number
  created_at: string
  updated_at: string
  study_program_name?: string
  study_program_code?: string
}

export interface CPMK {
  id: number
  course_id: number
  code: string
  description: string
  learning_outcome: string
  cognitive_level: 'C1' | 'C2' | 'C3' | 'C4' | 'C5' | 'C6'
  weight_percentage: number
  is_active: boolean
  created_by?: number
  created_at: string
  updated_at: string
  course_name?: string
  course_code?: string
  cpl_relations?: CPMKCPLRelation[]
}

export interface CPMKCPLRelation {
  id: number
  cpmk_id: number
  cpl_id: number
  contribution_level: 'low' | 'medium' | 'high'
  weight_percentage: number
  created_by?: number
  created_at: string
  updated_at: string
  cpl_code?: string
  cpl_description?: string
  cpmk_code?: string
  cpmk_description?: string
}

export interface SubCPMK {
  id: number
  cpmk_id: number
  code: string
  description: string
  learning_indicator: string
  week_coverage: number[]
  weight_percentage: number
  is_active: boolean
  created_by?: number
  created_at: string
  updated_at: string
}

export interface AssessmentMethod {
  id: number
  name: string
  description?: string
  type: 'formatif' | 'sumatif'
  category: 'tugas' | 'kuis' | 'uts' | 'uas' | 'praktikum' | 'proyek' | 'presentasi' | 'lainnya'
  is_active: boolean
  created_by?: number
  created_at: string
  updated_at: string
}

export interface RubricCriterion {
  id: string
  name: string
  description: string
  weight_percentage: number
  levels: RubricLevel[]
}

export interface RubricLevel {
  id: string
  name: string
  description: string
  score: number
  min_score: number
  max_score: number
}

export interface AssessmentPlan {
  id: number
  cpmk_id: number
  sub_cpmk_id?: number
  assessment_method_id: number
  week_number: number
  assessment_title: string
  description?: string
  weight_percentage: number
  passing_grade: number
  rubric_criteria?: RubricCriterion[]
  due_date?: string
  is_active: boolean
  created_by?: number
  created_at: string
  updated_at: string
  // Relations
  cpmk_code?: string
  cpmk_description?: string
  sub_cpmk_code?: string
  sub_cpmk_description?: string
  assessment_method_name?: string
  assessment_method_type?: string
  assessment_method_category?: string
  course_id?: number
  course_name?: string
  course_code?: string
}

export interface AssessmentResult {
  id: number
  assessment_plan_id: number
  student_id: number
  score: number
  feedback?: string
  submission_date?: string
  graded_date?: string
  graded_by?: number
  is_late: boolean
  attempts: number
  created_at: string
  updated_at: string
  // Relations
  student_name?: string
  student_nim?: string
  assessment_title?: string
  grader_name?: string
}

export interface ReportTemplate {
  id: number
  name: string
  description?: string
  type: 'cpmk_achievement' | 'cpl_mapping' | 'assessment_analytics' | 'course_performance' | 'student_progress' | 'custom'
  category: 'academic' | 'administrative' | 'analytics' | 'compliance'
  template_config: ReportConfig
  is_active: boolean
  is_public: boolean
  created_by?: number
  created_at: string
  updated_at: string
  // Relations
  creator_name?: string
  usage_count?: number
}

export interface ReportConfig {
  title: string
  subtitle?: string
  filters: ReportFilter[]
  columns: ReportColumn[]
  charts?: ReportChart[]
  grouping?: ReportGrouping[]
  sorting?: ReportSorting[]
  formatting?: ReportFormatting
  export_options?: ExportOptions
}

export interface ReportFilter {
  field: string
  label: string
  type: 'select' | 'date' | 'daterange' | 'text' | 'number' | 'multiselect'
  options?: Array<{ label: string; value: any }>
  required?: boolean
  default_value?: any
}

export interface ReportColumn {
  field: string
  label: string
  type: 'text' | 'number' | 'date' | 'percentage' | 'currency' | 'boolean'
  width?: number
  sortable?: boolean
  filterable?: boolean
  format?: string
  aggregation?: 'sum' | 'avg' | 'count' | 'min' | 'max'
}

export interface ReportChart {
  type: 'bar' | 'line' | 'pie' | 'doughnut' | 'area' | 'scatter'
  title: string
  data_source: string
  x_axis: string
  y_axis: string
  color_scheme?: string[]
  options?: any
}

export interface ReportGrouping {
  field: string
  label: string
  order: 'asc' | 'desc'
}

export interface ReportSorting {
  field: string
  order: 'asc' | 'desc'
  priority: number
}

export interface ReportFormatting {
  page_size?: 'A4' | 'A3' | 'Letter' | 'Legal'
  orientation?: 'portrait' | 'landscape'
  margins?: { top: number; right: number; bottom: number; left: number }
  header?: string
  footer?: string
  logo?: string
  watermark?: string
}

export interface ExportOptions {
  formats: Array<'pdf' | 'excel' | 'csv' | 'json'>
  filename_pattern?: string
  include_charts?: boolean
  include_summary?: boolean
  compress?: boolean
}

export interface ReportInstance {
  id: number
  template_id: number
  name: string
  description?: string
  parameters: Record<string, any>
  status: 'pending' | 'processing' | 'completed' | 'failed'
  file_path?: string
  file_size?: number
  format: 'pdf' | 'excel' | 'csv' | 'json'
  generated_by?: number
  generated_at?: string
  expires_at?: string
  download_count: number
  is_scheduled: boolean
  schedule_config?: ScheduleConfig
  created_at: string
  updated_at: string
  // Relations
  template_name?: string
  generator_name?: string
}

export interface ScheduleConfig {
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly'
  day_of_week?: number
  day_of_month?: number
  time: string
  timezone: string
  recipients?: string[]
  auto_delete_after_days?: number
}

export interface DashboardWidget {
  id: string
  type: 'metric' | 'chart' | 'table' | 'progress' | 'trend'
  title: string
  subtitle?: string
  size: 'small' | 'medium' | 'large' | 'full'
  position: { x: number; y: number; w: number; h: number }
  data_source: string
  config: any
  refresh_interval?: number
  is_visible: boolean
}

export interface ReportData {
  headers: string[]
  rows: any[][]
  summary?: Record<string, any>
  charts?: any[]
  metadata: {
    total_records: number
    generated_at: string
    filters_applied: Record<string, any>
    execution_time: number
  }
}



export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data: T
  meta?: {
    current_page: number
    per_page: number
    total: number
    total_pages: number
  }
}

export interface PaginationParams {
  page?: number
  per_page?: number
  search?: string
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

export interface DashboardStats {
  total_users: number
  total_faculties: number
  total_study_programs: number
  total_courses: number
  total_cpl: number
  total_cpmk: number
  active_assessments: number
  recent_activities: Activity[]
}

export interface Activity {
  id: number
  user_id: number
  user_name: string
  action: string
  resource_type: string
  resource_id: number
  resource_name: string
  description: string
  created_at: string
}

export interface ChartData {
  labels: string[]
  datasets: {
    label: string
    data: number[]
    backgroundColor?: string | string[]
    borderColor?: string | string[]
    borderWidth?: number
  }[]
}
