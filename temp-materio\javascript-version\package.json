{"name": "materio-vuetify-vuejs-admin-template-free", "version": "2.3.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview --port 5050", "lint": "eslint . -c .eslintrc.cjs --fix --ext .ts,.js,.cjs,.vue,.tsx,.jsx", "build:icons": "tsx src/plugins/iconify/build-icons.js", "postinstall": "npm run build:icons"}, "dependencies": {"@floating-ui/dom": "1.6.8", "@iconify-json/bxl": "^1.1.10", "@stylistic/stylelint-config": "^1.0.1", "@vueuse/core": "^10.11.1", "@vueuse/math": "^10.11.1", "apexcharts": "^3.54.1", "pinia": "^2.3.0", "prismjs": "^1.29.0", "roboto-fontface": "^0.10.0", "vue": "^3.5.13", "vue-flatpickr-component": "11.0.5", "vue-prism-component": "^2.0.0", "vue-router": "^4.5.0", "vue3-apexcharts": "1.5.2", "vue3-perfect-scrollbar": "^2.0.0", "vuetify": "3.7.5", "webfontloader": "^1.6.28"}, "devDependencies": {"@antfu/eslint-config-vue": "^0.43.1", "@antfu/utils": "^0.7.10", "@iconify-json/ri": "^1.2.1", "@iconify/tools": "^4.0.7", "@iconify/utils": "^2.1.13", "@iconify/vue": "4.1.2", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "eslint": "^8.57.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-case-police": "^0.6.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-promise": "^6.6.0", "eslint-plugin-regex": "^1.10.0", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-sonarjs": "^0.24.0", "eslint-plugin-unicorn": "^51.0.1", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-vue": "^9.32.0", "postcss-html": "^1.7.0", "postcss-scss": "^4.0.9", "sass": "~1.76.0", "stylelint": "16.8.0", "stylelint-codeguide": "2.0.0", "stylelint-config-idiomatic-order": "10.0.0", "stylelint-config-standard-scss": "13.1.0", "stylelint-use-logical-spec": "5.0.1", "tsx": "^4.19.2", "unplugin-auto-import": "^0.18.6", "unplugin-vue-components": "^0.27.5", "vite": "^5.4.11", "vite-plugin-vue-layouts": "^0.11.0", "vite-plugin-vuetify": "2.0.3", "vite-svg-loader": "^5.1.0"}, "resolutions": {"postcss": "^8", "stylelint-order": "6.0.3", "@types/video.js": "^7"}, "overrides": {"postcss": "^8", "stylelint-order": "6.0.3", "@types/video.js": "^7"}}