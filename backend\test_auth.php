<?php

// Simple test script for authentication
require_once 'vendor/autoload.php';

try {
    // Direct database connection to check users
    $pdo = new PDO('mysql:host=localhost;dbname=rpswebid', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== RPS Management System - Authentication Test ===\n\n";
    
    // Check existing users
    $stmt = $pdo->query("SELECT id, username, email, full_name, role_id, is_active FROM users LIMIT 5");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Existing users:\n";
    foreach ($users as $user) {
        echo "- ID: {$user['id']}, Username: {$user['username']}, Email: {$user['email']}, Role: {$user['role_id']}, Active: {$user['is_active']}\n";
    }
    
    // Check if we have a test user, if not create one
    $testUsername = 'admin';
    $testPassword = 'admin123';
    
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ?");
    $stmt->execute([$testUsername]);
    $testUser = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$testUser) {
        echo "\nCreating test user...\n";
        $hashedPassword = password_hash($testPassword, PASSWORD_DEFAULT);
        
        $stmt = $pdo->prepare("INSERT INTO users (username, email, password_hash, full_name, role_id, is_active) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute([
            $testUsername,
            '<EMAIL>',
            $hashedPassword,
            'System Administrator',
            1, // Admin role
            1  // Active
        ]);
        
        echo "Test user created: username='admin', password='admin123'\n";
    } else {
        echo "\nTest user already exists: username='admin'\n";
        
        // Update password to ensure we know it
        $hashedPassword = password_hash($testPassword, PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("UPDATE users SET password_hash = ? WHERE username = ?");
        $stmt->execute([$hashedPassword, $testUsername]);
        echo "Password updated for test user\n";
    }
    
    echo "\n=== Test Authentication API ===\n";
    echo "You can now test the authentication API:\n";
    echo "POST http://localhost:8080/api/v1/auth/login\n";
    echo "Body: {\"username\": \"admin\", \"password\": \"admin123\"}\n\n";
    
    echo "=== Test Frontend ===\n";
    echo "Frontend URL: http://localhost:8080/\n";
    echo "API Base URL: http://localhost:8080/api/v1/\n\n";
    
    // Test API endpoint with curl if available
    echo "Testing API endpoint...\n";
    
    $loginData = json_encode([
        'username' => $testUsername,
        'password' => $testPassword
    ]);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:8080/api/v1/auth/login');
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $loginData);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Content-Length: ' . strlen($loginData)
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($response !== false) {
        echo "API Response (HTTP $httpCode):\n";
        echo $response . "\n\n";
        
        if ($httpCode == 200) {
            echo "✅ Authentication API is working!\n";
        } else {
            echo "❌ Authentication API returned error code: $httpCode\n";
        }
    } else {
        echo "❌ Could not connect to API endpoint. Make sure the server is running.\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
