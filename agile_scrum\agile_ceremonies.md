# RPS Management System - Agile Ceremonies Guide

## 🎯 Ceremony Overview

**Scrum Master:** <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, MT  
**Team Size:** 5-7 members  
**Sprint Duration:** 2 weeks  
**Working Hours:** 08:00 - 17:00 WIB  

## 📅 Sprint Planning

### 🎯 Sprint Planning Meeting
**Duration:** 4 hours (2 hours per part)  
**Frequency:** Every 2 weeks (start of sprint)  
**Participants:** Product Owner, Scrum Master, Development Team  

#### Part 1: What Will We Do? (2 hours)
**Objective:** Select and commit to user stories for the sprint

**Agenda:**
1. **Sprint Goal Definition** (30 min)
   - Review product vision and roadmap
   - Define clear, achievable sprint goal
   - Align team understanding

2. **Product Backlog Review** (45 min)
   - Review prioritized backlog items
   - Clarify requirements and acceptance criteria
   - Identify dependencies and risks

3. **Capacity Planning** (30 min)
   - Confirm team availability
   - Account for holidays, training, meetings
   - Calculate available story points

4. **Story Selection** (15 min)
   - Select stories that fit sprint capacity
   - Ensure stories support sprint goal
   - Get team commitment

#### Part 2: How Will We Do It? (2 hours)
**Objective:** Break down selected stories into actionable tasks

**Agenda:**
1. **Story Breakdown** (90 min)
   - Decompose stories into technical tasks
   - Identify implementation approach
   - Estimate task hours

2. **Task Assignment** (20 min)
   - Assign tasks to team members
   - Balance workload across team
   - Identify collaboration needs

3. **Sprint Backlog Creation** (10 min)
   - Finalize sprint backlog
   - Set up tracking tools
   - Confirm sprint commitment

### 📋 Sprint Planning Template

```markdown
## Sprint X Planning Summary

**Sprint Dates:** [Start Date] - [End Date]
**Sprint Goal:** [One sentence describing sprint objective]

### Team Capacity
- **Available Days:** X days
- **Team Velocity:** X story points
- **Planned Capacity:** X story points

### Selected User Stories
| Story ID | Title | Points | Owner | Dependencies |
|----------|-------|--------|-------|--------------|
| US-XXX | Story Title | X | Name | None/Details |

### Sprint Risks
1. **Risk:** Description
   **Mitigation:** Action plan

### Sprint Commitment
Team commits to delivering [X] story points focused on [sprint goal].
```

## 🏃‍♂️ Daily Standup

### 📊 Daily Scrum Meeting
**Duration:** 15 minutes  
**Frequency:** Daily (Monday-Friday)  
**Time:** 09:00 WIB  
**Participants:** Development Team, Scrum Master (Product Owner optional)  

#### Three Questions Format
Each team member answers:
1. **What did I complete yesterday?**
   - Specific tasks/stories completed
   - Any blockers resolved

2. **What will I work on today?**
   - Planned tasks for today
   - Expected outcomes

3. **What impediments am I facing?**
   - Current blockers
   - Help needed from team

#### Standup Rules
- **Start on time:** No waiting for latecomers
- **Stay focused:** Avoid detailed discussions
- **Keep it brief:** 2-3 minutes per person
- **Update board:** Reflect current status
- **Raise impediments:** Don't solve them in standup

#### Impediment Tracking
| Date | Impediment | Owner | Status | Resolution |
|------|------------|-------|--------|------------|
| 2025-01-25 | Database access issue | Dev1 | Open | Contact IT |

### 📈 Daily Metrics
- **Burndown progress:** Story points remaining
- **Task completion:** Tasks done vs planned
- **Impediment count:** Active blockers
- **Team mood:** Daily satisfaction rating

## 🔍 Sprint Review

### 🎭 Sprint Review Meeting
**Duration:** 2 hours  
**Frequency:** End of each sprint  
**Participants:** Product Owner, Scrum Master, Development Team, Stakeholders  

#### Agenda Structure
1. **Sprint Overview** (15 min)
   - Sprint goal review
   - Planned vs actual delivery
   - Key metrics summary

2. **Demo Session** (75 min)
   - Live demonstration of completed features
   - Stakeholder interaction and feedback
   - Q&A session

3. **Stakeholder Feedback** (20 min)
   - Collect feedback on demonstrated features
   - Identify new requirements or changes
   - Prioritize feedback for product backlog

4. **Metrics Review** (10 min)
   - Velocity and burndown analysis
   - Quality metrics (defects, rework)
   - Team performance indicators

#### Demo Guidelines
- **Show working software:** Live demo, not slides
- **Focus on business value:** Highlight user benefits
- **Encourage interaction:** Let stakeholders try features
- **Collect feedback:** Document all input for backlog

### 📊 Sprint Review Template

```markdown
## Sprint X Review Summary

**Sprint Goal:** [Goal statement]
**Achievement:** [Met/Partially Met/Not Met]

### Completed Stories
| Story ID | Title | Demo Status | Stakeholder Feedback |
|----------|-------|-------------|---------------------|
| US-XXX | Story Title | ✅ Demoed | Positive/Concerns |

### Sprint Metrics
- **Planned Points:** X
- **Completed Points:** X
- **Velocity:** X points
- **Defects Found:** X
- **Stakeholder Satisfaction:** X/5

### Key Feedback
1. **Positive Feedback:**
   - Feature X was well-received
   - UI improvements appreciated

2. **Improvement Areas:**
   - Performance needs optimization
   - Additional validation required

### Action Items
- [ ] Update backlog with new requirements
- [ ] Schedule follow-up for feature Y
- [ ] Address performance concerns
```

## 🔄 Sprint Retrospective

### 🤔 Sprint Retrospective Meeting
**Duration:** 1.5 hours  
**Frequency:** End of each sprint (after review)  
**Participants:** Development Team, Scrum Master  

#### Retrospective Formats

##### Format 1: Start-Stop-Continue (45 min)
1. **Start** (15 min): What should we start doing?
2. **Stop** (15 min): What should we stop doing?
3. **Continue** (15 min): What should we continue doing?

##### Format 2: Glad-Sad-Mad (45 min)
1. **Glad** (15 min): What made us happy?
2. **Sad** (15 min): What disappointed us?
3. **Mad** (15 min): What frustrated us?

##### Format 3: 4Ls (60 min)
1. **Liked** (15 min): What went well?
2. **Learned** (15 min): What did we learn?
3. **Lacked** (15 min): What was missing?
4. **Longed For** (15 min): What do we wish for?

#### Action Item Generation (30 min)
1. **Prioritize Issues** (15 min)
   - Vote on most important items
   - Focus on top 3-5 issues

2. **Create Action Items** (15 min)
   - Define specific, actionable improvements
   - Assign owners and deadlines
   - Set success criteria

#### Retrospective Rules
- **Safe environment:** No blame, focus on improvement
- **Everyone participates:** All voices heard
- **Be specific:** Concrete examples and actions
- **Time-boxed:** Stick to allocated time
- **Follow through:** Track action items

### 📋 Retrospective Template

```markdown
## Sprint X Retrospective

**Date:** [Date]
**Participants:** [Team members]
**Format:** [Chosen format]

### What Went Well
- Item 1: Specific example
- Item 2: Specific example

### What Could Be Improved
- Issue 1: Description and impact
- Issue 2: Description and impact

### Action Items
| Action | Owner | Deadline | Success Criteria |
|--------|-------|----------|------------------|
| Action 1 | Name | Date | Measurable outcome |
| Action 2 | Name | Date | Measurable outcome |

### Previous Action Items Review
| Action | Status | Notes |
|--------|--------|-------|
| Previous action 1 | ✅ Done | Successful |
| Previous action 2 | 🔄 In Progress | On track |

### Team Mood
**Overall Satisfaction:** X/5
**Team Confidence:** X/5
**Process Satisfaction:** X/5
```

## 📊 Backlog Refinement

### 🔧 Backlog Refinement Session
**Duration:** 1 hour  
**Frequency:** Weekly (mid-sprint)  
**Participants:** Product Owner, Scrum Master, Development Team  

#### Refinement Activities
1. **Story Review** (20 min)
   - Review upcoming stories
   - Clarify requirements
   - Update acceptance criteria

2. **Estimation** (20 min)
   - Estimate new stories
   - Re-estimate changed stories
   - Validate story size

3. **Prioritization** (15 min)
   - Adjust story priorities
   - Consider dependencies
   - Align with business goals

4. **Preparation** (5 min)
   - Ensure stories are ready
   - Identify missing information
   - Plan next refinement

#### Refinement Checklist
- [ ] Stories have clear acceptance criteria
- [ ] Business value is understood
- [ ] Technical approach is clear
- [ ] Dependencies are identified
- [ ] Stories are appropriately sized
- [ ] Priorities are current

## 📈 Metrics and Tracking

### Key Performance Indicators
1. **Velocity:** Story points completed per sprint
2. **Burndown:** Work remaining over time
3. **Cycle Time:** Time from start to completion
4. **Lead Time:** Time from request to delivery
5. **Defect Rate:** Bugs per story point
6. **Team Satisfaction:** Retrospective feedback

### Tracking Tools
- **Jira/Azure DevOps:** Story and task tracking
- **Burndown Charts:** Progress visualization
- **Velocity Charts:** Team performance trends
- **Cumulative Flow:** Work in progress analysis

### Reporting Schedule
- **Daily:** Burndown update
- **Weekly:** Velocity tracking
- **Sprint End:** Comprehensive metrics review
- **Monthly:** Trend analysis and improvement planning

---

**Note:** Ceremonies akan disesuaikan berdasarkan kebutuhan tim dan feedback dari retrospective. Fokus utama adalah pada continuous improvement dan delivery value kepada stakeholder.
