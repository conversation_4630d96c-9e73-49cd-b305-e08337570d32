# RPS Management System - Sprint Planning Guide

## 🏃‍♂️ Sprint Framework Overview

**Sprint Duration:** 2 weeks  
**Team Velocity:** 30 story points per sprint (estimated)  
**Sprint Ceremonies:**
- Sprint Planning: 4 hours (start of sprint)
- Daily Standups: 15 minutes (daily)
- Sprint Review: 2 hours (end of sprint)
- Sprint Retrospective: 1.5 hours (end of sprint)

## 📅 Sprint Calendar

### Sprint 0: Project Foundation (Week 1-2)
**Dates:** January 25 - February 7, 2025  
**Goal:** Establish development environment dan project foundation

#### Sprint 0 Objectives
- [ ] Development environment setup
- [ ] Team onboarding dan role assignment
- [ ] Architecture finalization
- [ ] Database design implementation
- [ ] CI/CD pipeline setup
- [ ] Project management tools configuration

#### Sprint 0 Deliverables
- Working development environment
- Database schema implemented
- Basic project structure
- Team communication channels established
- Definition of Done agreed upon

### Sprint 1: Authentication & Core Infrastructure (Week 3-4)
**Dates:** February 8 - February 21, 2025  
**Goal:** Implement secure authentication dan basic user management  
**Capacity:** 30 story points

#### Sprint 1 Backlog
| Story ID | Story Title | Story Points | Assignee | Status |
|----------|-------------|--------------|----------|---------|
| US-001 | User Login System | 8 | Backend Dev | Ready |
| US-002 | Role-Based Access Control | 13 | Backend Dev | Ready |
| US-003 | User Profile Management | 5 | Frontend Dev | Ready |
| US-022 | Academic Year Management | 5 | Backend Dev | Ready |

#### Sprint 1 Definition of Done
- [ ] All user stories meet acceptance criteria
- [ ] Unit tests written dan passing (>80% coverage)
- [ ] Integration tests passing
- [ ] Code reviewed dan approved
- [ ] Security review completed
- [ ] API documentation updated
- [ ] Frontend components responsive

### Sprint 2: Master Data Management (Week 5-6)
**Dates:** February 22 - March 7, 2025  
**Goal:** Implement foundational master data management  
**Capacity:** 30 story points

#### Sprint 2 Backlog
| Story ID | Story Title | Story Points | Assignee | Status |
|----------|-------------|--------------|----------|---------|
| US-004 | Admin User Management | 8 | Backend Dev | Ready |
| US-005 | Faculty Management | 5 | Backend Dev | Ready |
| US-006 | Study Program Management | 8 | Backend Dev | Ready |
| US-020 | Course Class Management | 8 | Frontend Dev | Ready |

#### Sprint 2 Goals
- Complete user management functionality
- Establish organizational hierarchy
- Enable basic administrative operations
- Prepare foundation for course management

### Sprint 3: Course Management (Week 7-8)
**Dates:** March 8 - March 21, 2025  
**Goal:** Implement comprehensive course management system  
**Capacity:** 30 story points

#### Sprint 3 Backlog
| Story ID | Story Title | Story Points | Assignee | Status |
|----------|-------------|--------------|----------|---------|
| US-007 | Course CRUD Operations | 13 | Backend Dev | Ready |
| US-008 | Course Reference Management | 8 | Backend Dev | Ready |
| US-009 | Course Topic Planning | 8 | Frontend Dev | Ready |

#### Sprint 3 Goals
- Enable complete course lifecycle management
- Implement course reference system
- Create weekly topic planning interface
- Establish course-program relationships

### Sprint 4: CPMK & CPL Foundation (Week 9-10)
**Dates:** March 22 - April 4, 2025  
**Goal:** Implement learning outcomes management  
**Capacity:** 30 story points

#### Sprint 4 Backlog
| Story ID | Story Title | Story Points | Assignee | Status |
|----------|-------------|--------------|----------|---------|
| US-010 | CPL Management | 13 | Backend Dev | Ready |
| US-011 | CPMK Management | 13 | Backend Dev | Ready |
| US-015 | Sub-CPMK Management | 8 | Frontend Dev | Ready |

#### Sprint 4 Goals
- Implement CPL management system
- Create CPMK management functionality
- Enable sub-CPMK breakdown
- Establish learning outcome hierarchy

### Sprint 5: Assessment & Integration (Week 11-12)
**Dates:** April 5 - April 18, 2025  
**Goal:** Complete assessment system dan CPMK-CPL integration  
**Capacity:** 30 story points

#### Sprint 5 Backlog
| Story ID | Story Title | Story Points | Assignee | Status |
|----------|-------------|--------------|----------|---------|
| US-012 | CPMK-CPL Mapping | 13 | Backend Dev | Ready |
| US-013 | Assessment Method Management | 8 | Backend Dev | Ready |
| US-014 | Assessment Planning | 13 | Frontend Dev | Ready |

#### Sprint 5 Goals
- Complete CPMK-CPL mapping functionality
- Implement assessment planning system
- Enable comprehensive evaluation framework
- Prepare for reporting dan analytics

## 🎯 Sprint Planning Process

### Pre-Sprint Planning (1 week before)
1. **Backlog Refinement**
   - Review dan update user stories
   - Estimate story points for new items
   - Clarify acceptance criteria
   - Identify dependencies

2. **Capacity Planning**
   - Confirm team availability
   - Account for holidays/leave
   - Adjust velocity if needed
   - Prepare sprint goal

### Sprint Planning Meeting (4 hours)

#### Part 1: What (2 hours)
- Review sprint goal
- Select user stories from product backlog
- Confirm story points dan acceptance criteria
- Identify risks dan dependencies

#### Part 2: How (2 hours)
- Break down user stories into tasks
- Estimate task hours
- Assign tasks to team members
- Create sprint backlog
- Confirm sprint commitment

### Sprint Planning Template

```markdown
## Sprint X Planning

**Sprint Goal:** [Clear, concise goal statement]
**Sprint Dates:** [Start date] - [End date]
**Team Capacity:** [Available hours/story points]

### Selected User Stories
| Story ID | Title | Points | Owner | Notes |
|----------|-------|--------|-------|-------|
| US-XXX | Story Title | X | Name | Dependencies/Risks |

### Sprint Risks
- Risk 1: Description dan mitigation
- Risk 2: Description dan mitigation

### Definition of Done Checklist
- [ ] All acceptance criteria met
- [ ] Unit tests written dan passing
- [ ] Integration tests passing
- [ ] Code reviewed
- [ ] Documentation updated
```

## 📊 Sprint Metrics & Tracking

### Key Metrics
1. **Velocity**: Story points completed per sprint
2. **Burndown**: Remaining work over time
3. **Cycle Time**: Time from start to completion
4. **Defect Rate**: Bugs found per story point
5. **Team Satisfaction**: Retrospective feedback

### Daily Tracking
- **Burndown Chart**: Updated daily
- **Task Board**: Kanban-style tracking
- **Impediment Log**: Blockers dan resolutions
- **Time Tracking**: Actual vs estimated hours

### Sprint Review Metrics
- Planned vs completed story points
- Quality metrics (defects, rework)
- Stakeholder feedback scores
- Demo success rate

## 🔄 Sprint Ceremonies

### Daily Standup (15 minutes)
**Format:**
- What did I complete yesterday?
- What will I work on today?
- What impediments am I facing?

**Rules:**
- Start on time
- Keep it focused
- Raise impediments, don't solve them
- Update task board

### Sprint Review (2 hours)
**Agenda:**
1. Sprint goal review (10 min)
2. Demo completed features (60 min)
3. Stakeholder feedback (30 min)
4. Metrics review (15 min)
5. Next sprint preview (5 min)

### Sprint Retrospective (1.5 hours)
**Format:**
1. What went well? (30 min)
2. What could be improved? (30 min)
3. Action items identification (20 min)
4. Action items prioritization (10 min)

## 📋 Sprint Checklist

### Sprint Start
- [ ] Sprint planning completed
- [ ] Sprint goal defined dan communicated
- [ ] Sprint backlog created
- [ ] Team capacity confirmed
- [ ] Risks identified dan mitigated
- [ ] Definition of Done reviewed

### During Sprint
- [ ] Daily standups conducted
- [ ] Progress tracked dan updated
- [ ] Impediments addressed promptly
- [ ] Quality gates maintained
- [ ] Stakeholder communication maintained

### Sprint End
- [ ] Sprint review conducted
- [ ] Demo delivered successfully
- [ ] Stakeholder feedback collected
- [ ] Sprint retrospective completed
- [ ] Action items identified dan assigned
- [ ] Next sprint prepared

## 🎯 Success Criteria

### Sprint Level
- All committed user stories completed
- Sprint goal achieved
- Quality standards maintained
- Team satisfaction > 4/5
- Stakeholder satisfaction > 4/5

### Release Level
- All must-have features delivered
- Performance benchmarks met
- Security requirements satisfied
- User acceptance criteria met
- Production readiness achieved

---

**Note:** Sprint planning akan disesuaikan berdasarkan team velocity dan feedback dari retrospective. Fleksibilitas dalam planning tetap dijaga untuk mengakomodasi perubahan requirements atau prioritas.
