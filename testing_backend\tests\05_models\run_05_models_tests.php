<?php

/**
 * Model Functionality Tests
 * 
 * Tests CRUD operations, data validation, relationships, and business logic
 */

// Ensure we have access to the test runner
if (!isset($testRunner)) {
    die("This test must be run through the TestRunner\n");
}

$testRunner->logSubSection('Model Functionality Tests');

$tests = [
    'model_files' => 'Model Files Existence',
    'database_connection' => 'Database Connection for Models',
    'user_model' => 'User Model Functionality',
    'model_validation' => 'Model Validation Rules',
    'model_relationships' => 'Model Relationships',
    'crud_operations' => 'CRUD Operations',
    'data_integrity' => 'Data Integrity Checks'
];

$totalTests = count($tests);
$currentTest = 0;
$passedTests = 0;

foreach ($tests as $testKey => $testName) {
    $currentTest++;
    $testRunner->logProgress($currentTest, $totalTests, "Running {$testName}");
    
    $startTime = microtime(true);
    $testPassed = false;
    $message = '';
    $details = [];

    try {
        switch ($testKey) {
            case 'model_files':
                // Check if model files exist
                $modelPath = __DIR__ . '/../../../backend/app/Models/';
                $expectedModels = [
                    'UserModel.php',
                    // Add other expected models here when they're created
                ];
                
                $existingModels = [];
                $missingModels = [];
                
                foreach ($expectedModels as $model) {
                    $filePath = $modelPath . $model;
                    if (file_exists($filePath)) {
                        $existingModels[] = $model;
                    } else {
                        $missingModels[] = $model;
                    }
                }
                
                // Also check for any other model files
                if (is_dir($modelPath)) {
                    $allFiles = scandir($modelPath);
                    $otherModels = array_filter($allFiles, function($file) use ($expectedModels) {
                        return str_ends_with($file, 'Model.php') && !in_array($file, $expectedModels);
                    });
                    $existingModels = array_merge($existingModels, $otherModels);
                }
                
                $testPassed = !empty($existingModels);
                $message = $testPassed 
                    ? "Model files found: " . implode(', ', $existingModels)
                    : "No model files found";
                $details = [
                    'model_path' => $modelPath,
                    'existing_models' => $existingModels,
                    'missing_models' => $missingModels,
                    'total_models' => count($existingModels)
                ];
                break;

            case 'database_connection':
                // Test database connection for model operations
                try {
                    $dbConfig = $config['database'];
                    $dsn = "pgsql:host={$dbConfig['hostname']};port={$dbConfig['port']};dbname={$dbConfig['database']}";
                    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
                        PDO::ATTR_TIMEOUT => 5,
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
                    ]);
                    
                    // Test a simple query
                    $stmt = $pdo->query("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public'");
                    $tableCount = $stmt->fetchColumn();
                    
                    $testPassed = $tableCount > 0;
                    $message = $testPassed 
                        ? "Database connection successful ({$tableCount} tables found)"
                        : "Database connection failed or no tables found";
                    $details = [
                        'table_count' => $tableCount,
                        'connection_successful' => true
                    ];
                    
                } catch (PDOException $e) {
                    $testPassed = false;
                    $message = "Database connection failed: " . $e->getMessage();
                    $details = [
                        'error' => $e->getMessage(),
                        'connection_successful' => false
                    ];
                }
                break;

            case 'user_model':
                // Test UserModel functionality
                try {
                    // Check if we can access the UserModel
                    $userModelPath = __DIR__ . '/../../../backend/app/Models/UserModel.php';
                    
                    if (!file_exists($userModelPath)) {
                        $testPassed = false;
                        $message = "UserModel.php file not found";
                        $details = ['file_path' => $userModelPath];
                        break;
                    }
                    
                    // Read and analyze the UserModel file
                    $modelContent = file_get_contents($userModelPath);
                    
                    // Check for required properties
                    $requiredProperties = [
                        'protected $table',
                        'protected $primaryKey',
                        'protected $allowedFields',
                        'protected $validationRules'
                    ];
                    
                    $foundProperties = [];
                    $missingProperties = [];
                    
                    foreach ($requiredProperties as $property) {
                        if (strpos($modelContent, $property) !== false) {
                            $foundProperties[] = $property;
                        } else {
                            $missingProperties[] = $property;
                        }
                    }
                    
                    $testPassed = empty($missingProperties);
                    $message = $testPassed 
                        ? "UserModel has all required properties"
                        : "UserModel missing properties: " . implode(', ', $missingProperties);
                    $details = [
                        'found_properties' => $foundProperties,
                        'missing_properties' => $missingProperties,
                        'file_size' => filesize($userModelPath)
                    ];
                    
                } catch (Exception $e) {
                    $testPassed = false;
                    $message = "UserModel test failed: " . $e->getMessage();
                    $details = ['error' => $e->getMessage()];
                }
                break;

            case 'model_validation':
                // Test model validation rules
                try {
                    $userModelPath = __DIR__ . '/../../../backend/app/Models/UserModel.php';
                    
                    if (!file_exists($userModelPath)) {
                        $testPassed = false;
                        $message = "UserModel.php file not found for validation test";
                        break;
                    }
                    
                    $modelContent = file_get_contents($userModelPath);
                    
                    // Check for validation rules
                    $validationChecks = [
                        'username' => 'required',
                        'email' => 'valid_email',
                        'password' => 'min_length'
                    ];
                    
                    $foundValidations = [];
                    $missingValidations = [];
                    
                    foreach ($validationChecks as $field => $rule) {
                        if (strpos($modelContent, "'{$field}'") !== false && strpos($modelContent, $rule) !== false) {
                            $foundValidations[] = "{$field} -> {$rule}";
                        } else {
                            $missingValidations[] = "{$field} -> {$rule}";
                        }
                    }
                    
                    $testPassed = count($foundValidations) >= 2; // At least 2 validation rules
                    $message = $testPassed 
                        ? "Model validation rules found: " . count($foundValidations)
                        : "Insufficient validation rules found";
                    $details = [
                        'found_validations' => $foundValidations,
                        'missing_validations' => $missingValidations
                    ];
                    
                } catch (Exception $e) {
                    $testPassed = false;
                    $message = "Model validation test failed: " . $e->getMessage();
                    $details = ['error' => $e->getMessage()];
                }
                break;

            case 'model_relationships':
                // Test if models have relationship methods
                try {
                    $userModelPath = __DIR__ . '/../../../backend/app/Models/UserModel.php';
                    
                    if (!file_exists($userModelPath)) {
                        $testPassed = false;
                        $message = "UserModel.php file not found for relationship test";
                        break;
                    }
                    
                    $modelContent = file_get_contents($userModelPath);
                    
                    // Look for relationship patterns
                    $relationshipPatterns = [
                        'hasMany' => 'public function.*hasMany',
                        'belongsTo' => 'public function.*belongsTo',
                        'hasOne' => 'public function.*hasOne'
                    ];
                    
                    $foundRelationships = [];
                    
                    foreach ($relationshipPatterns as $type => $pattern) {
                        if (preg_match("/{$pattern}/i", $modelContent)) {
                            $foundRelationships[] = $type;
                        }
                    }
                    
                    // For now, we'll pass if the model exists and is properly structured
                    $testPassed = strpos($modelContent, 'class UserModel') !== false;
                    $message = $testPassed 
                        ? "UserModel structure is valid (relationships: " . implode(', ', $foundRelationships) . ")"
                        : "UserModel structure is invalid";
                    $details = [
                        'found_relationships' => $foundRelationships,
                        'has_class_definition' => strpos($modelContent, 'class UserModel') !== false
                    ];
                    
                } catch (Exception $e) {
                    $testPassed = false;
                    $message = "Model relationship test failed: " . $e->getMessage();
                    $details = ['error' => $e->getMessage()];
                }
                break;

            case 'crud_operations':
                // Test basic CRUD operations on database
                try {
                    $dbConfig = $config['database'];
                    $dsn = "pgsql:host={$dbConfig['hostname']};port={$dbConfig['port']};dbname={$dbConfig['database']}";
                    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
                    ]);
                    
                    // Test if we can read from users table
                    $stmt = $pdo->query("SELECT COUNT(*) FROM users LIMIT 1");
                    $userCount = $stmt->fetchColumn();
                    
                    // Test if we can read user data
                    $stmt = $pdo->query("SELECT id, username, email FROM users LIMIT 1");
                    $userData = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    $testPassed = $userCount !== false && $userData !== false;
                    $message = $testPassed 
                        ? "CRUD operations possible (found {$userCount} users)"
                        : "CRUD operations failed";
                    $details = [
                        'user_count' => $userCount,
                        'sample_user' => $userData ? array_keys($userData) : null,
                        'can_read' => $userData !== false
                    ];
                    
                } catch (PDOException $e) {
                    $testPassed = false;
                    $message = "CRUD operations test failed: " . $e->getMessage();
                    $details = ['error' => $e->getMessage()];
                }
                break;

            case 'data_integrity':
                // Test data integrity constraints
                try {
                    $dbConfig = $config['database'];
                    $dsn = "pgsql:host={$dbConfig['hostname']};port={$dbConfig['port']};dbname={$dbConfig['database']}";
                    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
                    ]);
                    
                    // Check for foreign key constraints
                    $stmt = $pdo->query("
                        SELECT COUNT(*) as fk_count 
                        FROM information_schema.table_constraints 
                        WHERE constraint_type = 'FOREIGN KEY'
                    ");
                    $fkCount = $stmt->fetchColumn();
                    
                    // Check for unique constraints
                    $stmt = $pdo->query("
                        SELECT COUNT(*) as unique_count 
                        FROM information_schema.table_constraints 
                        WHERE constraint_type = 'UNIQUE'
                    ");
                    $uniqueCount = $stmt->fetchColumn();
                    
                    // Check for primary key constraints
                    $stmt = $pdo->query("
                        SELECT COUNT(*) as pk_count 
                        FROM information_schema.table_constraints 
                        WHERE constraint_type = 'PRIMARY KEY'
                    ");
                    $pkCount = $stmt->fetchColumn();
                    
                    $testPassed = $fkCount > 0 && $pkCount > 0;
                    $message = $testPassed 
                        ? "Data integrity constraints found (FK: {$fkCount}, PK: {$pkCount}, Unique: {$uniqueCount})"
                        : "Insufficient data integrity constraints";
                    $details = [
                        'foreign_keys' => $fkCount,
                        'primary_keys' => $pkCount,
                        'unique_constraints' => $uniqueCount,
                        'total_constraints' => $fkCount + $pkCount + $uniqueCount
                    ];
                    
                } catch (PDOException $e) {
                    $testPassed = false;
                    $message = "Data integrity test failed: " . $e->getMessage();
                    $details = ['error' => $e->getMessage()];
                }
                break;
        }

    } catch (Exception $e) {
        $testPassed = false;
        $message = "Exception: " . $e->getMessage();
        $details = [
            'exception' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ];
    }

    $duration = round((microtime(true) - $startTime) * 1000);
    $status = $testPassed ? 'PASS' : 'FAIL';
    
    $testRunner->logTestResult($testName, $status, $message, $duration, $details);
    
    if ($testPassed) {
        $passedTests++;
    }
}

// Summary for this test category
$testRunner->logSubSection('Model Tests Summary');
$testRunner->log('info', "Passed: {$passedTests}/{$totalTests} tests", 'SUMMARY');

if ($passedTests >= $totalTests * 0.7) { // 70% success rate for model tests
    $testRunner->logSuccess("Model functionality tests passed! Models are properly structured.");
    return true;
} else {
    $failedTests = $totalTests - $passedTests;
    $testRunner->logError("Model functionality tests failed! {$failedTests} test(s) need attention.");
    return false;
}
