<?php

echo "Testing MySQL Database Connection...\n";

// Load configuration from .env file (same as backend)
$config = [
    'hostname' => 'localhost',        // database.default.hostname
    'database' => 'rpswebid',         // database.default.database
    'username' => 'root',             // database.default.username
    'password' => '',                 // database.default.password
    'DBDriver' => 'MySQLi',           // database.default.DBDriver
    'port' => 3306,                   // database.default.port
    'charset' => 'utf8mb4',           // database.default.charset
    'DBCollat' => 'utf8mb4_general_ci' // database.default.DBCollat
];

try {
    // Test MySQLi connection
    echo "1. Testing MySQLi connection...\n";
    $mysqli = new mysqli($config['hostname'], $config['username'], $config['password'], $config['database'], $config['port']);
    
    if ($mysqli->connect_error) {
        echo "   MySQLi connection failed: " . $mysqli->connect_error . "\n";
    } else {
        echo "   MySQLi connection successful!\n";
        
        // Test a simple query
        $result = $mysqli->query("SELECT DATABASE() as current_db, VERSION() as version");
        if ($result) {
            $row = $result->fetch_assoc();
            echo "   Current database: " . $row['current_db'] . "\n";
            echo "   MySQL version: " . $row['version'] . "\n";
        }
        
        // Check tables
        $result = $mysqli->query("SHOW TABLES");
        if ($result) {
            echo "   Tables in database:\n";
            while ($row = $result->fetch_array()) {
                echo "     - " . $row[0] . "\n";
            }
        }
        
        $mysqli->close();
    }
    
    echo "\n2. Testing PDO connection...\n";
    $dsn = "mysql:host={$config['hostname']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    
    echo "   PDO connection successful!\n";
    
    // Test a simple query
    $stmt = $pdo->query("SELECT DATABASE() as current_db, VERSION() as version");
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   Current database: " . $row['current_db'] . "\n";
    echo "   MySQL version: " . $row['version'] . "\n";
    
    echo "\n✅ All database connections working!\n";
    
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
}
