<?php

namespace App\Controllers\Api;

use App\Models\UserModel;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

class AuthController extends BaseApiController
{
    protected $userModel;

    public function __construct()
    {
        parent::__construct();
        $this->userModel = new UserModel();
    }

    /**
     * User login
     */
    public function login()
    {
        // Validate request
        $validation = $this->validateRequest([
            'username' => 'required|min_length[3]',
            'password' => 'required|min_length[6]'
        ]);

        if ($validation) {
            return $validation;
        }

        $username = $this->request->getPost('username');
        $password = $this->request->getPost('password');
        $rememberMe = $this->request->getPost('remember_me', false);

        // Find user by username or email
        $user = $this->userModel->where('username', $username)
                                ->orWhere('email', $username)
                                ->first();

        if (!$user) {
            return $this->respondError('Invalid credentials', 401);
        }

        // Verify password
        if (!password_verify($password, $user['password_hash'])) {
            return $this->respondError('Invalid credentials', 401);
        }

        // Check if user is active
        if ($user['is_active'] != 1) {
            return $this->respondError('Account is not active', 403);
        }

        // Generate JWT token
        $token = $this->generateToken($user, $rememberMe);

        // Update last login
        $this->userModel->update($user['id'], [
            'last_login' => date('Y-m-d H:i:s')
        ]);

        // Remove password from response
        unset($user['password_hash']);

        return $this->respondSuccess([
            'user' => $user,
            'token' => $token,
            'expires_in' => $rememberMe ? 30 * 24 * 60 * 60 : 24 * 60 * 60 // 30 days or 1 day
        ], 'Login successful');
    }

    /**
     * User logout
     */
    public function logout()
    {
        // In a real application, you might want to blacklist the token
        // For now, we'll just return a success response
        return $this->respondSuccess(null, 'Logout successful');
    }

    /**
     * Refresh token
     */
    public function refresh()
    {
        $user = $this->getAuthenticatedUser();
        
        if (!$user) {
            return $this->respondUnauthorized('Invalid token');
        }

        // Get fresh user data
        $userData = $this->userModel->find($user->user_id);
        
        if (!$userData || $userData['is_active'] != 1) {
            return $this->respondUnauthorized('User not found or inactive');
        }

        // Generate new token
        $token = $this->generateToken($userData);

        // Remove password from response
        unset($userData['password_hash']);

        return $this->respondSuccess([
            'user' => $userData,
            'token' => $token,
            'expires_in' => 24 * 60 * 60 // 1 day
        ], 'Token refreshed successfully');
    }

    /**
     * Get user profile
     */
    public function profile()
    {
        $user = $this->getAuthenticatedUser();
        
        if (!$user) {
            return $this->respondUnauthorized('Invalid token');
        }

        // Get fresh user data
        $userData = $this->userModel->find($user->user_id);
        
        if (!$userData) {
            return $this->respondNotFound('User not found');
        }

        // Remove password from response
        unset($userData['password_hash']);

        return $this->respondSuccess($userData, 'Profile retrieved successfully');
    }

    /**
     * Generate JWT token
     */
    private function generateToken(array $user, bool $rememberMe = false): string
    {
        $key = getenv('JWT_SECRET') ?: 'your-secret-key-change-this-in-production';
        $issuedAt = time();
        $expirationTime = $issuedAt + ($rememberMe ? 30 * 24 * 60 * 60 : 24 * 60 * 60); // 30 days or 1 day

        $payload = [
            'iss' => base_url(), // Issuer
            'aud' => base_url(), // Audience
            'iat' => $issuedAt,  // Issued at
            'exp' => $expirationTime, // Expiration time
            'user_id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'role_id' => $user['role_id'],
            'nip' => $user['nip'] ?? null,
            'full_name' => $user['full_name']
        ];

        return JWT::encode($payload, $key, 'HS256');
    }
}
