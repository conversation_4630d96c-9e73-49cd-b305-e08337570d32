# =====================================================
# PYTHON DJANGO .GITIGNORE TEMPLATE
# =====================================================
# Optimized for Django backend development
# =====================================================

# =====================================================
# PYTHON SPECIFIC
# =====================================================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
.env.local
.env.development
.env.test
.env.production

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# =====================================================
# DJANGO SPECIFIC
# =====================================================

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Django migrations (optional - uncomment if needed)
# */migrations/*.py
# !*/migrations/__init__.py

# Django static files
/static/
/staticfiles/
/collected-static/

# Django media files
/media/
/uploads/

# Django secret key
secret_key.txt

# Django settings
settings/local.py
settings/production.py
settings/development.py

# =====================================================
# DJANGO REST FRAMEWORK
# =====================================================

# DRF
api_docs/
swagger-ui/

# =====================================================
# CELERY
# =====================================================

# Celery
celerybeat-schedule
celerybeat.pid

# =====================================================
# DATABASE
# =====================================================

# SQLite
*.sqlite3
*.db

# PostgreSQL
*.dump
*.backup

# MySQL
*.sql

# =====================================================
# CACHE & SESSION
# =====================================================

# Redis
dump.rdb

# Memcached
*.pid

# Django cache
django_cache/

# Session files
django_sessions/

# =====================================================
# LOGS
# =====================================================

# Application logs
*.log
logs/
log/
django.log
error.log
access.log

# =====================================================
# TESTING
# =====================================================

# Test databases
test_*.sqlite3
test.db

# Coverage
.coverage
htmlcov/
coverage.xml

# Pytest
.pytest_cache/

# =====================================================
# DEVELOPMENT TOOLS
# =====================================================

# Django Debug Toolbar
debug_toolbar/

# Django Extensions
django_extensions/

# IPython
.ipython/

# =====================================================
# VIRTUAL ENVIRONMENTS
# =====================================================

# Virtualenv
venv/
.venv/
env/
.env/
ENV/
env.bak/
venv.bak/

# Pipenv
Pipfile.lock

# Poetry
poetry.lock

# =====================================================
# IDE & EDITORS
# =====================================================

# PyCharm
.idea/
*.iml

# VS Code
.vscode/

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# =====================================================
# DEPLOYMENT
# =====================================================

# Docker
docker-compose.override.yml
.dockerignore

# Kubernetes
*.kubeconfig

# Heroku
.env.heroku
Procfile.local

# AWS
.aws/
.elasticbeanstalk/

# =====================================================
# SECURITY
# =====================================================

# SSL Certificates
*.pem
*.key
*.crt
*.csr

# API Keys & Secrets
secrets/
.secrets/
api_keys.py
credentials.json

# =====================================================
# BACKUP & TEMPORARY
# =====================================================

# Backup files
*.bak
*.backup
*.old
*.orig

# Temporary files
*.tmp
*.temp
tmp/
temp/

# =====================================================
# OPERATING SYSTEM
# =====================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~
.directory

# =====================================================
# MISC
# =====================================================

# Archive files
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# =====================================================
# END OF PYTHON DJANGO .GITIGNORE
# =====================================================
