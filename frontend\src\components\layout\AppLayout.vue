<template>
  <div>
    <!-- Show login page if not authenticated -->
    <router-view v-if="!authStore.isAuthenticated" />
    
    <!-- Show main layout if authenticated -->
    <div v-else>
      <!-- App Bar -->
      <v-app-bar
        :elevation="2"
        color="primary"
        dark
        app
      >
        <v-app-bar-nav-icon @click="drawer = !drawer" />
        
        <v-toolbar-title class="text-h6 font-weight-bold">
          {{ appTitle }}
        </v-toolbar-title>
        
        <v-spacer />
        
        <!-- Notifications -->
        <v-btn icon>
          <v-icon>mdi-bell</v-icon>
        </v-btn>
        
        <!-- Theme Toggle -->
        <v-btn icon @click="toggleTheme">
          <v-icon>{{ isDark ? 'mdi-weather-sunny' : 'mdi-weather-night' }}</v-icon>
        </v-btn>
        
        <!-- User Menu -->
        <v-menu offset-y>
          <template v-slot:activator="{ props }">
            <v-btn icon v-bind="props">
              <v-avatar size="32">
                <v-img
                  v-if="authStore.user?.avatar"
                  :src="authStore.user.avatar"
                  :alt="authStore.user?.full_name"
                />
                <v-icon v-else>mdi-account</v-icon>
              </v-avatar>
            </v-btn>
          </template>
          
          <v-list>
            <v-list-item>
              <v-list-item-title>{{ authStore.user?.full_name }}</v-list-item-title>
              <v-list-item-subtitle>{{ authStore.user?.email }}</v-list-item-subtitle>
            </v-list-item>
            
            <v-divider />
            
            <v-list-item @click="$router.push('/profile')">
              <v-list-item-prepend>
                <v-icon>mdi-account-circle</v-icon>
              </v-list-item-prepend>
              <v-list-item-title>Profile</v-list-item-title>
            </v-list-item>
            
            <v-list-item @click="logout">
              <v-list-item-prepend>
                <v-icon>mdi-logout</v-icon>
              </v-list-item-prepend>
              <v-list-item-title>Logout</v-list-item-title>
            </v-list-item>
          </v-list>
        </v-menu>
      </v-app-bar>
      
      <!-- Navigation Drawer -->
      <v-navigation-drawer
        v-model="drawer"
        app
        :rail="rail"
        @click="rail = false"
      >
        <v-list>
          <v-list-item
            prepend-avatar="/logo.png"
            :title="authStore.user?.full_name"
            :subtitle="authStore.user?.roles.join(', ')"
          >
            <template v-slot:append>
              <v-btn
                icon="mdi-chevron-left"
                variant="text"
                @click.stop="rail = !rail"
              />
            </template>
          </v-list-item>
        </v-list>
        
        <v-divider />
        
        <v-list density="compact" nav>
          <v-list-item
            v-for="item in navigationItems"
            :key="item.title"
            :to="item.to"
            :prepend-icon="item.icon"
            :title="item.title"
            :value="item.value"
            color="primary"
          />
        </v-list>
      </v-navigation-drawer>
      
      <!-- Main Content -->
      <v-main>
        <v-container fluid class="pa-4">
          <router-view />
        </v-container>
      </v-main>
      
      <!-- Loading Overlay -->
      <v-overlay
        v-model="authStore.loading"
        class="align-center justify-center"
      >
        <v-progress-circular
          color="primary"
          indeterminate
          size="64"
        />
      </v-overlay>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useTheme } from 'vuetify'
import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'

const theme = useTheme()
const authStore = useAuthStore()
const router = useRouter()

// State
const drawer = ref(true)
const rail = ref(false)

// Computed
const appTitle = computed(() => import.meta.env.VITE_APP_NAME || 'RPS Management System')
const isDark = computed(() => theme.global.name.value === 'dark')

// Navigation items based on user roles
const navigationItems = computed(() => {
  const items = [
    {
      title: 'Dashboard',
      icon: 'mdi-view-dashboard',
      to: '/dashboard',
      value: 'dashboard',
      roles: []
    },
    {
      title: 'Users',
      icon: 'mdi-account-group',
      to: '/users',
      value: 'users',
      roles: ['admin']
    },
    {
      title: 'Faculties',
      icon: 'mdi-domain',
      to: '/faculties',
      value: 'faculties',
      roles: ['admin', 'dekan', 'wakil_dekan']
    },
    {
      title: 'Study Programs',
      icon: 'mdi-school',
      to: '/study-programs',
      value: 'study-programs',
      roles: ['admin', 'dekan', 'wakil_dekan', 'kepala_prodi']
    },
    {
      title: 'Courses',
      icon: 'mdi-book-open-page-variant',
      to: '/courses',
      value: 'courses',
      roles: []
    },
    {
      title: 'CPL Management',
      icon: 'mdi-target',
      to: '/cpl',
      value: 'cpl',
      roles: ['admin', 'dekan', 'wakil_dekan', 'kepala_prodi']
    },
    {
      title: 'CPMK Management',
      icon: 'mdi-bullseye-arrow',
      to: '/cpmk',
      value: 'cpmk',
      roles: []
    },
    {
      title: 'Assessments',
      icon: 'mdi-clipboard-check',
      to: '/assessments',
      value: 'assessments',
      roles: []
    },
    {
      title: 'Reports',
      icon: 'mdi-chart-line',
      to: '/reports',
      value: 'reports',
      roles: []
    }
  ]
  
  // Filter items based on user roles
  return items.filter(item => {
    if (item.roles.length === 0) return true
    return item.roles.some(role => authStore.userRoles.includes(role))
  })
})

// Methods
const toggleTheme = () => {
  theme.global.name.value = theme.global.current.value.dark ? 'light' : 'dark'
}

const logout = async () => {
  await authStore.logout()
  router.push('/login')
}

// Lifecycle
onMounted(() => {
  // Set initial theme based on user preference or system
  const savedTheme = localStorage.getItem('theme')
  if (savedTheme) {
    theme.global.name.value = savedTheme
  } else {
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
    theme.global.name.value = prefersDark ? 'dark' : 'light'
  }
})

// Watch theme changes and save to localStorage
theme.global.name.value && localStorage.setItem('theme', theme.global.name.value)
</script>

<style scoped>
.v-navigation-drawer {
  border-right: 1px solid rgba(0, 0, 0, 0.12);
}

.v-app-bar {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.v-main {
  background-color: #f5f5f5;
}

.theme--dark .v-main {
  background-color: #121212;
}
</style>
