<!DOCTYPE html>
<html lang="en" class="light-style customizer-hide" dir="ltr" data-theme="theme-default" data-assets-path="<?= base_url('admin/assets/') ?>" data-template="vertical-menu-template-free">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    
    <title><?= isset($title) ? $title . ' - ' : '' ?>RPS Admin Dashboard</title>
    
    <meta name="description" content="RPS Admin Dashboard - Materio Template" />
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= base_url('admin/assets/img/favicon/favicon.ico') ?>" />
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Public+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap" rel="stylesheet" />
    
    <!-- Icons -->
    <link rel="stylesheet" href="<?= base_url('admin/assets/vendor/fonts/boxicons.css') ?>" />
    
    <!-- Core CSS -->
    <link rel="stylesheet" href="<?= base_url('admin/assets/vendor/css/core.css') ?>" class="template-customizer-core-css" />
    <link rel="stylesheet" href="<?= base_url('admin/assets/vendor/css/theme-default.css') ?>" class="template-customizer-theme-css" />
    <link rel="stylesheet" href="<?= base_url('admin/assets/css/demo.css') ?>" />
    
    <!-- Vendors CSS -->
    <link rel="stylesheet" href="<?= base_url('admin/assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.css') ?>" />
    
    <!-- Page CSS -->
    <link rel="stylesheet" href="<?= base_url('admin/assets/vendor/css/pages/page-auth.css') ?>" />
    <?= $this->renderSection('page_css') ?>
    
    <!-- Helpers -->
    <script src="<?= base_url('admin/assets/vendor/js/helpers.js') ?>"></script>
    
    <!-- Template customizer & Theme config files -->
    <script src="<?= base_url('admin/assets/js/config.js') ?>"></script>
</head>

<body>
    <!-- Content -->
    <div class="container-xxl">
        <div class="authentication-wrapper authentication-basic container-p-y">
            <div class="authentication-inner">
                
                <!-- Flash Messages -->
                <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible" role="alert">
                    <?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger alert-dismissible" role="alert">
                    <?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <?php if (session()->getFlashdata('warning')): ?>
                <div class="alert alert-warning alert-dismissible" role="alert">
                    <?= session()->getFlashdata('warning') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <?php if (session()->getFlashdata('info')): ?>
                <div class="alert alert-info alert-dismissible" role="alert">
                    <?= session()->getFlashdata('info') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <!-- Main Content -->
                <?= $this->renderSection('content') ?>
                
            </div>
        </div>
    </div>
    <!-- / Content -->
    
    <!-- Core JS -->
    <script src="<?= base_url('admin/assets/vendor/libs/jquery/jquery.js') ?>"></script>
    <script src="<?= base_url('admin/assets/vendor/libs/popper/popper.js') ?>"></script>
    <script src="<?= base_url('admin/assets/vendor/js/bootstrap.js') ?>"></script>
    <script src="<?= base_url('admin/assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.js') ?>"></script>
    
    <script src="<?= base_url('admin/assets/vendor/js/menu.js') ?>"></script>
    
    <!-- Main JS -->
    <script src="<?= base_url('admin/assets/js/main.js') ?>"></script>
    
    <!-- Page JS -->
    <?= $this->renderSection('page_js') ?>
    
</body>

</html>
