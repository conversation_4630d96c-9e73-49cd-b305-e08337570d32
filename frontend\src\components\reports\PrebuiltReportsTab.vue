<template>
  <div>
    <!-- Filter Section -->
    <v-row class="mb-6">
      <v-col cols="12" md="4">
        <v-select
          v-model="selectedCategory"
          :items="categoryOptions"
          label="Report Category"
          variant="outlined"
          prepend-inner-icon="mdi-tag"
          clearable
          @update:model-value="filterReports"
        />
      </v-col>
      
      <v-col cols="12" md="4">
        <v-select
          v-model="selectedType"
          :items="typeOptions"
          label="Report Type"
          variant="outlined"
          prepend-inner-icon="mdi-file-chart"
          clearable
          @update:model-value="filterReports"
        />
      </v-col>
      
      <v-col cols="12" md="4">
        <v-text-field
          v-model="searchQuery"
          label="Search Reports"
          variant="outlined"
          prepend-inner-icon="mdi-magnify"
          clearable
          @update:model-value="filterReports"
        />
      </v-col>
    </v-row>

    <!-- Pre-built Reports Grid -->
    <v-row>
      <v-col
        v-for="report in filteredReports"
        :key="report.id"
        cols="12" sm="6" md="4" lg="3"
      >
        <v-card class="h-100" variant="outlined">
          <v-card-text>
            <div class="d-flex align-center mb-3">
              <v-icon
                :color="getReportTypeColor(report.type)"
                size="32"
                class="mr-3"
              >
                {{ getReportTypeIcon(report.type) }}
              </v-icon>
              
              <div class="flex-grow-1">
                <h3 class="text-h6 font-weight-bold">{{ report.name }}</h3>
                <v-chip
                  :color="getCategoryColor(report.category)"
                  size="small"
                  variant="tonal"
                >
                  {{ formatCategory(report.category) }}
                </v-chip>
              </div>
            </div>
            
            <p class="text-body-2 text-medium-emphasis mb-4">
              {{ report.description }}
            </p>
            
            <div class="d-flex align-center justify-space-between mb-3">
              <div class="text-caption text-medium-emphasis">
                <v-icon size="small" class="mr-1">mdi-download</v-icon>
                {{ report.usage_count || 0 }} downloads
              </div>
              
              <div class="text-caption text-medium-emphasis">
                <v-icon size="small" class="mr-1">mdi-clock</v-icon>
                ~{{ report.estimated_time || '2-5' }} min
              </div>
            </div>
            
            <!-- Export Format Options -->
            <div class="mb-3">
              <div class="text-caption text-medium-emphasis mb-1">Export Formats:</div>
              <div class="d-flex gap-1">
                <v-chip
                  v-for="format in report.export_formats"
                  :key="format"
                  size="x-small"
                  variant="outlined"
                >
                  {{ format.toUpperCase() }}
                </v-chip>
              </div>
            </div>
          </v-card-text>
          
          <v-card-actions>
            <v-btn
              color="primary"
              variant="flat"
              size="small"
              @click="openGenerateDialog(report)"
              :loading="generatingReports.has(report.id)"
            >
              <v-icon start>mdi-play</v-icon>
              Generate
            </v-btn>
            
            <v-btn
              variant="text"
              size="small"
              @click="previewReport(report)"
            >
              <v-icon start>mdi-eye</v-icon>
              Preview
            </v-btn>
            
            <v-spacer />
            
            <v-menu>
              <template v-slot:activator="{ props }">
                <v-btn
                  icon
                  size="small"
                  variant="text"
                  v-bind="props"
                >
                  <v-icon>mdi-dots-vertical</v-icon>
                </v-btn>
              </template>
              
              <v-list>
                <v-list-item @click="scheduleReport(report)">
                  <v-list-item-title>Schedule</v-list-item-title>
                  <template #prepend>
                    <v-icon>mdi-calendar-clock</v-icon>
                  </template>
                </v-list-item>
                
                <v-list-item @click="shareReport(report)">
                  <v-list-item-title>Share</v-list-item-title>
                  <template #prepend>
                    <v-icon>mdi-share</v-icon>
                  </template>
                </v-list-item>
                
                <v-list-item @click="viewAnalytics(report)">
                  <v-list-item-title>Analytics</v-list-item-title>
                  <template #prepend>
                    <v-icon>mdi-chart-bar</v-icon>
                  </template>
                </v-list-item>
              </v-list>
            </v-menu>
          </v-card-actions>
        </v-card>
      </v-col>
    </v-row>

    <!-- Empty State -->
    <div v-if="filteredReports.length === 0" class="text-center py-12">
      <v-icon size="64" color="grey-lighten-2">mdi-file-search</v-icon>
      <p class="text-h6 text-medium-emphasis mt-4">No Reports Found</p>
      <p class="text-body-2 text-medium-emphasis">
        Try adjusting your filters or search criteria
      </p>
    </div>

    <!-- Generate Report Dialog -->
    <v-dialog v-model="showGenerateDialog" max-width="600">
      <v-card>
        <v-card-title class="d-flex align-center">
          <v-icon class="mr-2">{{ getReportTypeIcon(selectedReport?.type) }}</v-icon>
          Generate {{ selectedReport?.name }}
        </v-card-title>
        
        <v-divider />
        
        <v-card-text>
          <ReportParametersForm
            v-if="selectedReport"
            :report="selectedReport"
            v-model="reportParameters"
          />
        </v-card-text>
        
        <v-card-actions>
          <v-spacer />
          <v-btn @click="showGenerateDialog = false">Cancel</v-btn>
          <v-btn
            color="primary"
            :loading="generating"
            @click="generateReport"
          >
            Generate Report
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Preview Dialog -->
    <v-dialog v-model="showPreviewDialog" max-width="1000" scrollable>
      <v-card>
        <v-card-title class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <v-icon class="mr-2">mdi-eye</v-icon>
            Preview: {{ selectedReport?.name }}
          </div>
          <v-btn icon variant="text" @click="showPreviewDialog = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>
        
        <v-divider />
        
        <v-card-text>
          <ReportPreview
            v-if="selectedReport"
            :report="selectedReport"
          />
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- Success/Error Snackbars -->
    <v-snackbar
      v-model="showSuccess"
      color="success"
      timeout="3000"
    >
      {{ successMessage }}
      <template v-slot:actions>
        <v-btn @click="showSuccess = false">Close</v-btn>
      </template>
    </v-snackbar>

    <v-snackbar
      v-model="showError"
      color="error"
      timeout="5000"
    >
      {{ errorMessage }}
      <template v-slot:actions>
        <v-btn @click="showError = false">Close</v-btn>
      </template>
    </v-snackbar>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import ReportParametersForm from '@/components/reports/ReportParametersForm.vue'
import ReportPreview from '@/components/reports/ReportPreview.vue'
import { reportsAPI } from '@/services/api'

// State
const loading = ref(false)
const generating = ref(false)
const showGenerateDialog = ref(false)
const showPreviewDialog = ref(false)
const showSuccess = ref(false)
const showError = ref(false)
const successMessage = ref('')
const errorMessage = ref('')

const selectedCategory = ref('')
const selectedType = ref('')
const searchQuery = ref('')
const selectedReport = ref<any>(null)
const reportParameters = ref({})
const generatingReports = ref(new Set<number>())

// Mock data for pre-built reports
const prebuiltReports = ref([
  {
    id: 1,
    name: 'CPMK Achievement Report',
    description: 'Comprehensive analysis of Course Learning Outcomes achievement across all courses',
    type: 'cpmk_achievement',
    category: 'academic',
    usage_count: 45,
    estimated_time: '3-5',
    export_formats: ['pdf', 'excel', 'csv']
  },
  {
    id: 2,
    name: 'CPL Mapping Matrix',
    description: 'Visual mapping between Graduate Learning Outcomes and Course Learning Outcomes',
    type: 'cpl_mapping',
    category: 'academic',
    usage_count: 32,
    estimated_time: '2-4',
    export_formats: ['pdf', 'excel']
  },
  {
    id: 3,
    name: 'Assessment Analytics',
    description: 'Detailed analysis of assessment methods, performance, and trends',
    type: 'assessment_analytics',
    category: 'analytics',
    usage_count: 28,
    estimated_time: '4-6',
    export_formats: ['pdf', 'excel', 'csv']
  },
  {
    id: 4,
    name: 'Course Performance Dashboard',
    description: 'Performance metrics and analytics for individual courses',
    type: 'course_performance',
    category: 'analytics',
    usage_count: 38,
    estimated_time: '2-3',
    export_formats: ['pdf', 'excel']
  },
  {
    id: 5,
    name: 'Student Progress Tracking',
    description: 'Individual and cohort student progress analysis',
    type: 'student_progress',
    category: 'academic',
    usage_count: 22,
    estimated_time: '3-5',
    export_formats: ['pdf', 'excel', 'csv']
  },
  {
    id: 6,
    name: 'Faculty Overview Report',
    description: 'Comprehensive faculty performance and resource utilization',
    type: 'faculty_overview',
    category: 'administrative',
    usage_count: 15,
    estimated_time: '5-8',
    export_formats: ['pdf', 'excel']
  },
  {
    id: 7,
    name: 'Compliance Audit Report',
    description: 'Academic compliance and accreditation readiness assessment',
    type: 'compliance_audit',
    category: 'compliance',
    usage_count: 12,
    estimated_time: '6-10',
    export_formats: ['pdf']
  },
  {
    id: 8,
    name: 'Grade Distribution Analysis',
    description: 'Statistical analysis of grade distributions across courses and programs',
    type: 'grade_distribution',
    category: 'analytics',
    usage_count: 25,
    estimated_time: '3-4',
    export_formats: ['pdf', 'excel', 'csv']
  }
])

// Computed
const filteredReports = computed(() => {
  let filtered = prebuiltReports.value

  if (selectedCategory.value) {
    filtered = filtered.filter(report => report.category === selectedCategory.value)
  }

  if (selectedType.value) {
    filtered = filtered.filter(report => report.type === selectedType.value)
  }

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(report => 
      report.name.toLowerCase().includes(query) ||
      report.description.toLowerCase().includes(query)
    )
  }

  return filtered
})

const categoryOptions = [
  { title: 'Academic', value: 'academic' },
  { title: 'Administrative', value: 'administrative' },
  { title: 'Analytics', value: 'analytics' },
  { title: 'Compliance', value: 'compliance' }
]

const typeOptions = [
  { title: 'CPMK Achievement', value: 'cpmk_achievement' },
  { title: 'CPL Mapping', value: 'cpl_mapping' },
  { title: 'Assessment Analytics', value: 'assessment_analytics' },
  { title: 'Course Performance', value: 'course_performance' },
  { title: 'Student Progress', value: 'student_progress' },
  { title: 'Faculty Overview', value: 'faculty_overview' },
  { title: 'Compliance Audit', value: 'compliance_audit' },
  { title: 'Grade Distribution', value: 'grade_distribution' }
]

// Methods
const filterReports = () => {
  // Filtering is handled by computed property
}

const openGenerateDialog = (report: any) => {
  selectedReport.value = report
  reportParameters.value = {}
  showGenerateDialog.value = true
}

const generateReport = async () => {
  if (!selectedReport.value) return
  
  generating.value = true
  generatingReports.value.add(selectedReport.value.id)
  
  try {
    const response = await reportsAPI.generateReport(selectedReport.value.id, reportParameters.value)
    successMessage.value = `${selectedReport.value.name} generation started successfully`
    showSuccess.value = true
    showGenerateDialog.value = false
  } catch (error: any) {
    errorMessage.value = error.response?.data?.message || 'Failed to generate report'
    showError.value = true
  } finally {
    generating.value = false
    generatingReports.value.delete(selectedReport.value.id)
  }
}

const previewReport = (report: any) => {
  selectedReport.value = report
  showPreviewDialog.value = true
}

const scheduleReport = (report: any) => {
  console.log('Schedule report:', report.name)
  // Implementation for scheduling
}

const shareReport = (report: any) => {
  console.log('Share report:', report.name)
  // Implementation for sharing
}

const viewAnalytics = (report: any) => {
  console.log('View analytics for:', report.name)
  // Implementation for analytics
}

// Utility functions
const getReportTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    cpmk_achievement: 'primary',
    cpl_mapping: 'secondary',
    assessment_analytics: 'success',
    course_performance: 'warning',
    student_progress: 'info',
    faculty_overview: 'purple',
    compliance_audit: 'orange',
    grade_distribution: 'teal'
  }
  return colors[type] || 'grey'
}

const getReportTypeIcon = (type?: string) => {
  const icons: Record<string, string> = {
    cpmk_achievement: 'mdi-target',
    cpl_mapping: 'mdi-bullseye-arrow',
    assessment_analytics: 'mdi-clipboard-check',
    course_performance: 'mdi-book-open-page-variant',
    student_progress: 'mdi-account-school',
    faculty_overview: 'mdi-account-group',
    compliance_audit: 'mdi-shield-check',
    grade_distribution: 'mdi-chart-histogram'
  }
  return icons[type || ''] || 'mdi-file-chart'
}

const getCategoryColor = (category: string) => {
  const colors: Record<string, string> = {
    academic: 'blue',
    administrative: 'green',
    analytics: 'purple',
    compliance: 'orange'
  }
  return colors[category] || 'grey'
}

const formatCategory = (category: string) => {
  const categories: Record<string, string> = {
    academic: 'Academic',
    administrative: 'Administrative',
    analytics: 'Analytics',
    compliance: 'Compliance'
  }
  return categories[category] || category
}

// Lifecycle
onMounted(() => {
  // Load any additional data if needed
})
</script>
