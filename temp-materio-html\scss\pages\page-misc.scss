// * Miscellaneous
// *******************************************************************************

@import "../_bootstrap-extended/include";

// Misc wrapper styles
.misc-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.25rem;
  min-block-size: 100vh;
}

// Misc background image styles
.misc-bg {
  position: absolute;
  inline-size: 100%;
  inset-block-end: 0;
  inset-inline-start: 0;
}

// Misc object styles
.misc-object,
.misc-object-right {
  position: absolute;
  z-index: 1;
}
.misc-object {
  inset-block-end: 6%;
  inset-inline-start: 3%;
}
.misc-object-right {
  inset-block-end: 7%;
  inset-inline-end: 3%;
}

// Misc model style

.misc-model {
  position: relative;
  inset-block-end: 3rem;
}
