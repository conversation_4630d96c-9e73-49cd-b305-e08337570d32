<?php

/**
 * API Endpoint Tests
 * 
 * Validates all API routes, request/response formats, and error handling
 */

// Ensure we have access to the test runner
if (!isset($testRunner)) {
    die("This test must be run through the TestRunner\n");
}

$testRunner->logSubSection('API Endpoint Tests');

// First, get authentication token
$authToken = null;
$baseUrl = $config['api']['base_url'];

// Try to login to get auth token
try {
    $loginUrl = $baseUrl . '/auth/login';
    $testUser = $config['test_data']['users']['admin'];
    $loginData = [
        'username' => $testUser['username'],
        'password' => $testUser['password']
    ];
    
    $response = $testRunner->makeHttpRequest('POST', $loginUrl, $loginData);
    if ($response['status_code'] === 200 && isset($response['json']['data']['token'])) {
        $authToken = $response['json']['data']['token'];
        $testRunner->log('info', 'Authentication token obtained for API testing', 'AUTH');
    } else {
        $testRunner->logWarning('Could not obtain auth token, some tests may fail');
    }
} catch (Exception $e) {
    $testRunner->logWarning('Authentication failed: ' . $e->getMessage());
}

$tests = [
    'auth_endpoints' => 'Authentication Endpoints',
    'user_endpoints' => 'User Management Endpoints',
    'faculty_endpoints' => 'Faculty Management Endpoints',
    'study_program_endpoints' => 'Study Program Endpoints',
    'course_endpoints' => 'Course Management Endpoints',
    'cpmk_endpoints' => 'CPMK Management Endpoints',
    'cpl_endpoints' => 'CPL Management Endpoints',
    'assessment_endpoints' => 'Assessment Endpoints',
    'report_endpoints' => 'Report Endpoints',
    'error_handling' => 'Error Handling'
];

$totalTests = count($tests);
$currentTest = 0;
$passedTests = 0;

foreach ($tests as $testKey => $testName) {
    $currentTest++;
    $testRunner->logProgress($currentTest, $totalTests, "Running {$testName}");
    
    $startTime = microtime(true);
    $testPassed = false;
    $message = '';
    $details = [];

    try {
        $headers = $authToken ? ['Authorization' => 'Bearer ' . $authToken] : [];
        
        switch ($testKey) {
            case 'auth_endpoints':
                $endpoints = $config['api']['test_endpoints']['auth'];
                $results = [];
                $successCount = 0;
                
                foreach ($endpoints as $name => $endpoint) {
                    $url = $baseUrl . $endpoint;
                    
                    try {
                        if ($name === 'login') {
                            // Test login with invalid credentials
                            $response = $testRunner->makeHttpRequest('POST', $url, ['username' => 'invalid', 'password' => 'invalid']);
                            $expected = in_array($response['status_code'], [400, 401, 422]);
                        } else {
                            // Test other endpoints
                            $response = $testRunner->makeHttpRequest('GET', $url, null, $headers);
                            $expected = in_array($response['status_code'], [200, 401, 404]);
                        }
                        
                        if ($expected) $successCount++;
                        
                        $results[$name] = [
                            'status_code' => $response['status_code'],
                            'success' => $expected,
                            'duration' => $response['duration']
                        ];
                        
                    } catch (Exception $e) {
                        $results[$name] = [
                            'error' => $e->getMessage(),
                            'success' => false
                        ];
                    }
                }
                
                $testPassed = $successCount === count($endpoints);
                $message = "Auth endpoints: {$successCount}/" . count($endpoints) . " working";
                $details = ['endpoints' => $results];
                break;

            case 'user_endpoints':
                $endpoints = $config['api']['test_endpoints']['users'];
                $results = [];
                $successCount = 0;
                
                foreach ($endpoints as $name => $endpoint) {
                    $url = $baseUrl . str_replace('{id}', '1', $endpoint);
                    
                    try {
                        $method = in_array($name, ['create', 'update']) ? 'POST' : 'GET';
                        if ($name === 'update') $method = 'PUT';
                        if ($name === 'delete') $method = 'DELETE';
                        
                        $response = $testRunner->makeHttpRequest($method, $url, null, $headers);
                        $expected = in_array($response['status_code'], [200, 201, 401, 404, 405]);
                        
                        if ($expected) $successCount++;
                        
                        $results[$name] = [
                            'method' => $method,
                            'status_code' => $response['status_code'],
                            'success' => $expected,
                            'duration' => $response['duration']
                        ];
                        
                    } catch (Exception $e) {
                        $results[$name] = [
                            'error' => $e->getMessage(),
                            'success' => false
                        ];
                    }
                }
                
                $testPassed = $successCount >= count($endpoints) * 0.7; // 70% success rate
                $message = "User endpoints: {$successCount}/" . count($endpoints) . " accessible";
                $details = ['endpoints' => $results];
                break;

            case 'faculty_endpoints':
                $endpoints = $config['api']['test_endpoints']['faculties'];
                $results = [];
                $successCount = 0;
                
                foreach ($endpoints as $name => $endpoint) {
                    $url = $baseUrl . str_replace('{id}', '1', $endpoint);
                    
                    try {
                        $method = $name === 'list' || $name === 'show' ? 'GET' : 'POST';
                        if ($name === 'update') $method = 'PUT';
                        if ($name === 'delete') $method = 'DELETE';
                        
                        $response = $testRunner->makeHttpRequest($method, $url, null, $headers);
                        $expected = in_array($response['status_code'], [200, 201, 401, 404, 405]);
                        
                        if ($expected) $successCount++;
                        
                        $results[$name] = [
                            'method' => $method,
                            'status_code' => $response['status_code'],
                            'success' => $expected,
                            'duration' => $response['duration']
                        ];
                        
                    } catch (Exception $e) {
                        $results[$name] = [
                            'error' => $e->getMessage(),
                            'success' => false
                        ];
                    }
                }
                
                $testPassed = $successCount >= count($endpoints) * 0.7;
                $message = "Faculty endpoints: {$successCount}/" . count($endpoints) . " accessible";
                $details = ['endpoints' => $results];
                break;

            case 'study_program_endpoints':
                $endpoints = $config['api']['test_endpoints']['study_programs'];
                $results = [];
                $successCount = 0;
                
                foreach ($endpoints as $name => $endpoint) {
                    $url = $baseUrl . str_replace('{id}', '1', $endpoint);
                    
                    try {
                        $method = $name === 'list' || $name === 'show' ? 'GET' : 'POST';
                        if ($name === 'update') $method = 'PUT';
                        if ($name === 'delete') $method = 'DELETE';
                        
                        $response = $testRunner->makeHttpRequest($method, $url, null, $headers);
                        $expected = in_array($response['status_code'], [200, 201, 401, 404, 405]);
                        
                        if ($expected) $successCount++;
                        
                        $results[$name] = [
                            'method' => $method,
                            'status_code' => $response['status_code'],
                            'success' => $expected,
                            'duration' => $response['duration']
                        ];
                        
                    } catch (Exception $e) {
                        $results[$name] = [
                            'error' => $e->getMessage(),
                            'success' => false
                        ];
                    }
                }
                
                $testPassed = $successCount >= count($endpoints) * 0.7;
                $message = "Study program endpoints: {$successCount}/" . count($endpoints) . " accessible";
                $details = ['endpoints' => $results];
                break;

            case 'course_endpoints':
                $endpoints = $config['api']['test_endpoints']['courses'];
                $results = [];
                $successCount = 0;
                
                foreach ($endpoints as $name => $endpoint) {
                    $url = $baseUrl . str_replace('{id}', '1', $endpoint);
                    
                    try {
                        $method = in_array($name, ['list', 'show', 'references', 'topics']) ? 'GET' : 'POST';
                        if ($name === 'update') $method = 'PUT';
                        if ($name === 'delete') $method = 'DELETE';
                        
                        $response = $testRunner->makeHttpRequest($method, $url, null, $headers);
                        $expected = in_array($response['status_code'], [200, 201, 401, 404, 405]);
                        
                        if ($expected) $successCount++;
                        
                        $results[$name] = [
                            'method' => $method,
                            'status_code' => $response['status_code'],
                            'success' => $expected,
                            'duration' => $response['duration']
                        ];
                        
                    } catch (Exception $e) {
                        $results[$name] = [
                            'error' => $e->getMessage(),
                            'success' => false
                        ];
                    }
                }
                
                $testPassed = $successCount >= count($endpoints) * 0.7;
                $message = "Course endpoints: {$successCount}/" . count($endpoints) . " accessible";
                $details = ['endpoints' => $results];
                break;

            case 'cpmk_endpoints':
                $endpoints = $config['api']['test_endpoints']['cpmk'];
                $results = [];
                $successCount = 0;
                
                foreach ($endpoints as $name => $endpoint) {
                    $url = $baseUrl . str_replace('{id}', '1', $endpoint);
                    
                    try {
                        $method = in_array($name, ['list', 'show', 'sub_cpmk', 'cpl_relations']) ? 'GET' : 'POST';
                        if ($name === 'update') $method = 'PUT';
                        if ($name === 'delete') $method = 'DELETE';
                        
                        $response = $testRunner->makeHttpRequest($method, $url, null, $headers);
                        $expected = in_array($response['status_code'], [200, 201, 401, 404, 405]);
                        
                        if ($expected) $successCount++;
                        
                        $results[$name] = [
                            'method' => $method,
                            'status_code' => $response['status_code'],
                            'success' => $expected,
                            'duration' => $response['duration']
                        ];
                        
                    } catch (Exception $e) {
                        $results[$name] = [
                            'error' => $e->getMessage(),
                            'success' => false
                        ];
                    }
                }
                
                $testPassed = $successCount >= count($endpoints) * 0.7;
                $message = "CPMK endpoints: {$successCount}/" . count($endpoints) . " accessible";
                $details = ['endpoints' => $results];
                break;

            case 'cpl_endpoints':
                $endpoints = $config['api']['test_endpoints']['cpl'];
                $results = [];
                $successCount = 0;
                
                foreach ($endpoints as $name => $endpoint) {
                    $url = $baseUrl . str_replace('{id}', '1', $endpoint);
                    
                    try {
                        $method = $name === 'list' || $name === 'show' ? 'GET' : 'POST';
                        if ($name === 'update') $method = 'PUT';
                        if ($name === 'delete') $method = 'DELETE';
                        
                        $response = $testRunner->makeHttpRequest($method, $url, null, $headers);
                        $expected = in_array($response['status_code'], [200, 201, 401, 404, 405]);
                        
                        if ($expected) $successCount++;
                        
                        $results[$name] = [
                            'method' => $method,
                            'status_code' => $response['status_code'],
                            'success' => $expected,
                            'duration' => $response['duration']
                        ];
                        
                    } catch (Exception $e) {
                        $results[$name] = [
                            'error' => $e->getMessage(),
                            'success' => false
                        ];
                    }
                }
                
                $testPassed = $successCount >= count($endpoints) * 0.7;
                $message = "CPL endpoints: {$successCount}/" . count($endpoints) . " accessible";
                $details = ['endpoints' => $results];
                break;

            case 'assessment_endpoints':
                $endpoints = $config['api']['test_endpoints']['assessments'];
                $results = [];
                $successCount = 0;
                
                foreach ($endpoints as $name => $endpoint) {
                    $url = $baseUrl . $endpoint;
                    
                    try {
                        $response = $testRunner->makeHttpRequest('GET', $url, null, $headers);
                        $expected = in_array($response['status_code'], [200, 401, 404, 405]);
                        
                        if ($expected) $successCount++;
                        
                        $results[$name] = [
                            'status_code' => $response['status_code'],
                            'success' => $expected,
                            'duration' => $response['duration']
                        ];
                        
                    } catch (Exception $e) {
                        $results[$name] = [
                            'error' => $e->getMessage(),
                            'success' => false
                        ];
                    }
                }
                
                $testPassed = $successCount >= count($endpoints) * 0.7;
                $message = "Assessment endpoints: {$successCount}/" . count($endpoints) . " accessible";
                $details = ['endpoints' => $results];
                break;

            case 'report_endpoints':
                $endpoints = array_merge(
                    $config['api']['test_endpoints']['reports'],
                    $config['api']['test_endpoints']['dashboard']
                );
                $results = [];
                $successCount = 0;
                
                foreach ($endpoints as $name => $endpoint) {
                    $url = $baseUrl . $endpoint;
                    
                    try {
                        $response = $testRunner->makeHttpRequest('GET', $url, null, $headers);
                        $expected = in_array($response['status_code'], [200, 401, 404, 405]);
                        
                        if ($expected) $successCount++;
                        
                        $results[$name] = [
                            'status_code' => $response['status_code'],
                            'success' => $expected,
                            'duration' => $response['duration']
                        ];
                        
                    } catch (Exception $e) {
                        $results[$name] = [
                            'error' => $e->getMessage(),
                            'success' => false
                        ];
                    }
                }
                
                $testPassed = $successCount >= count($endpoints) * 0.7;
                $message = "Report endpoints: {$successCount}/" . count($endpoints) . " accessible";
                $details = ['endpoints' => $results];
                break;

            case 'error_handling':
                // Test error handling with invalid requests
                $errorTests = [
                    'invalid_endpoint' => $baseUrl . '/invalid/endpoint',
                    'malformed_json' => $baseUrl . '/auth/login',
                    'missing_auth' => $baseUrl . '/users'
                ];
                
                $results = [];
                $successCount = 0;
                
                foreach ($errorTests as $name => $url) {
                    try {
                        if ($name === 'malformed_json') {
                            // Send malformed JSON
                            $response = $testRunner->makeHttpRequest('POST', $url, 'invalid json');
                        } else {
                            $response = $testRunner->makeHttpRequest('GET', $url);
                        }
                        
                        $expected = in_array($response['status_code'], [400, 401, 404, 405, 422]);
                        
                        if ($expected) $successCount++;
                        
                        $results[$name] = [
                            'status_code' => $response['status_code'],
                            'success' => $expected,
                            'duration' => $response['duration']
                        ];
                        
                    } catch (Exception $e) {
                        $results[$name] = [
                            'error' => $e->getMessage(),
                            'success' => false
                        ];
                    }
                }
                
                $testPassed = $successCount >= count($errorTests) * 0.7;
                $message = "Error handling: {$successCount}/" . count($errorTests) . " tests passed";
                $details = ['error_tests' => $results];
                break;
        }

    } catch (Exception $e) {
        $testPassed = false;
        $message = "Exception: " . $e->getMessage();
        $details = [
            'exception' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ];
    }

    $duration = round((microtime(true) - $startTime) * 1000);
    $status = $testPassed ? 'PASS' : 'FAIL';
    
    $testRunner->logTestResult($testName, $status, $message, $duration, $details);
    
    if ($testPassed) {
        $passedTests++;
    }
}

// Summary for this test category
$testRunner->logSubSection('API Endpoint Tests Summary');
$testRunner->log('info', "Passed: {$passedTests}/{$totalTests} tests", 'SUMMARY');

if ($passedTests >= $totalTests * 0.8) { // 80% success rate for API tests
    $testRunner->logSuccess("API endpoint tests passed! Most endpoints are accessible and working.");
    return true;
} else {
    $failedTests = $totalTests - $passedTests;
    $testRunner->logError("API endpoint tests failed! {$failedTests} test(s) need attention.");
    return false;
}
