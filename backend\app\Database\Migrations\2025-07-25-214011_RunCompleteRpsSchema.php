<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class RunCompleteRpsSchema extends Migration
{
    public function up()
    {
        // Path to SQL file (go up one level from backend)
        $sqlFile = dirname(ROOTPATH) . '/database_design_mysql56.sql';

        if (!file_exists($sqlFile)) {
            throw new \Exception("SQL file not found: " . $sqlFile);
        }

        // Read SQL file content
        $sql = file_get_contents($sqlFile);

        if ($sql === false) {
            throw new \Exception("Failed to read SQL file: " . $sqlFile);
        }

        // Remove problematic statements for CodeIgniter migration
        $sql = $this->cleanSqlForMigration($sql);

        // Split SQL into individual statements
        $statements = $this->splitSqlStatements($sql);

        // Execute each statement
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement) && $statement !== ';') {
                try {
                    $this->db->query($statement);
                    echo "Executed: " . substr($statement, 0, 50) . "..." . PHP_EOL;
                } catch (\Exception $e) {
                    echo "Warning: " . $e->getMessage() . PHP_EOL;
                    echo "Statement: " . substr($statement, 0, 100) . "..." . PHP_EOL;
                    // Continue with other statements
                }
            }
        }

        echo "Complete RPS schema migration completed!" . PHP_EOL;
    }

    public function down()
    {
        // Drop all tables in reverse order
        $tables = [
            'v_assessment_plan_details',
            'v_cpmk_cpl_details',
            'v_course_details',
            'assessment_plans',
            'sub_cpmk',
            'cpmk_cpl_relations',
            'cpmk',
            'course_topics',
            'course_references',
            'courses',
            'cpl',
            'study_programs',
            'faculties',
            'users',
            'roles',
            'assessment_methods'
        ];

        // Disable foreign key checks
        $this->db->query('SET FOREIGN_KEY_CHECKS = 0');

        foreach ($tables as $table) {
            try {
                // Check if table exists by trying to describe it
                $this->db->query("DESCRIBE `{$table}`");
                $this->forge->dropTable($table, true);
                echo "Dropped table: " . $table . PHP_EOL;
            } catch (\Exception $e) {
                // Table doesn't exist, skip
                echo "Table not found (skipped): " . $table . PHP_EOL;
            }
        }

        // Re-enable foreign key checks
        $this->db->query('SET FOREIGN_KEY_CHECKS = 1');

        echo "Complete RPS schema rollback completed!" . PHP_EOL;
    }

    private function cleanSqlForMigration($sql)
    {
        // Remove database creation and use statements
        $sql = preg_replace('/CREATE DATABASE.*?;/i', '', $sql);
        $sql = preg_replace('/USE\s+`?[^`\s]+`?\s*;/i', '', $sql);

        // Remove transaction control statements (CodeIgniter handles this)
        $sql = preg_replace('/SET\s+AUTOCOMMIT\s*=\s*0\s*;/i', '', $sql);
        $sql = preg_replace('/START\s+TRANSACTION\s*;/i', '', $sql);
        $sql = preg_replace('/COMMIT\s*;/i', '', $sql);

        // Remove foreign key check statements (we'll handle this manually)
        $sql = preg_replace('/SET\s+FOREIGN_KEY_CHECKS\s*=\s*[01]\s*;/i', '', $sql);

        // Remove DROP TABLE statements (we'll handle this in down method)
        $sql = preg_replace('/DROP\s+TABLE\s+IF\s+EXISTS.*?;/i', '', $sql);

        // Remove timezone setting
        $sql = preg_replace('/SET\s+time_zone\s*=.*?;/i', '', $sql);

        // Remove SQL_MODE setting
        $sql = preg_replace('/SET\s+SQL_MODE\s*=.*?;/i', '', $sql);

        return $sql;
    }

    private function splitSqlStatements($sql)
    {
        // Split by semicolon, but be careful with semicolons inside strings
        $statements = [];
        $current = '';
        $inString = false;
        $stringChar = '';

        for ($i = 0; $i < strlen($sql); $i++) {
            $char = $sql[$i];

            if (!$inString && ($char === '"' || $char === "'")) {
                $inString = true;
                $stringChar = $char;
            } elseif ($inString && $char === $stringChar) {
                // Check if it's escaped
                if ($i > 0 && $sql[$i-1] !== '\\') {
                    $inString = false;
                }
            } elseif (!$inString && $char === ';') {
                $statements[] = $current;
                $current = '';
                continue;
            }

            $current .= $char;
        }

        // Add the last statement if it doesn't end with semicolon
        if (!empty(trim($current))) {
            $statements[] = $current;
        }

        return $statements;
    }
}
