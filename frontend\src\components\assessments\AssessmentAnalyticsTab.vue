<template>
  <div>
    <!-- Course Selection -->
    <v-row class="mb-6">
      <v-col cols="12" md="8">
        <v-select
          v-model="selectedCourse"
          :items="courseOptions"
          label="Select Course for Analytics"
          variant="outlined"
          prepend-inner-icon="mdi-book-open-page-variant"
          clearable
          @update:model-value="handleCourseChange"
        />
      </v-col>
      
      <v-col cols="12" md="4" class="d-flex align-center gap-2">
        <v-btn
          color="primary"
          variant="outlined"
          @click="exportReport"
          :disabled="!selectedCourse"
        >
          <v-icon start>mdi-file-chart</v-icon>
          Export Report
        </v-btn>
      </v-col>
    </v-row>

    <!-- Analytics Dashboard -->
    <div v-if="selectedCourse" class="text-center py-12">
      <v-icon size="64" color="grey-lighten-2">mdi-chart-line</v-icon>
      <p class="text-h6 text-medium-emphasis mt-4">Assessment Analytics & Reports</p>
      <p class="text-body-2 text-medium-emphasis">
        Comprehensive analytics dashboard with performance metrics, grade distributions, and achievement reports
      </p>
      <p class="text-caption text-medium-emphasis mt-2">
        This feature will be fully implemented in the next development phase
      </p>
    </div>

    <!-- Empty State -->
    <div v-else class="text-center py-12">
      <v-icon size="64" color="grey-lighten-2">mdi-chart-line</v-icon>
      <p class="text-h6 text-medium-emphasis mt-4">Select a Course to View Analytics</p>
      <p class="text-body-2 text-medium-emphasis">
        Choose a course from the dropdown above to view assessment analytics and reports
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { coursesAPI } from '@/services/api'
import type { Course } from '@/types/auth'

// State
const courses = ref<Course[]>([])
const selectedCourse = ref<number | null>(null)

// Computed options
const courseOptions = computed(() => 
  courses.value.map(course => ({
    title: `${course.code} - ${course.name}`,
    value: course.id
  }))
)

// Methods
const loadCourses = async () => {
  try {
    const response = await coursesAPI.getAll({ per_page: 1000 })
    courses.value = response.data.data
  } catch (error) {
    console.error('Load courses error:', error)
  }
}

const handleCourseChange = () => {
  // Implementation for course change
  console.log('Course changed:', selectedCourse.value)
}

const exportReport = () => {
  // Implementation for report export
  console.log('Export report for course:', selectedCourse.value)
}

// Lifecycle
onMounted(() => {
  loadCourses()
})
</script>
