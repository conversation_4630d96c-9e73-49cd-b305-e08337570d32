<?php

require_once 'vendor/autoload.php';

// Create database if it doesn't exist
try {
    $pdo = new PDO('mysql:host=localhost', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create database
    $pdo->exec("CREATE DATABASE IF NOT EXISTS rpswebid CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci");
    echo "Database 'rpswebid' created or already exists.\n";
    
    // Test connection to the specific database
    $pdo = new PDO('mysql:host=localhost;dbname=rpswebid', 'root', '');
    echo "Successfully connected to rpswebid database.\n";
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
    exit(1);
}

// Now test CodeIgniter database connection
try {
    // Bootstrap CodeIgniter
    define('FCPATH', __DIR__ . DIRECTORY_SEPARATOR . 'public' . DIRECTORY_SEPARATOR);
    define('SYSTEMPATH', __DIR__ . DIRECTORY_SEPARATOR . 'vendor' . DIRECTORY_SEPARATOR . 'codeigniter4' . DIRECTORY_SEPARATOR . 'framework' . DIRECTORY_SEPARATOR . 'system' . DIRECTORY_SEPARATOR);
    define('APPPATH', __DIR__ . DIRECTORY_SEPARATOR . 'app' . DIRECTORY_SEPARATOR);
    define('WRITEPATH', __DIR__ . DIRECTORY_SEPARATOR . 'writable' . DIRECTORY_SEPARATOR);
    define('TESTPATH', __DIR__ . DIRECTORY_SEPARATOR . 'tests' . DIRECTORY_SEPARATOR);
    define('CI_DEBUG', 1);

    require_once SYSTEMPATH . 'bootstrap.php';

    $app = \Config\Services::codeigniter();
    $app->initialize();

    // Test database connection
    $db = \Config\Database::connect();
    
    if ($db->connID) {
        echo "CodeIgniter database connection successful.\n";
        
        // Check if tables exist
        $tables = $db->listTables();
        echo "Existing tables: " . implode(', ', $tables) . "\n";
        
        if (empty($tables)) {
            echo "No tables found. Running migrations...\n";
            
            // Run migrations programmatically
            $migrate = \Config\Services::migrations();
            $migrate->latest();
            
            echo "Migrations completed.\n";
            
            // Run seeders
            $seeder = \Config\Database::seeder();
            $seeder->call('DatabaseSeeder');
            
            echo "Seeders completed.\n";
        }
        
    } else {
        echo "CodeIgniter database connection failed.\n";
        exit(1);
    }
    
} catch (Exception $e) {
    echo "CodeIgniter error: " . $e->getMessage() . "\n";
    exit(1);
}

echo "Database setup completed successfully!\n";
