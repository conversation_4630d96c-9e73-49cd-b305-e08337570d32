<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class FacultySeeder extends Seeder
{
    public function run()
    {
        // Get admin user ID for dean assignment
        $adminUser = $this->db->table('users')
            ->where('username', 'admin.teknik')
            ->get()
            ->getRowArray();

        $data = [
            [
                'code' => 'FT',
                'name' => 'Fakultas Teknik',
                'description' => 'Fakultas Teknik menyelenggarakan pendidikan tinggi di bidang teknik dan teknologi',
                'dean_id' => $adminUser['id'] ?? null,
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'code' => 'FEB',
                'name' => 'Fakultas Ekonomi dan Bisnis',
                'description' => 'Fakultas Ekonomi menyelenggarakan pendidikan tinggi di bidang ekonomi dan bisnis',
                'dean_id' => null,
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'code' => 'FP',
                'name' => 'Fakultas Pertanian',
                'description' => 'Fakultas Pertanian menyelenggarakan pendidikan tinggi untuk bidang pertanian',
                'dean_id' => null,
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'code' => 'FH',
                'name' => 'Fakultas Hukum',
                'description' => 'Fakultas Hukum menyelenggarakan pendidikan tinggi di bidang hukum',
                'dean_id' => null,
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            
        ];

        // Insert data
        $this->db->table('faculties')->insertBatch($data);
        
        echo "Faculties seeded successfully!" . PHP_EOL;
    }
}
