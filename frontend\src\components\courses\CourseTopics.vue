<template>
  <div class="pa-6">
    <!-- Header -->
    <div class="d-flex align-center justify-space-between mb-4">
      <div>
        <h3 class="text-h6 font-weight-bold">{{ course.name }} - Weekly Topics</h3>
        <p class="text-subtitle-2 text-medium-emphasis">
          Plan and manage weekly course topics and learning materials
        </p>
      </div>
      
      <v-btn
        color="primary"
        @click="openCreateModal"
      >
        <v-icon start>mdi-plus</v-icon>
        Add Topic
      </v-btn>
    </div>

    <!-- Topics Timeline -->
    <v-timeline align="start" class="topic-timeline">
      <v-timeline-item
        v-for="topic in sortedTopics"
        :key="topic.id"
        :dot-color="getWeekColor(topic.week_number)"
        size="small"
      >
        <template #icon>
          <span class="text-caption font-weight-bold">{{ topic.week_number }}</span>
        </template>
        
        <v-card variant="outlined" class="mb-4">
          <v-card-title class="d-flex align-center justify-space-between">
            <div>
              <h4 class="text-h6">Week {{ topic.week_number }}</h4>
              <p class="text-subtitle-2 text-medium-emphasis">{{ topic.topic_title }}</p>
            </div>
            
            <div class="d-flex gap-1">
              <v-btn
                icon
                size="small"
                variant="text"
                @click="openEditModal(topic)"
              >
                <v-icon size="small">mdi-pencil</v-icon>
                <v-tooltip activator="parent">Edit</v-tooltip>
              </v-btn>
              
              <v-btn
                icon
                size="small"
                variant="text"
                color="error"
                @click="openDeleteDialog(topic)"
              >
                <v-icon size="small">mdi-delete</v-icon>
                <v-tooltip activator="parent">Delete</v-tooltip>
              </v-btn>
            </div>
          </v-card-title>
          
          <v-card-text>
            <div v-if="topic.description" class="mb-3">
              <h5 class="text-subtitle-2 font-weight-bold mb-1">Description:</h5>
              <p class="text-body-2">{{ topic.description }}</p>
            </div>
            
            <div v-if="topic.learning_materials" class="mb-3">
              <h5 class="text-subtitle-2 font-weight-bold mb-1">Learning Materials:</h5>
              <p class="text-body-2">{{ topic.learning_materials }}</p>
            </div>
            
            <div v-if="topic.learning_methods" class="mb-3">
              <h5 class="text-subtitle-2 font-weight-bold mb-1">Learning Methods:</h5>
              <p class="text-body-2">{{ topic.learning_methods }}</p>
            </div>
            
            <div v-if="topic.estimated_time" class="d-flex align-center">
              <v-icon size="small" class="mr-1">mdi-clock</v-icon>
              <span class="text-caption">{{ formatTime(topic.estimated_time) }}</span>
            </div>
          </v-card-text>
        </v-card>
      </v-timeline-item>
    </v-timeline>

    <!-- Empty State -->
    <div v-if="topics.length === 0" class="text-center py-8">
      <v-icon size="64" color="grey-lighten-2">mdi-format-list-numbered</v-icon>
      <p class="text-h6 text-medium-emphasis mt-4">No topics planned yet</p>
      <v-btn
        color="primary"
        @click="openCreateModal"
        class="mt-2"
      >
        <v-icon start>mdi-plus</v-icon>
        Plan First Topic
      </v-btn>
    </div>

    <!-- Topic Form Modal -->
    <FormModal
      v-model="showModal"
      :title="modalTitle"
      icon="mdi-format-list-numbered"
      :mode="modalMode"
      :loading="modalLoading"
      max-width="800"
      @submit="handleSubmit"
      @close="closeModal"
    >
      <v-row>
        <v-col cols="12" md="6">
          <v-select
            v-model="formData.week_number"
            :items="weekOptions"
            :rules="weekRules"
            label="Week Number *"
            variant="outlined"
            prepend-inner-icon="mdi-calendar-week"
            :disabled="modalLoading"
          />
        </v-col>
        
        <v-col cols="12" md="6">
          <v-text-field
            v-model="formData.estimated_time"
            label="Estimated Time (minutes)"
            type="number"
            variant="outlined"
            prepend-inner-icon="mdi-clock"
            :disabled="modalLoading"
          />
        </v-col>
        
        <v-col cols="12">
          <v-text-field
            v-model="formData.topic_title"
            :rules="titleRules"
            label="Topic Title *"
            variant="outlined"
            prepend-inner-icon="mdi-format-title"
            :disabled="modalLoading"
          />
        </v-col>
        
        <v-col cols="12">
          <v-textarea
            v-model="formData.description"
            label="Description"
            variant="outlined"
            prepend-inner-icon="mdi-text"
            rows="3"
            :disabled="modalLoading"
          />
        </v-col>
        
        <v-col cols="12">
          <v-textarea
            v-model="formData.learning_materials"
            label="Learning Materials"
            variant="outlined"
            prepend-inner-icon="mdi-book-open"
            rows="3"
            :disabled="modalLoading"
          />
        </v-col>
        
        <v-col cols="12">
          <v-textarea
            v-model="formData.learning_methods"
            label="Learning Methods"
            variant="outlined"
            prepend-inner-icon="mdi-teach"
            rows="3"
            :disabled="modalLoading"
          />
        </v-col>
      </v-row>
    </FormModal>

    <!-- Delete Confirmation Dialog -->
    <v-dialog v-model="showDeleteDialog" max-width="400">
      <v-card>
        <v-card-title class="text-h6">Confirm Delete</v-card-title>
        <v-card-text>
          Are you sure you want to delete this topic?
          This action cannot be undone.
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn @click="showDeleteDialog = false">Cancel</v-btn>
          <v-btn
            color="error"
            :loading="deleteLoading"
            @click="confirmDelete"
          >
            Delete
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Success/Error Snackbars -->
    <v-snackbar
      v-model="showSuccess"
      color="success"
      timeout="3000"
    >
      {{ successMessage }}
    </v-snackbar>

    <v-snackbar
      v-model="showError"
      color="error"
      timeout="5000"
    >
      {{ errorMessage }}
    </v-snackbar>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import FormModal from '@/components/common/FormModal.vue'
import { coursesAPI } from '@/services/api'
import type { Course } from '@/types/auth'

interface CourseTopic {
  id: number
  course_id: number
  week_number: number
  topic_title: string
  description?: string
  learning_materials?: string
  learning_methods?: string
  estimated_time?: number
  created_at: string
  updated_at: string
}

interface Props {
  course: Course
}

const props = defineProps<Props>()
const emit = defineEmits<{
  close: []
}>()

// State
const loading = ref(false)
const modalLoading = ref(false)
const deleteLoading = ref(false)
const showModal = ref(false)
const showDeleteDialog = ref(false)
const showSuccess = ref(false)
const showError = ref(false)
const successMessage = ref('')
const errorMessage = ref('')

const topics = ref<CourseTopic[]>([])
const selectedTopic = ref<CourseTopic | null>(null)
const modalMode = ref<'create' | 'edit'>('create')

// Form data
const formData = reactive({
  week_number: null as number | null,
  topic_title: '',
  description: '',
  learning_materials: '',
  learning_methods: '',
  estimated_time: null as number | null
})

// Computed
const modalTitle = computed(() => 
  modalMode.value === 'create' ? 'Add Topic' : 'Edit Topic'
)

const sortedTopics = computed(() => 
  [...topics.value].sort((a, b) => a.week_number - b.week_number)
)

const weekOptions = computed(() => {
  const usedWeeks = topics.value
    .filter(t => modalMode.value === 'create' || t.id !== selectedTopic.value?.id)
    .map(t => t.week_number)
  
  return Array.from({ length: 16 }, (_, i) => ({
    title: `Week ${i + 1}`,
    value: i + 1,
    disabled: usedWeeks.includes(i + 1)
  }))
})

// Validation rules
const weekRules = [
  (v: number) => !!v || 'Week number is required'
]

const titleRules = [
  (v: string) => !!v || 'Topic title is required',
  (v: string) => v.length >= 3 || 'Title must be at least 3 characters'
]

// Methods
const loadTopics = async () => {
  loading.value = true
  try {
    const response = await coursesAPI.getTopics(props.course.id)
    topics.value = response.data.data
  } catch (error: any) {
    errorMessage.value = 'Failed to load topics'
    showError.value = true
  } finally {
    loading.value = false
  }
}

const openCreateModal = () => {
  modalMode.value = 'create'
  resetForm()
  showModal.value = true
}

const openEditModal = (topic: CourseTopic) => {
  modalMode.value = 'edit'
  selectedTopic.value = topic
  populateForm(topic)
  showModal.value = true
}

const openDeleteDialog = (topic: CourseTopic) => {
  selectedTopic.value = topic
  showDeleteDialog.value = true
}

const resetForm = () => {
  Object.assign(formData, {
    week_number: null,
    topic_title: '',
    description: '',
    learning_materials: '',
    learning_methods: '',
    estimated_time: null
  })
}

const populateForm = (topic: CourseTopic) => {
  Object.assign(formData, {
    week_number: topic.week_number,
    topic_title: topic.topic_title,
    description: topic.description || '',
    learning_materials: topic.learning_materials || '',
    learning_methods: topic.learning_methods || '',
    estimated_time: topic.estimated_time
  })
}

const handleSubmit = async () => {
  modalLoading.value = true
  try {
    const data = {
      ...formData,
      course_id: props.course.id
    }
    
    if (modalMode.value === 'create') {
      await coursesAPI.createTopic(props.course.id, data)
      successMessage.value = 'Topic added successfully!'
    } else if (selectedTopic.value) {
      await coursesAPI.updateTopic(props.course.id, selectedTopic.value.id, data)
      successMessage.value = 'Topic updated successfully!'
    }
    
    showSuccess.value = true
    closeModal()
    await loadTopics()
  } catch (error: any) {
    errorMessage.value = error.response?.data?.message || 'Operation failed'
    showError.value = true
  } finally {
    modalLoading.value = false
  }
}

const confirmDelete = async () => {
  if (!selectedTopic.value) return
  
  deleteLoading.value = true
  try {
    await coursesAPI.deleteTopic(props.course.id, selectedTopic.value.id)
    successMessage.value = 'Topic deleted successfully!'
    showSuccess.value = true
    showDeleteDialog.value = false
    await loadTopics()
  } catch (error: any) {
    errorMessage.value = error.response?.data?.message || 'Delete failed'
    showError.value = true
  } finally {
    deleteLoading.value = false
  }
}

const closeModal = () => {
  showModal.value = false
  selectedTopic.value = null
  resetForm()
}

const getWeekColor = (weekNumber: number) => {
  const colors = ['primary', 'success', 'warning', 'info', 'secondary']
  return colors[(weekNumber - 1) % colors.length]
}

const formatTime = (minutes: number) => {
  if (minutes < 60) {
    return `${minutes} minutes`
  }
  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60
  return remainingMinutes > 0 
    ? `${hours}h ${remainingMinutes}m`
    : `${hours}h`
}

// Lifecycle
onMounted(() => {
  loadTopics()
})
</script>

<style scoped>
.topic-timeline {
  max-height: 600px;
  overflow-y: auto;
}

.v-timeline-item :deep(.v-timeline-item__body) {
  padding-bottom: 0;
}

.v-card {
  transition: transform 0.2s ease-in-out;
}

.v-card:hover {
  transform: translateY(-1px);
}
</style>
