/* Divider
******************************************************************************* */

.divider {
  --#{$prefix}divider-color: #{$divider-color};
  display: block;
  overflow: hidden;
  margin-block: $divider-margin-y;
  margin-inline: $divider-margin-x;
  text-align: center;
  white-space: nowrap;

  .divider-text {
    position: relative;
    display: inline-block;
    color: var(--#{$prefix}heading-color);
    font-size: $divider-font-size;
    padding-block: $divider-text-padding-y;
    padding-inline: $divider-text-padding-x;

    .icon-base {
      @include icon-base($divider-icon-size);
    }

    &::before,
    &::after {
      position: absolute;
      border-block-start: 1px solid var(--#{$prefix}divider-color);
      content: "";
      inline-size: 100vw;
      inset-block-start: 50%;
    }

    &::before {
      inset-inline-end: 100%;
    }

    &::after {
      inset-inline-start: 100%;
    }
  }

  &.text-start {
    .divider-text {
      padding-inline-start: 0;
    }
  }

  &.text-end {
    .divider-text {
      padding-inline-end: 0;
    }
  }

  &.text-start-center {
    .divider-text {
      inset-inline-start: -25%;
    }
  }

  &.text-end-center {
    .divider-text {
      inset-inline-end: -25%;
    }
  }

  // Dotted Divider
  &.divider-dotted {
    &::after,
    &::before,
    .divider-text::after,
    .divider-text::before {
      border-width: 0 1px 1px;
      border-style: dotted;
      border-color: var(--#{$prefix}divider-color);
    }
  }

  // Dashed Divider
  &.divider-dashed {
    &::after,
    &::before,
    .divider-text::after,
    .divider-text::before {
      border-width: 0 1px 1px;
      border-style: dashed;
      border-color: var(--#{$prefix}divider-color);
    }
  }

  // For Vertical Divider
  &.divider-vertical {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: unset;
    block-size: 100%;
    &::before,
    &::after {
      position: absolute;
      border-inline-start: 1px solid var(--#{$prefix}divider-color);
      content: "";
      inset-inline-start: 50%;
    }

    &::before {
      inset-block: 0 50%;
    }

    &::after {
      inset-block: 50% 0;
    }

    // Dashed Vertical Divider
    &.divider-dashed {
      &::before,
      &::after {
        border-width: 1px 1px 1px 0;
        border-style: dashed;
      }
    }

    // Dotted Vertical Divider
    &.divider-dotted {
      &::before,
      &::after {
        border-width: 1px 1px 1px 0;
        border-style: dotted;
      }
    }

    // For Vertical Divider text
    .divider-text {
      z-index: 1;
      padding: .5rem;
      background-color: var(--#{$prefix}paper-bg);
      &::before,
      &::after {
        content: unset;
      }


      // For card statistics Total Visits divider
      .badge-divider-bg {
        @include border-radius(50%);
        font-size: $divider-font-size - .1125rem;
        font-weight: $display-font-weight;
        padding-block: $divider-text-padding-x - .71rem;
        padding-inline: $divider-text-padding-x - .687rem;
      }
    }
  }
}

@each $state in map-keys($theme-colors) {
  .divider.divider-#{$state} {
    --#{$prefix}divider-color: var(--#{$prefix}#{$state});
  }
}
