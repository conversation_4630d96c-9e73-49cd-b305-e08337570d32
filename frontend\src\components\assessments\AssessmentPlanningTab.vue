<template>
  <div>
    <!-- Course Selection -->
    <v-row class="mb-6">
      <v-col cols="12" md="8">
        <v-select
          v-model="selectedCourse"
          :items="courseOptions"
          label="Select Course for Assessment Planning"
          variant="outlined"
          prepend-inner-icon="mdi-book-open-page-variant"
          clearable
          @update:model-value="handleCourseChange"
        />
      </v-col>
      
      <v-col cols="12" md="4" class="d-flex align-center gap-2">
        <v-btn
          color="primary"
          @click="openPlanningWizard"
          :disabled="!selectedCourse"
        >
          <v-icon start>mdi-wizard-hat</v-icon>
          Planning Wizard
        </v-btn>
        
        <v-btn
          color="secondary"
          variant="outlined"
          @click="showWeightValidation = true"
          :disabled="!selectedCourse"
        >
          <v-icon start>mdi-scale-balance</v-icon>
          Validate Weights
        </v-btn>
      </v-col>
    </v-row>

    <!-- Course Info Card -->
    <v-card v-if="selectedCourseInfo" class="mb-6" variant="outlined">
      <v-card-text>
        <div class="d-flex align-center justify-space-between">
          <div>
            <h3 class="text-h6 font-weight-bold">{{ selectedCourseInfo.name }}</h3>
            <p class="text-subtitle-2 text-medium-emphasis">
              {{ selectedCourseInfo.code }} • {{ selectedCourseInfo.credits }} SKS • Semester {{ selectedCourseInfo.semester }}
            </p>
          </div>
          
          <div class="text-right">
            <div class="text-h6 font-weight-bold">{{ totalPlans }}</div>
            <div class="text-caption text-medium-emphasis">Assessment Plans</div>
          </div>
        </div>
      </v-card-text>
    </v-card>

    <!-- Statistics Cards -->
    <v-row v-if="selectedCourse" class="mb-6">
      <v-col cols="12" sm="6" md="3">
        <v-card color="primary" variant="tonal">
          <v-card-text>
            <div class="d-flex align-center justify-space-between">
              <div>
                <p class="text-subtitle-2 text-medium-emphasis mb-1">Total Plans</p>
                <h2 class="text-h4 font-weight-bold">{{ totalPlans }}</h2>
              </div>
              <v-icon size="48" color="primary">mdi-calendar-clock</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
      
      <v-col cols="12" sm="6" md="3">
        <v-card color="success" variant="tonal">
          <v-card-text>
            <div class="d-flex align-center justify-space-between">
              <div>
                <p class="text-subtitle-2 text-medium-emphasis mb-1">Total Weight</p>
                <h2 class="text-h4 font-weight-bold">{{ totalWeight }}%</h2>
              </div>
              <v-icon size="48" color="success">mdi-scale-balance</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
      
      <v-col cols="12" sm="6" md="3">
        <v-card color="warning" variant="tonal">
          <v-card-text>
            <div class="d-flex align-center justify-space-between">
              <div>
                <p class="text-subtitle-2 text-medium-emphasis mb-1">CPMK Covered</p>
                <h2 class="text-h4 font-weight-bold">{{ cpmkCovered }}</h2>
              </div>
              <v-icon size="48" color="warning">mdi-target</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
      
      <v-col cols="12" sm="6" md="3">
        <v-card color="info" variant="tonal">
          <v-card-text>
            <div class="d-flex align-center justify-space-between">
              <div>
                <p class="text-subtitle-2 text-medium-emphasis mb-1">Avg Passing</p>
                <h2 class="text-h4 font-weight-bold">{{ avgPassingGrade }}%</h2>
              </div>
              <v-icon size="48" color="info">mdi-chart-line</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Assessment Plans Table -->
    <DataTable
      v-if="selectedCourse"
      title="Assessment Plans"
      icon="mdi-calendar-clock"
      item-name="Assessment Plan"
      :headers="tableHeaders"
      :items="assessmentPlans"
      :loading="loading"
      :total-items="totalItems"
      :filters="tableFilters"
      @add="openCreateModal"
      @edit="openEditModal"
      @delete="openDeleteDialog"
      @view="openViewModal"
      @refresh="loadAssessmentPlans"
      @search="handleSearch"
      @filter="handleFilter"
      @update:options="handleTableOptions"
    >
      <!-- Custom slots for specific columns -->
      <template #item.assessment_title="{ item }">
        <div>
          <div class="font-weight-medium">{{ item.assessment_title }}</div>
          <div class="text-caption text-medium-emphasis">Week {{ item.week_number }}</div>
        </div>
      </template>

      <template #item.cpmk="{ item }">
        <div v-if="item.cpmk_code">
          <v-chip
            color="primary"
            variant="tonal"
            size="small"
            class="mb-1"
          >
            {{ item.cpmk_code }}
          </v-chip>
          <div v-if="item.sub_cpmk_code" class="text-caption">
            Sub: {{ item.sub_cpmk_code }}
          </div>
        </div>
        <span v-else class="text-medium-emphasis">-</span>
      </template>

      <template #item.assessment_method="{ item }">
        <v-chip
          :color="getMethodTypeColor(item.assessment_method_type)"
          variant="tonal"
          size="small"
        >
          {{ item.assessment_method_name }}
        </v-chip>
      </template>

      <template #item.weight_percentage="{ item }">
        <div class="d-flex align-center">
          <v-progress-linear
            :model-value="item.weight_percentage"
            :color="getWeightColor(item.weight_percentage)"
            height="6"
            rounded
            class="mr-2"
            style="min-width: 60px;"
          />
          <span class="text-caption font-weight-medium">{{ item.weight_percentage }}%</span>
        </div>
      </template>

      <template #item.passing_grade="{ item }">
        <span class="font-weight-medium">{{ item.passing_grade }}%</span>
      </template>

      <template #item.due_date="{ item }">
        <div v-if="item.due_date">
          {{ formatDate(item.due_date) }}
        </div>
        <span v-else class="text-medium-emphasis">-</span>
      </template>

      <template #item.actions="{ item }">
        <div class="d-flex align-center gap-1">
          <v-btn
            icon
            size="small"
            variant="text"
            @click="openViewModal(item)"
          >
            <v-icon size="small">mdi-eye</v-icon>
            <v-tooltip activator="parent">View Details</v-tooltip>
          </v-btn>
          
          <v-btn
            icon
            size="small"
            variant="text"
            @click="openEditModal(item)"
          >
            <v-icon size="small">mdi-pencil</v-icon>
            <v-tooltip activator="parent">Edit Plan</v-tooltip>
          </v-btn>
          
          <v-btn
            icon
            size="small"
            variant="text"
            @click="openRubricModal(item)"
          >
            <v-icon size="small">mdi-format-list-checks</v-icon>
            <v-tooltip activator="parent">Manage Rubric</v-tooltip>
          </v-btn>
          
          <v-btn
            icon
            size="small"
            variant="text"
            color="error"
            @click="openDeleteDialog(item)"
          >
            <v-icon size="small">mdi-delete</v-icon>
            <v-tooltip activator="parent">Delete Plan</v-tooltip>
          </v-btn>
        </div>
      </template>
    </DataTable>

    <!-- Empty State -->
    <div v-else class="text-center py-12">
      <v-icon size="64" color="grey-lighten-2">mdi-calendar-clock</v-icon>
      <p class="text-h6 text-medium-emphasis mt-4">Select a Course to Start Assessment Planning</p>
      <p class="text-body-2 text-medium-emphasis">
        Choose a course from the dropdown above to create and manage assessment plans
      </p>
    </div>

    <!-- Planning Wizard Modal -->
    <v-dialog v-model="showPlanningWizard" max-width="1200" scrollable persistent>
      <v-card>
        <v-card-title class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <v-icon class="mr-2">mdi-wizard-hat</v-icon>
            <span class="text-h6 font-weight-bold">Assessment Planning Wizard</span>
          </div>
          <v-btn icon variant="text" @click="closePlanningWizard">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>
        
        <v-divider />
        
        <v-card-text>
          <AssessmentPlanningWizard
            v-if="selectedCourse"
            :course-id="selectedCourse"
            @close="closePlanningWizard"
            @completed="handleWizardCompleted"
          />
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- Weight Validation Modal -->
    <v-dialog v-model="showWeightValidation" max-width="800">
      <v-card>
        <v-card-title class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <v-icon class="mr-2">mdi-scale-balance</v-icon>
            <span class="text-h6 font-weight-bold">Weight Validation</span>
          </div>
          <v-btn icon variant="text" @click="showWeightValidation = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>
        
        <v-divider />
        
        <v-card-text>
          <AssessmentWeightValidation
            v-if="selectedCourse"
            :course-id="selectedCourse"
            @close="showWeightValidation = false"
          />
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- Rubric Management Modal -->
    <v-dialog v-model="showRubricModal" max-width="1000" scrollable>
      <v-card>
        <v-card-title class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <v-icon class="mr-2">mdi-format-list-checks</v-icon>
            <span class="text-h6 font-weight-bold">Rubric Management</span>
          </div>
          <v-btn icon variant="text" @click="showRubricModal = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>
        
        <v-divider />
        
        <v-card-text>
          <AssessmentRubricBuilder
            v-if="selectedPlan"
            :assessment-plan="selectedPlan"
            @close="showRubricModal = false"
            @updated="handleRubricUpdated"
          />
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import DataTable from '@/components/common/DataTable.vue'
import AssessmentPlanningWizard from '@/components/assessments/AssessmentPlanningWizard.vue'
import AssessmentWeightValidation from '@/components/assessments/AssessmentWeightValidation.vue'
import AssessmentRubricBuilder from '@/components/assessments/AssessmentRubricBuilder.vue'
import { assessmentPlansAPI, coursesAPI } from '@/services/api'
import type { AssessmentPlan, Course, PaginationParams } from '@/types/auth'

// State
const loading = ref(false)
const showPlanningWizard = ref(false)
const showWeightValidation = ref(false)
const showRubricModal = ref(false)

const assessmentPlans = ref<AssessmentPlan[]>([])
const courses = ref<Course[]>([])
const totalItems = ref(0)
const selectedCourse = ref<number | null>(null)
const selectedCourseInfo = ref<Course | null>(null)
const selectedPlan = ref<AssessmentPlan | null>(null)

// Pagination and filtering
const searchQuery = ref('')
const filters = ref<Record<string, any>>({})
const pagination = ref<PaginationParams>({
  page: 1,
  per_page: 20,
  sort_by: 'week_number',
  sort_order: 'asc'
})

// Statistics
const totalPlans = computed(() => assessmentPlans.value.length)
const totalWeight = computed(() => {
  return Math.round(assessmentPlans.value.reduce((sum, plan) => sum + plan.weight_percentage, 0))
})
const cpmkCovered = computed(() => {
  const uniqueCpmk = new Set(assessmentPlans.value.map(plan => plan.cpmk_id))
  return uniqueCpmk.size
})
const avgPassingGrade = computed(() => {
  if (assessmentPlans.value.length === 0) return 0
  const total = assessmentPlans.value.reduce((sum, plan) => sum + plan.passing_grade, 0)
  return Math.round(total / assessmentPlans.value.length)
})

// Computed options
const courseOptions = computed(() =>
  courses.value.map(course => ({
    title: `${course.code} - ${course.name}`,
    value: course.id
  }))
)

// Table configuration
const tableHeaders = [
  { key: 'assessment_title', title: 'Assessment Title', sortable: true, type: 'text' as const },
  { key: 'cpmk', title: 'CPMK', sortable: false, type: 'text' as const },
  { key: 'assessment_method', title: 'Method', sortable: false, type: 'text' as const },
  { key: 'weight_percentage', title: 'Weight', sortable: true, type: 'text' as const },
  { key: 'passing_grade', title: 'Passing Grade', sortable: true, type: 'text' as const },
  { key: 'due_date', title: 'Due Date', sortable: true, type: 'date' as const },
  { key: 'is_active', title: 'Status', sortable: true, type: 'boolean' as const }
]

const tableFilters = [
  {
    key: 'week_number',
    label: 'Week',
    options: Array.from({ length: 16 }, (_, i) => ({
      title: `Week ${i + 1}`,
      value: i + 1
    }))
  },
  {
    key: 'assessment_method_type',
    label: 'Assessment Type',
    options: [
      { title: 'Formative', value: 'formatif' },
      { title: 'Summative', value: 'sumatif' }
    ]
  },
  {
    key: 'is_active',
    label: 'Status',
    options: [
      { title: 'Active', value: true },
      { title: 'Inactive', value: false }
    ]
  }
]

// Methods
const loadCourses = async () => {
  try {
    const response = await coursesAPI.getAll({ per_page: 1000 })
    courses.value = response.data.data
  } catch (error) {
    console.error('Load courses error:', error)
  }
}

const loadAssessmentPlans = async () => {
  if (!selectedCourse.value) return

  loading.value = true
  try {
    const params = {
      ...pagination.value,
      search: searchQuery.value,
      ...filters.value
    }

    const response = await assessmentPlansAPI.getByCourse(selectedCourse.value, params)
    assessmentPlans.value = response.data.data
    totalItems.value = response.data.meta?.total || 0
  } catch (error: any) {
    console.error('Load assessment plans error:', error)
  } finally {
    loading.value = false
  }
}

const handleCourseChange = async () => {
  if (selectedCourse.value) {
    // Load course info
    try {
      const response = await coursesAPI.getById(selectedCourse.value)
      selectedCourseInfo.value = response.data
    } catch (error) {
      console.error('Load course info error:', error)
    }

    // Load assessment plans
    await loadAssessmentPlans()
  } else {
    selectedCourseInfo.value = null
    assessmentPlans.value = []
  }
}

const openPlanningWizard = () => {
  showPlanningWizard.value = true
}

const closePlanningWizard = () => {
  showPlanningWizard.value = false
}

const handleWizardCompleted = () => {
  closePlanningWizard()
  loadAssessmentPlans()
}

const openCreateModal = () => {
  // Implementation for create modal
  console.log('Open create modal')
}

const openEditModal = (plan: AssessmentPlan) => {
  // Implementation for edit modal
  console.log('Open edit modal', plan)
}

const openViewModal = (plan: AssessmentPlan) => {
  // Implementation for view modal
  console.log('Open view modal', plan)
}

const openRubricModal = (plan: AssessmentPlan) => {
  selectedPlan.value = plan
  showRubricModal.value = true
}

const openDeleteDialog = (plan: AssessmentPlan) => {
  // Implementation for delete dialog
  console.log('Open delete dialog', plan)
}

const handleRubricUpdated = () => {
  showRubricModal.value = false
  loadAssessmentPlans()
}

const handleSearch = (query: string) => {
  searchQuery.value = query
  pagination.value.page = 1
  loadAssessmentPlans()
}

const handleFilter = (filterValues: Record<string, any>) => {
  filters.value = filterValues
  pagination.value.page = 1
  loadAssessmentPlans()
}

const handleTableOptions = (options: any) => {
  pagination.value = {
    ...pagination.value,
    page: options.page,
    per_page: options.itemsPerPage,
    sort_by: options.sortBy?.[0]?.key || 'week_number',
    sort_order: options.sortBy?.[0]?.order || 'asc'
  }
  loadAssessmentPlans()
}

// Utility functions
const getMethodTypeColor = (type?: string) => {
  return type === 'formatif' ? 'success' : 'warning'
}

const getWeightColor = (weight: number) => {
  if (weight >= 30) return 'error'
  if (weight >= 20) return 'warning'
  if (weight >= 10) return 'success'
  return 'info'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('id-ID', {
    day: 'numeric',
    month: 'short',
    year: 'numeric'
  })
}

// Lifecycle
onMounted(() => {
  loadCourses()
})

// Watch for course changes
watch(selectedCourse, handleCourseChange)
</script>
