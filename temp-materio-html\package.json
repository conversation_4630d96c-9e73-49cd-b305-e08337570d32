{"name": "Materio-bootstrap-html-admin-template-free", "description": "Most Powerful & Comprehensive Free Bootstrap 5 HTML Admin Dashboard Template built for developers! 🚀", "license": "MIT", "version": "3.0.0", "author": {"name": "ThemeSelection", "url": "https://themeselection.com/"}, "bugs": {"url": "https://github.com/themeselection/materio-bootstrap-html-admin-template-free/issues"}, "private": false, "repository": {"type": "git", "url": "git+https://github.com/themeselection/materio-bootstrap-html-admin-template-free.git"}, "homepage": "https://themeselection.com/item/materio-dashboard-free-bootstrap/", "keywords": ["bootstrap", "front-end", "admin", "framework", "admin-dashboard", "webapp", "bootstrap5", "themeselection", "freebies"], "scripts": {"build": "npx gulp build", "build:js": "npx gulp build:js", "build:css": "npx gulp build:css", "build:copy": "npx gulp build:copy", "build:prod": "npx gulp build --env=production", "build:prod:js": "npx gulp build:js --env=production", "build:prod:css": "npx gulp build:css --env=production", "build:prod:copy": "npx gulp build:copy --env=production", "watch": "npx gulp watch", "serve": "npx gulp serve", "format:scss": "npx stylelint --fix \"**/*.scss\""}, "dependencies": {"@popperjs/core": "^2.11.8", "apexcharts": "~4.2.0", "bootstrap": "~5.3.5", "highlight.js": "~11.10.0", "@iconify/json": "^2.2.337", "@iconify/tools": "^4.1.2", "@iconify/types": "^2.0.0", "@iconify/utils": "^2.3.0", "jquery": "~3.7.1", "masonry-layout": "~4.2.2", "node-waves": "~0.7.6", "perfect-scrollbar": "~1.5.6"}, "devDependencies": {"@babel/core": "~7.23.9", "@babel/plugin-transform-object-rest-spread": "7.23.4", "@babel/plugin-transform-destructuring": "~7.23.3", "@babel/plugin-transform-template-literals": "~7.23.3", "@babel/preset-env": "~7.23.9", "@stylistic/stylelint-config": "^1.0.1", "@stylistic/stylelint-plugin": "^2.1.3", "ajv": "^8.17.1", "ansi-colors": "~4.1.3", "babel-loader": "~9.1.3", "browser-sync": "^3.0.4", "color-support": "~1.1.3", "css-loader": "~6.9.1", "deepmerge": "~4.3.1", "del": "~6.1.1", "eslint": "~9.16.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.4.0", "fancy-log": "~2.0.0", "gulp": "~4.0.2", "gulp-autoprefixer": "~8.0.0", "gulp-dart-sass": "^1.1.0", "gulp-environment": "~1.5.2", "gulp-exec": "^5.0.0", "gulp-if": "~3.0.0", "gulp-purgecss": "^7.0.2", "gulp-rename": "~2.0.0", "gulp-replace": "~1.1.4", "gulp-sourcemaps": "~3.0.0", "gulp-uglify": "^3.0.2", "gulp-useref": "^5.0.0", "html-loader": "~4.2.0", "js-beautify": "^1.15.4", "prettier": "^3.5.3", "sass": "1.78.0", "sass-loader": "~14.0.0", "string-replace-webpack-plugin": "~0.1.3", "style-loader": "~3.3.4", "stylelint": "^16.19.1", "stylelint-config-idiomatic-order": "^10.0.0", "stylelint-config-standard-scss": "^13.1.0", "stylelint-use-logical-spec": "^5.0.1", "terser-webpack-plugin": "~5.3.14", "webpack": "~5.89.0", "yarn": "~1.22.22"}, "overrides": {"prop-types": "15.8.1", "sass": "1.78.0"}, "resolutions": {"prop-types": "15.8.1", "sass": "1.78.0"}, "gulp-environment": {"environments": [{"name": "development", "aliases": ["dev"]}, {"name": "production", "aliases": ["prod"]}], "default": "development"}}