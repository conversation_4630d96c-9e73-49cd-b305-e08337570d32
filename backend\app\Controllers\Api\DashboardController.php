<?php

namespace App\Controllers\Api;

use App\Models\UserModel;

class DashboardController extends BaseApiController
{
    protected $userModel;

    public function __construct()
    {
        parent::__construct();
        $this->userModel = new UserModel();
    }

    /**
     * Get dashboard statistics
     */
    public function getStats()
    {
        try {
            // Get current user
            $currentUser = $this->getAuthenticatedUser();
            
            if (!$currentUser) {
                return $this->respondUnauthorized('Authentication required');
            }

            // Basic statistics
            $stats = [
                'users' => [
                    'total' => $this->userModel->countAllResults(),
                    'active' => $this->userModel->where('is_active', 1)->countAllResults(),
                    'inactive' => $this->userModel->where('is_active', 0)->countAllResults()
                ],
                'system' => [
                    'version' => '1.0.0',
                    'environment' => ENVIRONMENT,
                    'php_version' => PHP_VERSION,
                    'codeigniter_version' => \CodeIgniter\CodeIgniter::CI_VERSION
                ],
                'user_info' => [
                    'id' => $currentUser->user_id,
                    'username' => $currentUser->username,
                    'email' => $currentUser->email,
                    'role_id' => $currentUser->role_id,
                    'full_name' => $currentUser->full_name ?? 'N/A'
                ]
            ];

            // Add role-specific statistics
            if ($this->hasRole('admin') || $currentUser->role_id == 1) {
                $stats['admin'] = [
                    'total_logins_today' => $this->getTodayLogins(),
                    'recent_users' => $this->getRecentUsers(5)
                ];
            }

            return $this->respondSuccess($stats, 'Dashboard statistics retrieved successfully');

        } catch (\Exception $e) {
            return $this->respondError('Failed to retrieve dashboard statistics: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get today's login count
     */
    private function getTodayLogins()
    {
        $today = date('Y-m-d');
        return $this->userModel
            ->where('DATE(last_login)', $today)
            ->countAllResults();
    }

    /**
     * Get recent users
     */
    private function getRecentUsers($limit = 5)
    {
        $users = $this->userModel
            ->select('id, username, full_name, email, created_at, last_login')
            ->orderBy('created_at', 'DESC')
            ->findAll($limit);

        return $users;
    }

    /**
     * Get system health check
     */
    public function healthCheck()
    {
        try {
            // Check database connection
            $db = \Config\Database::connect();
            $dbStatus = $db->connID ? 'connected' : 'disconnected';

            // Check writable directories
            $writableChecks = [
                'logs' => is_writable(WRITEPATH . 'logs'),
                'cache' => is_writable(WRITEPATH . 'cache'),
                'uploads' => is_writable(WRITEPATH . 'uploads')
            ];

            $health = [
                'status' => 'healthy',
                'timestamp' => date('Y-m-d H:i:s'),
                'checks' => [
                    'database' => $dbStatus,
                    'writable_directories' => $writableChecks,
                    'php_version' => PHP_VERSION,
                    'memory_usage' => memory_get_usage(true),
                    'memory_limit' => ini_get('memory_limit')
                ]
            ];

            // Determine overall health status
            if ($dbStatus !== 'connected' || in_array(false, $writableChecks)) {
                $health['status'] = 'unhealthy';
            }

            return $this->respondSuccess($health, 'Health check completed');

        } catch (\Exception $e) {
            return $this->respondError('Health check failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get application information
     */
    public function getAppInfo()
    {
        $appInfo = [
            'name' => 'RPS Management System',
            'version' => '1.0.0',
            'description' => 'Sistem Manajemen Rencana Pembelajaran Semester',
            'framework' => [
                'name' => 'CodeIgniter',
                'version' => \CodeIgniter\CodeIgniter::CI_VERSION
            ],
            'frontend' => [
                'name' => 'Vue.js',
                'version' => '3.x'
            ],
            'api' => [
                'version' => 'v1',
                'base_url' => base_url('api/v1/'),
                'documentation' => base_url('api/docs')
            ],
            'environment' => ENVIRONMENT,
            'timezone' => date_default_timezone_get(),
            'server_time' => date('Y-m-d H:i:s')
        ];

        return $this->respondSuccess($appInfo, 'Application information retrieved successfully');
    }
}
