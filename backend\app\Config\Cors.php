<?php

namespace Config;

use CodeIgniter\Config\BaseConfig;

/**
 * Cross-Origin Resource Sharing (CORS) Configuration
 *
 * @see https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS
 */
class Cors extends BaseConfig
{
    /**
     * The default CORS configuration.
     *
     * @var array{
     *      allowedOrigins: list<string>,
     *      allowedOriginsPatterns: list<string>,
     *      supportsCredentials: bool,
     *      allowedHeaders: list<string>,
     *      exposedHeaders: list<string>,
     *      allowedMethods: list<string>,
     *      maxAge: int,
     *  }
     */
    public array $default = [
        /**
         * Origins for the `Access-Control-Allow-Origin` header.
         * Allow frontend development server and production domains
         */
        'allowedOrigins' => [
            'http://localhost:3000',  // Vue.js dev server
            'http://localhost:8080',  // Alternative dev port
            'http://localhost',       // Laragon local
            'http://127.0.0.1:3000',
            'http://127.0.0.1:8080',
        ],

        /**
         * Origin regex patterns for the `Access-Control-Allow-Origin` header.
         * Allow any localhost port for development
         */
        'allowedOriginsPatterns' => [
            'http://localhost:\d+',
            'http://127\.0\.0\.1:\d+',
        ],

        /**
         * Allow credentials for JWT authentication
         */
        'supportsCredentials' => true,

        /**
         * Set headers to allow for API communication
         */
        'allowedHeaders' => [
            'Origin',
            'Content-Type',
            'Accept',
            'Authorization',
            'X-Requested-With',
            'X-CSRF-TOKEN',
            'X-API-KEY',
        ],

        /**
         * Set headers to expose for frontend access
         */
        'exposedHeaders' => [
            'Authorization',
            'X-Total-Count',
            'X-Page-Count',
        ],

        /**
         * Set methods to allow for RESTful API
         */
        'allowedMethods' => [
            'GET',
            'POST',
            'PUT',
            'PATCH',
            'DELETE',
            'OPTIONS',
            'HEAD',
        ],

        /**
         * Set how many seconds the results of a preflight request can be cached.
         */
        'maxAge' => 7200,
    ];
}
