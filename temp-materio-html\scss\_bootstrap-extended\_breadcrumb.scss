// Breadcrumbs
// *******************************************************************************


.breadcrumb {
  --#{$prefix}breadcrumb-color: #{$breadcrumb-color};
}


.breadcrumb-item {
  a {
    color: var(--#{$prefix}breadcrumb-color);

    &:hover,
    &:focus {
      color: var(--#{$prefix}breadcrumb-item-active-color);
    }
  }
  &:not(.active).icon-base.breadcrumb-icon {
    color: var(--#{$prefix}breadcrumb-divider-color);
  }
}

.breadcrumb-item.active a {
  &,
  &:hover,
  &:focus,
  &:active {
    color: inherit;
  }
}

.breadcrumb-custom-icon .breadcrumb-item + .breadcrumb-item::before {
  content: none !important;
}
