# RPS Admin Dashboard - Materio Vue Implementation

This document provides instructions for using the newly implemented admin dashboard based on the Materio Vue template for the RPS (Rencana Pembelajaran Semester) system.

## 🚀 Features

- **Modern UI**: Based on Materio Vue template with Vuetify components
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- **Dashboard Analytics**: Overview of users, courses, CPMK, and CPL statistics
- **User Management**: Complete CRUD operations for user management
- **Course Management**: Manage academic courses with detailed information
- **CPMK Management**: Handle Course Learning Outcomes (Capaian Pembelajaran Mata Kuliah)
- **CPL Management**: Manage Program Learning Outcomes (Capaian Pembelajaran Lulusan)
- **Reports & Analytics**: Generate and view system reports
- **Settings**: System configuration and preferences

## 📁 File Structure

```
backend/
├── app/
│   ├── Controllers/
│   │   └── AdminController.php          # Main admin controller
│   ├── Views/
│   │   └── admin/
│   │       ├── layouts/
│   │       │   ├── main.php             # Main admin layout
│   │       │   └── blank.php            # Blank layout for auth pages
│   │       ├── components/
│   │       │   ├── sidebar.php          # Navigation sidebar
│   │       │   ├── navbar.php           # Top navigation bar
│   │       │   └── footer.php           # Footer component
│   │       ├── dashboard/
│   │       │   └── index.php            # Dashboard home page
│   │       ├── auth/
│   │       │   └── login.php            # Admin login page
│   │       ├── users/
│   │       │   └── index.php            # Users management
│   │       ├── courses/
│   │       │   └── index.php            # Courses management
│   │       ├── cpmk/
│   │       │   └── index.php            # CPMK management
│   │       ├── cpl/
│   │       │   └── index.php            # CPL management
│   │       ├── reports/
│   │       │   └── index.php            # Reports page
│   │       └── settings/
│   │           └── index.php            # Settings page
│   └── Config/
│       └── Routes.php                   # Updated with admin routes
└── public/
    └── admin/
        └── assets/
            ├── css/
            │   ├── materio-vue.css      # Compiled Materio Vue styles
            │   └── materio-admin.css    # Custom admin styles
            ├── js/
            │   └── materio-admin.js     # Custom admin JavaScript
            └── img/                     # Admin dashboard images
```

## 🛠 Installation & Setup

### 1. Prerequisites
- Web server (Apache/Nginx)
- PHP 7.4 or higher
- CodeIgniter 4 framework
- Modern web browser

### 2. Access the Admin Dashboard

1. **Start your web server**
2. **Navigate to the admin login page**:
   ```
   http://your-domain/backend/public/admin/login
   ```

### 3. Admin Routes

The following routes are available:

| Route | Description | Auth Required |
|-------|-------------|---------------|
| `/admin/login` | Admin login page | No |
| `/admin` | Dashboard home | Yes |
| `/admin/users` | Users management | Yes |
| `/admin/courses` | Courses management | Yes |
| `/admin/cpmk` | CPMK management | Yes |
| `/admin/cpl` | CPL management | Yes |
| `/admin/reports` | Reports & analytics | Yes |
| `/admin/settings` | System settings | Yes |
| `/admin/logout` | Logout | Yes |

## 🎨 Design Features

### Color Scheme
- **Primary**: #696cff (Materio Purple)
- **Success**: #71dd37 (Green)
- **Info**: #03c3ec (Blue)
- **Warning**: #ffab00 (Orange)
- **Danger**: #ff3e1d (Red)

### Icons
- **Icon Library**: Remix Icons (ri-*)
- **Usage**: `<i class="ri-icon-name"></i>`

### Components
- **Cards**: Modern card design with shadows
- **Buttons**: Gradient and outline styles
- **Forms**: Floating labels and validation
- **Tables**: Responsive with action buttons
- **Modals**: For create/edit operations

## 🔧 Customization

### Adding New Pages

1. **Create the view file**:
   ```php
   // backend/app/Views/admin/your-page/index.php
   <?= $this->extend('admin/layouts/main') ?>
   
   <?= $this->section('content') ?>
   <!-- Your content here -->
   <?= $this->endSection() ?>
   ```

2. **Add controller method**:
   ```php
   // In AdminController.php
   public function yourPage()
   {
       $data = [
           'title' => 'Your Page Title',
           'page' => 'your-page'
       ];
       return view('admin/your-page/index', $data);
   }
   ```

3. **Add route**:
   ```php
   // In Routes.php admin group
   $routes->get('your-page', 'AdminController::yourPage');
   ```

4. **Add to sidebar**:
   ```php
   // In sidebar.php
   <li class="menu-item <?= (isset($page) && $page == 'your-page') ? 'active' : '' ?>">
       <a href="<?= base_url('admin/your-page') ?>" class="menu-link">
           <i class="menu-icon ri-your-icon"></i>
           <div>Your Page</div>
       </a>
   </li>
   ```

### Custom Styling

Add custom CSS to `backend/public/admin/assets/css/materio-admin.css`:

```css
.your-custom-class {
    /* Your styles here */
}
```

### Custom JavaScript

Add custom JavaScript to `backend/public/admin/assets/js/materio-admin.js` or create page-specific scripts:

```php
<?= $this->section('page_js') ?>
<script>
// Your custom JavaScript
</script>
<?= $this->endSection() ?>
```

## 🔐 Authentication

The admin dashboard includes basic authentication:

- **Login Required**: All admin routes except login require authentication
- **Session Management**: Uses CodeIgniter sessions
- **Role-Based**: Checks for admin role (role_id = 1)

### Default Admin User
You'll need to create an admin user in your database with:
- `role_id = 1` (Admin role)
- Hashed password using `password_hash()`

## 📱 Responsive Design

The dashboard is fully responsive and works on:
- **Desktop**: Full sidebar and features
- **Tablet**: Collapsible sidebar
- **Mobile**: Hidden sidebar with toggle button

## 🧪 Testing

Run the test script to verify installation:

```bash
php test_admin_dashboard.php
```

This will check:
- Route accessibility
- File existence
- Asset availability
- Configuration setup

## 🚀 Performance Optimization

### CSS & JS Optimization
- Materio Vue CSS is pre-compiled
- Custom CSS is minified
- JavaScript is optimized for performance

### Caching
- Enable CodeIgniter caching for better performance
- Use browser caching for static assets

### Database
- Optimize database queries
- Use proper indexing
- Implement pagination for large datasets

## 🔧 Troubleshooting

### Common Issues

1. **404 Errors on Admin Routes**
   - Check if Routes.php is properly configured
   - Verify .htaccess file exists and is configured

2. **CSS/JS Not Loading**
   - Check file paths in layout files
   - Verify assets exist in public/admin/assets/

3. **Authentication Issues**
   - Check session configuration
   - Verify admin user exists with correct role

4. **Permission Errors**
   - Check file permissions on assets folder
   - Ensure web server can read files

### Debug Mode
Enable debug mode in CodeIgniter for detailed error messages:

```php
// In .env file
CI_ENVIRONMENT = development
```

## 📞 Support

For issues or questions:
1. Check the troubleshooting section
2. Review CodeIgniter 4 documentation
3. Check Materio template documentation

## 🎯 Next Steps

1. **Implement Authentication**: Set up proper user authentication
2. **Add Database Models**: Create models for courses, CPMK, CPL
3. **Implement CRUD Operations**: Complete create, read, update, delete functionality
4. **Add Validation**: Implement form validation and security measures
5. **Create Reports**: Build detailed reporting functionality
6. **Add File Upload**: Implement file upload for course materials
7. **Implement Search**: Add advanced search and filtering
8. **Add Notifications**: Implement real-time notifications
9. **Create API Endpoints**: Build REST API for mobile app integration
10. **Add Testing**: Implement unit and integration tests

---

**Happy Coding! 🎉**

The admin dashboard is now ready for use and further development. The Materio Vue template provides a solid foundation for building a comprehensive RPS management system.
