@echo off
echo RPS Management System - First Time Setup
echo.

echo [1/6] Setting up Backend...
cd /d "c:\laragon\www\rpswebid\backend"
echo Installing Composer dependencies...
composer install
if not exist .env (
    echo Creating .env file...
    copy .env.example .env
    echo Please edit .env file to configure your database settings
)

echo [2/6] Setting up Frontend...
cd /d "c:\laragon\www\rpswebid\frontend"
echo Installing npm dependencies...
npm install
if not exist .env (
    echo Creating .env file...
    copy .env.example .env
)

echo [3/6] Building Frontend for production...
npm run build

echo [4/6] Database setup...
cd /d "c:\laragon\www\rpswebid\backend"
echo Running migrations...
php spark migrate
echo Running seeders...
php spark db:seed

echo [5/6] Testing backend server...
start "Test Backend" cmd /c "php spark serve --host=0.0.0.0 --port=8080 & timeout /t 5 & exit"
timeout /t 6 /nobreak >nul

echo [6/6] Setup complete!
echo.
echo ✅ Setup completed successfully!
echo.
echo Next steps:
echo 1. Edit backend/.env for database configuration
echo 2. Edit frontend/.env for API configuration
echo 3. Run start-dev.bat for development mode
echo 4. Or run docker-start.bat for Docker mode
echo.
echo Default login credentials:
echo - Super Admin: <EMAIL> / password123
echo - Dosen: <EMAIL> / password123
echo - Mahasiswa: <EMAIL> / password123
echo.
pause
