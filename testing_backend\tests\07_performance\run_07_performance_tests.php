<?php

/**
 * Performance Tests
 * 
 * Measures response times, memory usage, and concurrent request handling
 */

// Ensure we have access to the test runner
if (!isset($testRunner)) {
    die("This test must be run through the TestRunner\n");
}

$testRunner->logSubSection('Performance Tests');

$tests = [
    'response_time_measurement' => 'Response Time Measurement',
    'memory_usage_tracking' => 'Memory Usage Tracking',
    'database_query_performance' => 'Database Query Performance',
    'api_endpoint_performance' => 'API Endpoint Performance',
    'concurrent_request_handling' => 'Concurrent Request Handling',
    'load_testing' => 'Basic Load Testing',
    'resource_utilization' => 'Resource Utilization'
];

$totalTests = count($tests);
$currentTest = 0;
$passedTests = 0;

foreach ($tests as $testKey => $testName) {
    $currentTest++;
    $testRunner->logProgress($currentTest, $totalTests, "Running {$testName}");
    
    $startTime = microtime(true);
    $testPassed = false;
    $message = '';
    $details = [];

    try {
        $baseUrl = $config['api']['base_url'];
        $performanceThresholds = $config['performance'];
        
        switch ($testKey) {
            case 'response_time_measurement':
                // Measure response times for various endpoints
                $endpoints = [
                    'backend_root' => str_replace('/api/v1', '', $baseUrl),
                    'auth_login' => $baseUrl . '/auth/login',
                    'auth_profile' => $baseUrl . '/auth/profile'
                ];
                
                $responseTimes = [];
                $totalRequests = 0;
                $fastRequests = 0;
                
                foreach ($endpoints as $name => $url) {
                    try {
                        $requestStart = microtime(true);
                        
                        if ($name === 'auth_login') {
                            // Test with invalid credentials to get quick response
                            $response = $testRunner->makeHttpRequest('POST', $url, ['username' => 'test', 'password' => 'test']);
                        } else {
                            $response = $testRunner->makeHttpRequest('GET', $url);
                        }
                        
                        $requestTime = round((microtime(true) - $requestStart) * 1000);
                        $responseTimes[$name] = [
                            'time_ms' => $requestTime,
                            'status_code' => $response['status_code'],
                            'fast' => $requestTime < $performanceThresholds['response_time']['good']
                        ];
                        
                        $totalRequests++;
                        if ($requestTime < $performanceThresholds['response_time']['good']) {
                            $fastRequests++;
                        }
                        
                    } catch (Exception $e) {
                        $responseTimes[$name] = [
                            'error' => $e->getMessage(),
                            'fast' => false
                        ];
                        $totalRequests++;
                    }
                }
                
                $averageTime = $totalRequests > 0 ? array_sum(array_column($responseTimes, 'time_ms')) / $totalRequests : 0;
                $fastPercentage = $totalRequests > 0 ? ($fastRequests / $totalRequests) * 100 : 0;
                
                $testPassed = $fastPercentage >= 70; // 70% of requests should be fast
                $message = $testPassed 
                    ? "Response times are good (avg: {$averageTime}ms, {$fastPercentage}% fast)"
                    : "Response times need improvement (avg: {$averageTime}ms, {$fastPercentage}% fast)";
                $details = [
                    'response_times' => $responseTimes,
                    'average_time_ms' => round($averageTime, 2),
                    'fast_percentage' => round($fastPercentage, 2),
                    'threshold_good_ms' => $performanceThresholds['response_time']['good']
                ];
                break;

            case 'memory_usage_tracking':
                // Track memory usage during test execution
                $memoryBefore = $testRunner->getMemoryUsage();
                
                // Perform some memory-intensive operations
                $testData = [];
                for ($i = 0; $i < 1000; $i++) {
                    $testData[] = str_repeat('test', 100);
                }
                
                $memoryAfter = $testRunner->getMemoryUsage();
                $memoryIncrease = $memoryAfter['current_mb'] - $memoryBefore['current_mb'];
                
                // Clean up
                unset($testData);
                
                $memoryFinal = $testRunner->getMemoryUsage();
                $memoryThreshold = $performanceThresholds['memory_usage']['max_mb'];
                
                $testPassed = $memoryAfter['peak_mb'] < $memoryThreshold;
                $message = $testPassed 
                    ? "Memory usage is within limits (peak: {$memoryAfter['peak_mb']}MB)"
                    : "Memory usage exceeds limits (peak: {$memoryAfter['peak_mb']}MB)";
                $details = [
                    'memory_before_mb' => $memoryBefore['current_mb'],
                    'memory_after_mb' => $memoryAfter['current_mb'],
                    'memory_final_mb' => $memoryFinal['current_mb'],
                    'peak_memory_mb' => $memoryAfter['peak_mb'],
                    'memory_increase_mb' => round($memoryIncrease, 2),
                    'threshold_mb' => $memoryThreshold
                ];
                break;

            case 'database_query_performance':
                // Test database query performance
                try {
                    $dbConfig = $config['database'];
                    $dsn = "pgsql:host={$dbConfig['hostname']};port={$dbConfig['port']};dbname={$dbConfig['database']}";
                    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
                    ]);
                    
                    $queryTests = [];
                    
                    // Test simple query
                    $queryStart = microtime(true);
                    $stmt = $pdo->query("SELECT COUNT(*) FROM users");
                    $userCount = $stmt->fetchColumn();
                    $simpleQueryTime = round((microtime(true) - $queryStart) * 1000);
                    
                    $queryTests['simple_query'] = [
                        'time_ms' => $simpleQueryTime,
                        'result' => $userCount,
                        'fast' => $simpleQueryTime < 100
                    ];
                    
                    // Test join query
                    $queryStart = microtime(true);
                    $stmt = $pdo->query("
                        SELECT u.username, r.name as role_name 
                        FROM users u 
                        LEFT JOIN roles r ON u.role_id = r.id 
                        LIMIT 10
                    ");
                    $joinResults = $stmt->fetchAll();
                    $joinQueryTime = round((microtime(true) - $queryStart) * 1000);
                    
                    $queryTests['join_query'] = [
                        'time_ms' => $joinQueryTime,
                        'result_count' => count($joinResults),
                        'fast' => $joinQueryTime < 200
                    ];
                    
                    $fastQueries = array_filter($queryTests, fn($test) => $test['fast']);
                    $testPassed = count($fastQueries) >= 1;
                    $message = $testPassed 
                        ? "Database query performance is good"
                        : "Database query performance needs improvement";
                    $details = ['query_tests' => $queryTests];
                    
                } catch (PDOException $e) {
                    $testPassed = false;
                    $message = "Database query performance test failed: " . $e->getMessage();
                    $details = ['error' => $e->getMessage()];
                }
                break;

            case 'api_endpoint_performance':
                // Test API endpoint performance
                $endpointTests = [];
                
                // Get auth token first
                $authToken = null;
                try {
                    $loginUrl = $baseUrl . '/auth/login';
                    $testUser = $config['test_data']['users']['admin'];
                    $loginData = ['username' => $testUser['username'], 'password' => $testUser['password']];
                    
                    $loginResponse = $testRunner->makeHttpRequest('POST', $loginUrl, $loginData);
                    if ($loginResponse['status_code'] === 200 && isset($loginResponse['json']['data']['token'])) {
                        $authToken = $loginResponse['json']['data']['token'];
                    }
                } catch (Exception $e) {
                    // Continue without auth token
                }
                
                $testEndpoints = [
                    'auth_login' => ['method' => 'POST', 'url' => $baseUrl . '/auth/login', 'data' => ['username' => 'test', 'password' => 'test']],
                    'auth_profile' => ['method' => 'GET', 'url' => $baseUrl . '/auth/profile', 'headers' => $authToken ? ['Authorization' => 'Bearer ' . $authToken] : []],
                    'users_list' => ['method' => 'GET', 'url' => $baseUrl . '/users', 'headers' => $authToken ? ['Authorization' => 'Bearer ' . $authToken] : []]
                ];
                
                foreach ($testEndpoints as $name => $endpoint) {
                    try {
                        $requestStart = microtime(true);
                        $response = $testRunner->makeHttpRequest(
                            $endpoint['method'], 
                            $endpoint['url'], 
                            $endpoint['data'] ?? null, 
                            $endpoint['headers'] ?? []
                        );
                        $requestTime = round((microtime(true) - $requestStart) * 1000);
                        
                        $endpointTests[$name] = [
                            'time_ms' => $requestTime,
                            'status_code' => $response['status_code'],
                            'fast' => $requestTime < $performanceThresholds['response_time']['acceptable']
                        ];
                        
                    } catch (Exception $e) {
                        $endpointTests[$name] = [
                            'error' => $e->getMessage(),
                            'fast' => false
                        ];
                    }
                }
                
                $fastEndpoints = array_filter($endpointTests, fn($test) => $test['fast']);
                $testPassed = count($fastEndpoints) >= count($endpointTests) * 0.6; // 60% should be fast
                $message = $testPassed 
                    ? "API endpoint performance is acceptable"
                    : "API endpoint performance needs improvement";
                $details = ['endpoint_tests' => $endpointTests];
                break;

            case 'concurrent_request_handling':
                // Simulate concurrent requests (simplified version)
                $concurrentTests = [];
                $concurrentCount = min(5, $performanceThresholds['concurrent_users']); // Limit for testing
                
                $urls = [
                    $baseUrl . '/auth/login',
                    str_replace('/api/v1', '', $baseUrl)
                ];
                
                foreach ($urls as $url) {
                    $requestTimes = [];
                    
                    // Make multiple requests quickly
                    for ($i = 0; $i < $concurrentCount; $i++) {
                        try {
                            $requestStart = microtime(true);
                            
                            if (str_contains($url, 'login')) {
                                $response = $testRunner->makeHttpRequest('POST', $url, ['username' => 'test', 'password' => 'test']);
                            } else {
                                $response = $testRunner->makeHttpRequest('GET', $url);
                            }
                            
                            $requestTime = round((microtime(true) - $requestStart) * 1000);
                            $requestTimes[] = $requestTime;
                            
                        } catch (Exception $e) {
                            $requestTimes[] = 5000; // Penalty for failed requests
                        }
                    }
                    
                    $averageTime = array_sum($requestTimes) / count($requestTimes);
                    $maxTime = max($requestTimes);
                    
                    $concurrentTests[basename($url)] = [
                        'average_time_ms' => round($averageTime, 2),
                        'max_time_ms' => $maxTime,
                        'request_count' => count($requestTimes),
                        'acceptable' => $averageTime < $performanceThresholds['response_time']['acceptable'] * 2
                    ];
                }
                
                $acceptableTests = array_filter($concurrentTests, fn($test) => $test['acceptable']);
                $testPassed = count($acceptableTests) >= 1;
                $message = $testPassed 
                    ? "Concurrent request handling is acceptable"
                    : "Concurrent request handling needs improvement";
                $details = ['concurrent_tests' => $concurrentTests];
                break;

            case 'load_testing':
                // Basic load testing
                $loadTests = [];
                $requestCount = 10;
                $successfulRequests = 0;
                $totalTime = 0;
                
                $testUrl = str_replace('/api/v1', '', $baseUrl);
                
                for ($i = 0; $i < $requestCount; $i++) {
                    try {
                        $requestStart = microtime(true);
                        $response = $testRunner->makeHttpRequest('GET', $testUrl);
                        $requestTime = round((microtime(true) - $requestStart) * 1000);
                        
                        if ($response['status_code'] === 200) {
                            $successfulRequests++;
                        }
                        
                        $totalTime += $requestTime;
                        
                    } catch (Exception $e) {
                        // Count as failed request
                    }
                }
                
                $averageTime = $requestCount > 0 ? $totalTime / $requestCount : 0;
                $successRate = ($successfulRequests / $requestCount) * 100;
                
                $testPassed = $successRate >= 90 && $averageTime < $performanceThresholds['response_time']['acceptable'];
                $message = $testPassed 
                    ? "Load testing passed (success rate: {$successRate}%, avg time: {$averageTime}ms)"
                    : "Load testing failed (success rate: {$successRate}%, avg time: {$averageTime}ms)";
                $details = [
                    'request_count' => $requestCount,
                    'successful_requests' => $successfulRequests,
                    'success_rate_percent' => round($successRate, 2),
                    'average_time_ms' => round($averageTime, 2),
                    'total_time_ms' => $totalTime
                ];
                break;

            case 'resource_utilization':
                // Check resource utilization
                $resourceTests = [];
                
                // Memory usage
                $memory = $testRunner->getMemoryUsage();
                $resourceTests['memory'] = [
                    'current_mb' => $memory['current_mb'],
                    'peak_mb' => $memory['peak_mb'],
                    'within_limits' => $memory['peak_mb'] < $performanceThresholds['memory_usage']['max_mb']
                ];
                
                // Check if we can measure CPU usage (simplified)
                $cpuStart = microtime(true);
                $iterations = 100000;
                for ($i = 0; $i < $iterations; $i++) {
                    $dummy = md5($i);
                }
                $cpuTime = round((microtime(true) - $cpuStart) * 1000);
                
                $resourceTests['cpu'] = [
                    'test_duration_ms' => $cpuTime,
                    'iterations' => $iterations,
                    'performance_rating' => $cpuTime < 100 ? 'Good' : ($cpuTime < 500 ? 'Acceptable' : 'Poor')
                ];
                
                $goodResources = 0;
                if ($resourceTests['memory']['within_limits']) $goodResources++;
                if ($cpuTime < 500) $goodResources++;
                
                $testPassed = $goodResources >= 1;
                $message = $testPassed 
                    ? "Resource utilization is acceptable"
                    : "Resource utilization needs attention";
                $details = ['resource_tests' => $resourceTests];
                break;
        }

    } catch (Exception $e) {
        $testPassed = false;
        $message = "Exception: " . $e->getMessage();
        $details = [
            'exception' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ];
    }

    $duration = round((microtime(true) - $startTime) * 1000);
    $status = $testPassed ? 'PASS' : 'FAIL';
    
    $testRunner->logTestResult($testName, $status, $message, $duration, $details);
    
    if ($testPassed) {
        $passedTests++;
    }
}

// Summary for this test category
$testRunner->logSubSection('Performance Tests Summary');
$testRunner->log('info', "Passed: {$passedTests}/{$totalTests} tests", 'SUMMARY');

if ($passedTests >= $totalTests * 0.6) { // 60% success rate for performance tests
    $testRunner->logSuccess("Performance tests passed! System performance is acceptable.");
    return true;
} else {
    $failedTests = $totalTests - $passedTests;
    $testRunner->logError("Performance tests failed! {$failedTests} test(s) indicate performance issues.");
    return false;
}
