/**
 * Materio Admin Dashboard JavaScript
 * Based on Bootstrap 5 and Materio Design
 */

(function() {
    'use strict';

    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        initializeAdmin();
    });

    function initializeAdmin() {
        initializeMobileMenu();
        initializeTooltips();
        initializeAlerts();
        initializeCharts();
        initializeFormValidation();
    }

    // Mobile Menu Toggle
    function initializeMobileMenu() {
        const menuToggle = document.querySelector('.layout-menu-toggle');
        const layoutMenu = document.querySelector('.layout-menu');
        const layoutOverlay = document.querySelector('.layout-overlay');

        if (menuToggle) {
            menuToggle.addEventListener('click', function() {
                toggleMobileMenu();
            });
        }

        if (layoutOverlay) {
            layoutOverlay.addEventListener('click', function() {
                closeMobileMenu();
            });
        }

        // Close menu on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeMobileMenu();
            }
        });
    }

    function toggleMobileMenu() {
        const layoutMenu = document.querySelector('.layout-menu');
        const layoutOverlay = document.querySelector('.layout-overlay');

        if (layoutMenu && layoutOverlay) {
            layoutMenu.classList.toggle('show');
            layoutOverlay.classList.toggle('show');
            document.body.classList.toggle('menu-open');
        }
    }

    function closeMobileMenu() {
        const layoutMenu = document.querySelector('.layout-menu');
        const layoutOverlay = document.querySelector('.layout-overlay');

        if (layoutMenu && layoutOverlay) {
            layoutMenu.classList.remove('show');
            layoutOverlay.classList.remove('show');
            document.body.classList.remove('menu-open');
        }
    }

    // Initialize Bootstrap Tooltips
    function initializeTooltips() {
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    }

    // Auto-hide alerts
    function initializeAlerts() {
        const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
        alerts.forEach(function(alert) {
            setTimeout(function() {
                if (alert && alert.parentNode) {
                    alert.style.opacity = '0';
                    alert.style.transform = 'translateY(-10px)';
                    setTimeout(function() {
                        if (alert.parentNode) {
                            alert.parentNode.removeChild(alert);
                        }
                    }, 300);
                }
            }, 5000);
        });
    }

    // Initialize Charts (placeholder for chart libraries)
    function initializeCharts() {
        // This would integrate with Chart.js or ApexCharts
        const chartElements = document.querySelectorAll('[data-chart]');
        chartElements.forEach(function(element) {
            const chartType = element.getAttribute('data-chart');
            const chartData = element.getAttribute('data-chart-data');
            
            // Placeholder for chart initialization
            console.log('Chart element found:', chartType, chartData);
        });
    }

    // Form Validation
    function initializeFormValidation() {
        const forms = document.querySelectorAll('.needs-validation');
        forms.forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            });
        });
    }

    // Utility Functions
    window.AdminUtils = {
        // Show notification
        showNotification: function(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;
            
            document.body.appendChild(notification);
            
            // Auto remove after 5 seconds
            setTimeout(function() {
                if (notification.parentNode) {
                    notification.classList.remove('show');
                    setTimeout(function() {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 150);
                }
            }, 5000);
        },

        // Confirm dialog
        confirm: function(message, callback) {
            if (confirm(message)) {
                if (typeof callback === 'function') {
                    callback();
                }
                return true;
            }
            return false;
        },

        // AJAX helper
        ajax: function(options) {
            const defaults = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            };

            const config = Object.assign({}, defaults, options);

            return fetch(config.url, {
                method: config.method,
                headers: config.headers,
                body: config.data ? JSON.stringify(config.data) : null
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .catch(error => {
                console.error('AJAX Error:', error);
                throw error;
            });
        },

        // Format number with commas
        formatNumber: function(num) {
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        },

        // Format date
        formatDate: function(date, format = 'Y-m-d') {
            const d = new Date(date);
            const year = d.getFullYear();
            const month = String(d.getMonth() + 1).padStart(2, '0');
            const day = String(d.getDate()).padStart(2, '0');
            
            switch(format) {
                case 'Y-m-d':
                    return `${year}-${month}-${day}`;
                case 'd/m/Y':
                    return `${day}/${month}/${year}`;
                case 'd M Y':
                    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                                  'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                    return `${day} ${months[d.getMonth()]} ${year}`;
                default:
                    return d.toLocaleDateString();
            }
        }
    };

    // Global event handlers
    document.addEventListener('click', function(e) {
        // Handle delete confirmations
        if (e.target.classList.contains('btn-delete') || e.target.closest('.btn-delete')) {
            const button = e.target.classList.contains('btn-delete') ? e.target : e.target.closest('.btn-delete');
            const message = button.getAttribute('data-confirm') || 'Are you sure you want to delete this item?';
            
            if (!confirm(message)) {
                e.preventDefault();
                return false;
            }
        }

        // Handle AJAX forms
        if (e.target.classList.contains('btn-ajax') || e.target.closest('.btn-ajax')) {
            e.preventDefault();
            const button = e.target.classList.contains('btn-ajax') ? e.target : e.target.closest('.btn-ajax');
            const form = button.closest('form');
            
            if (form) {
                const formData = new FormData(form);
                const url = form.action || window.location.href;
                
                AdminUtils.ajax({
                    url: url,
                    method: form.method || 'POST',
                    data: Object.fromEntries(formData)
                })
                .then(response => {
                    if (response.success) {
                        AdminUtils.showNotification(response.message || 'Operation completed successfully', 'success');
                        if (response.redirect) {
                            window.location.href = response.redirect;
                        }
                    } else {
                        AdminUtils.showNotification(response.message || 'An error occurred', 'danger');
                    }
                })
                .catch(error => {
                    AdminUtils.showNotification('An error occurred while processing your request', 'danger');
                });
            }
        }
    });

})();
