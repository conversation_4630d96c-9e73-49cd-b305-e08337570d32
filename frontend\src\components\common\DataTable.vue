<template>
  <v-card>
    <!-- Table Header -->
    <v-card-title class="d-flex align-center justify-space-between">
      <div class="d-flex align-center">
        <v-icon v-if="icon" class="mr-2">{{ icon }}</v-icon>
        <span class="text-h6 font-weight-bold">{{ title }}</span>
      </div>
      
      <div class="d-flex align-center gap-2">
        <!-- Search -->
        <v-text-field
          v-model="searchQuery"
          prepend-inner-icon="mdi-magnify"
          label="Search..."
          variant="outlined"
          density="compact"
          hide-details
          clearable
          style="min-width: 250px;"
          @input="handleSearch"
        />
        
        <!-- Add Button -->
        <v-btn
          v-if="showAddButton"
          color="primary"
          @click="$emit('add')"
        >
          <v-icon start>mdi-plus</v-icon>
          Add {{ itemName }}
        </v-btn>
        
        <!-- Refresh Button -->
        <v-btn
          icon
          variant="text"
          @click="$emit('refresh')"
          :loading="loading"
        >
          <v-icon>mdi-refresh</v-icon>
        </v-btn>
      </div>
    </v-card-title>

    <v-divider />

    <!-- Filters -->
    <v-card-text v-if="filters.length > 0" class="pb-0">
      <v-row>
        <v-col
          v-for="filter in filters"
          :key="filter.key"
          cols="12"
          sm="6"
          md="3"
        >
          <v-select
            v-model="filterValues[filter.key]"
            :items="filter.options"
            :label="filter.label"
            variant="outlined"
            density="compact"
            clearable
            @update:model-value="handleFilter"
          />
        </v-col>
      </v-row>
    </v-card-text>

    <!-- Data Table -->
    <v-data-table
      :headers="headers"
      :items="items"
      :loading="loading"
      :items-per-page="itemsPerPage"
      :server-items-length="totalItems"
      class="elevation-0"
      @update:options="handleTableOptions"
    >
      <!-- Custom column slots -->
      <template
        v-for="header in headers"
        :key="header.key"
        #[`item.${header.key}`]="{ item }"
      >
        <slot
          :name="`item.${header.key}`"
          :item="item"
          :value="item[header.key]"
        >
          <!-- Default rendering -->
          <span v-if="header.type === 'text'">{{ item[header.key] }}</span>
          
          <!-- Boolean chip -->
          <v-chip
            v-else-if="header.type === 'boolean'"
            :color="item[header.key] ? 'success' : 'error'"
            size="small"
            variant="tonal"
          >
            {{ item[header.key] ? 'Active' : 'Inactive' }}
          </v-chip>
          
          <!-- Date formatting -->
          <span v-else-if="header.type === 'date'">
            {{ formatDate(item[header.key]) }}
          </span>
          
          <!-- Badge/chip -->
          <v-chip
            v-else-if="header.type === 'badge'"
            :color="getBadgeColor(item[header.key])"
            size="small"
            variant="tonal"
          >
            {{ item[header.key] }}
          </v-chip>
          
          <!-- Default -->
          <span v-else>{{ item[header.key] }}</span>
        </slot>
      </template>

      <!-- Actions column -->
      <template #item.actions="{ item }">
        <div class="d-flex align-center gap-1">
          <v-btn
            icon
            size="small"
            variant="text"
            @click="$emit('view', item)"
          >
            <v-icon size="small">mdi-eye</v-icon>
            <v-tooltip activator="parent">View</v-tooltip>
          </v-btn>
          
          <v-btn
            icon
            size="small"
            variant="text"
            @click="$emit('edit', item)"
          >
            <v-icon size="small">mdi-pencil</v-icon>
            <v-tooltip activator="parent">Edit</v-tooltip>
          </v-btn>
          
          <v-btn
            icon
            size="small"
            variant="text"
            color="error"
            @click="$emit('delete', item)"
          >
            <v-icon size="small">mdi-delete</v-icon>
            <v-tooltip activator="parent">Delete</v-tooltip>
          </v-btn>
        </div>
      </template>

      <!-- No data -->
      <template #no-data>
        <div class="text-center py-8">
          <v-icon size="64" color="grey-lighten-2">{{ noDataIcon }}</v-icon>
          <p class="text-h6 text-medium-emphasis mt-4">{{ noDataText }}</p>
          <v-btn
            v-if="showAddButton"
            color="primary"
            @click="$emit('add')"
            class="mt-2"
          >
            <v-icon start>mdi-plus</v-icon>
            Add {{ itemName }}
          </v-btn>
        </div>
      </template>

      <!-- Loading -->
      <template #loading>
        <div class="text-center py-8">
          <v-progress-circular indeterminate color="primary" />
          <p class="text-body-2 text-medium-emphasis mt-2">Loading data...</p>
        </div>
      </template>
    </v-data-table>
  </v-card>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface TableHeader {
  key: string
  title: string
  sortable?: boolean
  type?: 'text' | 'boolean' | 'date' | 'badge'
  width?: string
  align?: 'start' | 'center' | 'end'
}

interface FilterOption {
  key: string
  label: string
  options: Array<{ title: string; value: any }>
}

interface Props {
  title: string
  icon?: string
  itemName: string
  headers: TableHeader[]
  items: any[]
  loading?: boolean
  totalItems?: number
  itemsPerPage?: number
  showAddButton?: boolean
  filters?: FilterOption[]
  noDataText?: string
  noDataIcon?: string
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  totalItems: 0,
  itemsPerPage: 20,
  showAddButton: true,
  filters: () => [],
  noDataText: 'No data available',
  noDataIcon: 'mdi-database-off'
})

const emit = defineEmits<{
  add: []
  edit: [item: any]
  delete: [item: any]
  view: [item: any]
  refresh: []
  search: [query: string]
  filter: [filters: Record<string, any>]
  'update:options': [options: any]
}>()

// State
const searchQuery = ref('')
const filterValues = ref<Record<string, any>>({})
const currentPage = ref(1)
const sortBy = ref([])

// Computed
const tableHeaders = computed(() => [
  ...props.headers,
  {
    key: 'actions',
    title: 'Actions',
    sortable: false,
    width: '120px',
    align: 'center' as const
  }
] as TableHeader[])

// Methods
const handleSearch = () => {
  emit('search', searchQuery.value)
}

const handleFilter = () => {
  emit('filter', filterValues.value)
}

const handleTableOptions = (options: any) => {
  currentPage.value = options.page
  sortBy.value = options.sortBy
  emit('update:options', options)
}

const formatDate = (dateString: string) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const getBadgeColor = (value: string) => {
  const colorMap: Record<string, string> = {
    active: 'success',
    inactive: 'error',
    pending: 'warning',
    approved: 'success',
    rejected: 'error',
    draft: 'info'
  }
  return colorMap[value?.toLowerCase()] || 'primary'
}

// Watch for prop changes
watch(() => props.items, () => {
  // Reset to first page when items change
  currentPage.value = 1
})
</script>

<style scoped>
.v-data-table {
  border-radius: 0 0 4px 4px;
}

.v-data-table :deep(.v-data-table__td) {
  padding: 8px 16px;
}

.v-data-table :deep(.v-data-table__th) {
  padding: 12px 16px;
  font-weight: 600;
}
</style>
