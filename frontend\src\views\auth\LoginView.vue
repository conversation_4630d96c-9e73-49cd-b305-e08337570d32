<template>
  <v-container fluid class="login-container fill-height">
    <v-row align="center" justify="center" class="fill-height no-gutters">
      <v-col cols="12" sm="10" md="8" lg="6" xl="4" class="d-flex justify-center">
        <v-card class="login-card elevation-12 rounded-lg" max-width="400" width="100%">
          <v-card-text class="pa-6 pa-sm-8">
            <!-- Logo and Title -->
            <div class="text-center mb-6">
              <v-avatar
                size="60"
                class="mx-auto mb-3 elevation-2"
                color="primary"
              >
                <v-icon size="30" color="white">mdi-school</v-icon>
              </v-avatar>
              <h1 class="text-h5 font-weight-bold text-primary mb-1">
                RPS Management
              </h1>
              <p class="text-body-2 text-medium-emphasis">
                Sistem Manajemen Rencana Pembelajaran Semester
              </p>
            </div>

            <!-- Login Form -->
            <v-form ref="form" v-model="valid" @submit.prevent="handleLogin">
              <v-text-field
                v-model="credentials.username"
                :rules="usernameRules"
                label="Username atau Email"
                prepend-inner-icon="mdi-account"
                variant="outlined"
                class="mb-3"
                :disabled="loading"
                autofocus
              />

              <v-text-field
                v-model="credentials.password"
                :rules="passwordRules"
                :type="showPassword ? 'text' : 'password'"
                label="Password"
                prepend-inner-icon="mdi-lock"
                :append-inner-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
                @click:append-inner="showPassword = !showPassword"
                variant="outlined"
                class="mb-3"
                :disabled="loading"
              />

              <v-checkbox
                v-model="credentials.remember_me"
                label="Remember me"
                color="primary"
                class="mb-3"
                :disabled="loading"
              />

              <!-- Error Alert -->
              <v-alert
                v-if="error"
                type="error"
                variant="tonal"
                class="mb-4"
                closable
                @click:close="error = null"
              >
                {{ error }}
              </v-alert>

              <!-- Login Button -->
              <v-btn
                type="submit"
                color="primary"
                size="large"
                block
                :loading="loading"
                :disabled="!valid"
                class="mb-4"
              >
                <v-icon start>mdi-login</v-icon>
                Login
              </v-btn>
            </v-form>

            <!-- Additional Links -->
            <div class="text-center">
              <v-btn
                variant="text"
                color="primary"
                size="small"
                @click="showForgotPassword = true"
              >
                Forgot Password?
              </v-btn>
            </div>
          </v-card-text>
        </v-card>

        <!-- System Info -->
        <div class="text-center mt-4">
          <p class="text-caption text-medium-emphasis mb-1">
            {{ appName }} v{{ appVersion }}
          </p>
          <p class="text-caption text-medium-emphasis">
            © {{ currentYear }} {{ organizationName }}
          </p>
        </div>
      </v-col>
    </v-row>

    <!-- Forgot Password Dialog -->
    <v-dialog v-model="showForgotPassword" max-width="400">
      <v-card>
        <v-card-title class="text-h6">Reset Password</v-card-title>
        <v-card-text>
          <v-text-field
            v-model="resetEmail"
            label="Email Address"
            type="email"
            variant="outlined"
            prepend-inner-icon="mdi-email"
          />
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn @click="showForgotPassword = false">Cancel</v-btn>
          <v-btn color="primary" @click="handleForgotPassword">
            Send Reset Link
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import type { LoginCredentials } from '@/types/auth'

const router = useRouter()
const authStore = useAuthStore()

// State
const form = ref()
const valid = ref(false)
const loading = ref(false)
const error = ref<string | null>(null)
const showPassword = ref(false)
const showForgotPassword = ref(false)
const resetEmail = ref('')

const credentials = ref<LoginCredentials>({
  username: '',
  password: '',
  remember_me: false
})

// Validation rules
const usernameRules = [
  (v: string) => !!v || 'Username atau email harus diisi',
  (v: string) => v.length >= 3 || 'Username minimal 3 karakter'
]

const passwordRules = [
  (v: string) => !!v || 'Password harus diisi',
  (v: string) => v.length >= 6 || 'Password minimal 6 karakter'
]

// Computed
const appName = computed(() => import.meta.env.VITE_APP_NAME || 'RPS Management System')
const appVersion = computed(() => import.meta.env.VITE_APP_VERSION || '1.0.0')
const organizationName = computed(() => import.meta.env.VITE_ORGANIZATION_NAME || 'Universitas Example')
const currentYear = computed(() => new Date().getFullYear())

// Methods
const handleLogin = async () => {
  if (!valid.value) return

  loading.value = true
  error.value = null

  try {
    await authStore.login(credentials.value)
    router.push('/dashboard')
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Login gagal. Silakan coba lagi.'
  } finally {
    loading.value = false
  }
}

const handleForgotPassword = async () => {
  if (!resetEmail.value) return

  try {
    // TODO: Implement forgot password API call
    console.log('Reset password for:', resetEmail.value)
    showForgotPassword.value = false
    resetEmail.value = ''
    
    // Show success message
    // You can use a toast notification here
  } catch (err) {
    console.error('Forgot password error:', err)
  }
}

// Lifecycle
onMounted(() => {
  // Clear any existing errors
  error.value = null
  
  // Focus on username field
  setTimeout(() => {
    const usernameField = document.querySelector('input[type="text"]') as HTMLInputElement
    if (usernameField) {
      usernameField.focus()
    }
  }, 100)
})
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 16px;
}

.login-card {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95) !important;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.theme--dark .login-card {
  background: rgba(33, 33, 33, 0.95) !important;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.v-img {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.v-btn {
  text-transform: none;
  font-weight: 500;
}

.v-text-field {
  margin-bottom: 8px;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .login-container {
    padding: 8px;
  }

  .login-card {
    margin: 0;
  }

  .v-card-text {
    padding: 16px !important;
  }
}

/* Animation for form elements */
.login-card {
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Ensure proper spacing */
.no-gutters {
  margin: 0 !important;
}

.no-gutters > .col {
  padding: 0 !important;
}
</style>
