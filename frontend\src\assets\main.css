@import './base.css';

/* Remove conflicting styles that interfere with Vuetify */
#app {
  /* Remove max-width, margin, padding, and grid that conflict with Vuetify layout */
  font-weight: normal;
}

/* Keep only necessary link styles */
a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

/* Remove media query that forces flex layout on body and grid on #app */
