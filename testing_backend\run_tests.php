<?php

/**
 * Main Test Runner Script
 * 
 * This script orchestrates the execution of all backend tests
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set time limit for testing
set_time_limit(300); // 5 minutes

// Include required files
require_once __DIR__ . '/config/test_config.php';
require_once __DIR__ . '/utils/TestRunner.php';

try {
    // Load configuration
    $config = include __DIR__ . '/config/test_config.php';
    
    // Initialize test runner
    $testRunner = new TestRunner($config);
    
    // Display header
    echo "\n";
    echo "╔══════════════════════════════════════════════════════════════╗\n";
    echo "║                RPS Backend Testing Framework                 ║\n";
    echo "║                                                              ║\n";
    echo "║  Comprehensive testing for RPS Management System Backend    ║\n";
    echo "╚══════════════════════════════════════════════════════════════╝\n";
    echo "\n";
    
    // Run all tests
    $summary = $testRunner->runAllTests();
    
    // Generate HTML report
    generateHtmlReport($testRunner, $summary, $config);
    
    // Display final summary
    echo "\n";
    echo "╔══════════════════════════════════════════════════════════════╗\n";
    echo "║                      FINAL SUMMARY                          ║\n";
    echo "╠══════════════════════════════════════════════════════════════╣\n";
    printf("║  Total Tests: %-10d                                   ║\n", $summary['total']);
    printf("║  Passed:      %-10d (%.1f%%)                         ║\n", $summary['passed'], $summary['success_rate']);
    printf("║  Failed:      %-10d                                   ║\n", $summary['failed']);
    printf("║  Skipped:     %-10d                                   ║\n", $summary['skipped']);
    printf("║  Duration:    %-10dms                                ║\n", $summary['session_duration']);
    echo "╚══════════════════════════════════════════════════════════════╝\n";
    echo "\n";
    
    if ($summary['failed'] === 0) {
        echo "🎉 ALL TESTS PASSED! The backend is ready for production.\n";
        exit(0);
    } else {
        echo "❌ SOME TESTS FAILED! Please review the test results and fix the issues.\n";
        exit(1);
    }
    
} catch (Exception $e) {
    echo "💥 FATAL ERROR: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}

/**
 * Generate HTML test report
 */
function generateHtmlReport($testRunner, $summary, $config)
{
    $results = $testRunner->getLogger()->getTestResults();
    $reportPath = __DIR__ . '/reports/html/test_report.html';
    
    // Ensure directory exists
    $reportDir = dirname($reportPath);
    if (!is_dir($reportDir)) {
        mkdir($reportDir, 0755, true);
    }
    
    $html = generateHtmlContent($summary, $results, $config);
    file_put_contents($reportPath, $html);
    
    echo "📊 HTML report generated: {$reportPath}\n";
}

/**
 * Generate HTML content for the report
 */
function generateHtmlContent($summary, $results, $config)
{
    $timestamp = date('Y-m-d H:i:s');
    $successRate = $summary['success_rate'];
    $statusColor = $successRate >= 90 ? '#4CAF50' : ($successRate >= 70 ? '#FF9800' : '#F44336');
    
    $html = <<<HTML
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RPS Backend Test Report</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f5f5; color: #333; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; text-align: center; }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .header p { font-size: 1.2em; opacity: 0.9; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
        .summary-card h3 { color: #666; font-size: 0.9em; text-transform: uppercase; margin-bottom: 10px; }
        .summary-card .value { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .summary-card .label { color: #888; font-size: 0.9em; }
        .success-rate { color: {$statusColor}; }
        .test-results { background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden; }
        .test-results h2 { background: #f8f9fa; padding: 20px; margin: 0; border-bottom: 1px solid #dee2e6; }
        .test-item { padding: 15px 20px; border-bottom: 1px solid #eee; display: flex; align-items: center; justify-content: space-between; }
        .test-item:last-child { border-bottom: none; }
        .test-name { font-weight: 500; flex: 1; }
        .test-status { padding: 5px 12px; border-radius: 20px; font-size: 0.8em; font-weight: bold; text-transform: uppercase; }
        .status-pass { background: #d4edda; color: #155724; }
        .status-fail { background: #f8d7da; color: #721c24; }
        .status-skip { background: #fff3cd; color: #856404; }
        .test-duration { color: #666; font-size: 0.9em; margin-left: 10px; }
        .test-message { color: #666; font-size: 0.9em; margin-top: 5px; }
        .progress-bar { background: #e9ecef; height: 20px; border-radius: 10px; overflow: hidden; margin: 20px 0; }
        .progress-fill { background: {$statusColor}; height: 100%; transition: width 0.3s ease; }
        .footer { text-align: center; margin-top: 30px; color: #666; }
        .category-section { margin-bottom: 30px; }
        .category-header { background: #f8f9fa; padding: 15px 20px; border-left: 4px solid #007bff; margin-bottom: 10px; }
        .category-header h3 { color: #007bff; margin: 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 RPS Backend Test Report</h1>
            <p>Generated on {$timestamp}</p>
        </div>
        
        <div class="summary">
            <div class="summary-card">
                <h3>Total Tests</h3>
                <div class="value">{$summary['total']}</div>
                <div class="label">Executed</div>
            </div>
            <div class="summary-card">
                <h3>Success Rate</h3>
                <div class="value success-rate">{$summary['success_rate']}%</div>
                <div class="label">Overall</div>
            </div>
            <div class="summary-card">
                <h3>Passed</h3>
                <div class="value" style="color: #4CAF50;">{$summary['passed']}</div>
                <div class="label">Tests</div>
            </div>
            <div class="summary-card">
                <h3>Failed</h3>
                <div class="value" style="color: #F44336;">{$summary['failed']}</div>
                <div class="label">Tests</div>
            </div>
            <div class="summary-card">
                <h3>Duration</h3>
                <div class="value">{$summary['session_duration']}</div>
                <div class="label">Milliseconds</div>
            </div>
        </div>
        
        <div class="progress-bar">
            <div class="progress-fill" style="width: {$summary['success_rate']}%;"></div>
        </div>
        
        <div class="test-results">
            <h2>📋 Test Results</h2>
HTML;

    // Group results by category
    $categories = [];
    foreach ($results as $result) {
        $category = 'General';
        if (preg_match('/^(\d+_\w+)/', $result['test_name'], $matches)) {
            $category = $matches[1];
        }
        $categories[$category][] = $result;
    }

    foreach ($categories as $categoryName => $categoryResults) {
        $html .= "<div class='category-section'>";
        $html .= "<div class='category-header'><h3>" . ucwords(str_replace('_', ' ', $categoryName)) . "</h3></div>";
        
        foreach ($categoryResults as $result) {
            $statusClass = 'status-' . strtolower($result['status']);
            $statusIcon = $result['status'] === 'PASS' ? '✅' : ($result['status'] === 'FAIL' ? '❌' : '⏭️');
            
            $html .= "<div class='test-item'>";
            $html .= "<div>";
            $html .= "<div class='test-name'>{$statusIcon} " . htmlspecialchars($result['test_name']) . "</div>";
            if (!empty($result['message'])) {
                $html .= "<div class='test-message'>" . htmlspecialchars($result['message']) . "</div>";
            }
            $html .= "</div>";
            $html .= "<div>";
            $html .= "<span class='test-status {$statusClass}'>{$result['status']}</span>";
            if ($result['duration'] > 0) {
                $html .= "<span class='test-duration'>{$result['duration']}ms</span>";
            }
            $html .= "</div>";
            $html .= "</div>";
        }
        
        $html .= "</div>";
    }

    $html .= <<<HTML
        </div>
        
        <div class="footer">
            <p>🚀 RPS Management System Backend Testing Framework</p>
            <p>Built with ❤️ for comprehensive backend validation</p>
        </div>
    </div>
</body>
</html>
HTML;

    return $html;
}
