<?php

echo "Testing MySQL connection...\n";

try {
    // Test MySQL connection
    $mysqli = new mysqli('localhost', 'root', '');
    
    if ($mysqli->connect_error) {
        echo "Connection failed: " . $mysqli->connect_error . "\n";
        exit(1);
    }
    
    echo "Connected successfully!\n";
    
    // Create database
    $result = $mysqli->query('CREATE DATABASE IF NOT EXISTS rpswebid CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci');
    
    if ($result) {
        echo "Database 'rpswebid' created successfully!\n";
    } else {
        echo "Database creation failed: " . $mysqli->error . "\n";
        exit(1);
    }
    
    // Test connection to the new database
    $mysqli->select_db('rpswebid');
    echo "Connected to rpswebid database!\n";
    
    $mysqli->close();
    echo "MySQL setup completed successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
