<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RPS Backend Test Report</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f5f5; color: #333; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; text-align: center; }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .header p { font-size: 1.2em; opacity: 0.9; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
        .summary-card h3 { color: #666; font-size: 0.9em; text-transform: uppercase; margin-bottom: 10px; }
        .summary-card .value { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .summary-card .label { color: #888; font-size: 0.9em; }
        .success-rate { color: #F44336; }
        .test-results { background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden; }
        .test-results h2 { background: #f8f9fa; padding: 20px; margin: 0; border-bottom: 1px solid #dee2e6; }
        .test-item { padding: 15px 20px; border-bottom: 1px solid #eee; display: flex; align-items: center; justify-content: space-between; }
        .test-item:last-child { border-bottom: none; }
        .test-name { font-weight: 500; flex: 1; }
        .test-status { padding: 5px 12px; border-radius: 20px; font-size: 0.8em; font-weight: bold; text-transform: uppercase; }
        .status-pass { background: #d4edda; color: #155724; }
        .status-fail { background: #f8d7da; color: #721c24; }
        .status-skip { background: #fff3cd; color: #856404; }
        .test-duration { color: #666; font-size: 0.9em; margin-left: 10px; }
        .test-message { color: #666; font-size: 0.9em; margin-top: 5px; }
        .progress-bar { background: #e9ecef; height: 20px; border-radius: 10px; overflow: hidden; margin: 20px 0; }
        .progress-fill { background: #F44336; height: 100%; transition: width 0.3s ease; }
        .footer { text-align: center; margin-top: 30px; color: #666; }
        .category-section { margin-bottom: 30px; }
        .category-header { background: #f8f9fa; padding: 15px 20px; border-left: 4px solid #007bff; margin-bottom: 10px; }
        .category-header h3 { color: #007bff; margin: 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 RPS Backend Test Report</h1>
            <p>Generated on 2025-07-27 11:07:04</p>
        </div>
        
        <div class="summary">
            <div class="summary-card">
                <h3>Total Tests</h3>
                <div class="value">52</div>
                <div class="label">Executed</div>
            </div>
            <div class="summary-card">
                <h3>Success Rate</h3>
                <div class="value success-rate">53.85%</div>
                <div class="label">Overall</div>
            </div>
            <div class="summary-card">
                <h3>Passed</h3>
                <div class="value" style="color: #4CAF50;">28</div>
                <div class="label">Tests</div>
            </div>
            <div class="summary-card">
                <h3>Failed</h3>
                <div class="value" style="color: #F44336;">24</div>
                <div class="label">Tests</div>
            </div>
            <div class="summary-card">
                <h3>Duration</h3>
                <div class="value">10930</div>
                <div class="label">Milliseconds</div>
            </div>
        </div>
        
        <div class="progress-bar">
            <div class="progress-fill" style="width: 53.85%;"></div>
        </div>
        
        <div class="test-results">
            <h2>📋 Test Results</h2><div class='category-section'><div class='category-header'><h3>General</h3></div><div class='test-item'><div><div class='test-name'>✅ PHP Version Check</div><div class='test-message'>PHP 8.1.10 meets requirement (&gt;= 8.1.0)</div></div><div><span class='test-status status-pass'>PASS</span></div></div><div class='test-item'><div><div class='test-name'>✅ Required PHP Extensions</div><div class='test-message'>All required extensions loaded (8/8)</div></div><div><span class='test-status status-pass'>PASS</span></div></div><div class='test-item'><div><div class='test-name'>✅ CodeIgniter Framework</div><div class='test-message'>CodeIgniter framework detected (Version: Unknown)</div></div><div><span class='test-status status-pass'>PASS</span><span class='test-duration'>1ms</span></div></div><div class='test-item'><div><div class='test-name'>✅ File Permissions</div><div class='test-message'>All required directories have proper permissions</div></div><div><span class='test-status status-pass'>PASS</span><span class='test-duration'>3ms</span></div></div><div class='test-item'><div><div class='test-name'>✅ Configuration Files</div><div class='test-message'>All configuration files found (4/4)</div></div><div><span class='test-status status-pass'>PASS</span><span class='test-duration'>1ms</span></div></div><div class='test-item'><div><div class='test-name'>✅ Environment Variables</div><div class='test-message'>Environment variables configured (11 variables found)</div></div><div><span class='test-status status-pass'>PASS</span></div></div><div class='test-item'><div><div class='test-name'>❌ Database Connection</div><div class='test-message'>Database connection failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near &#039;database&#039; at line 1</div></div><div><span class='test-status status-fail'>FAIL</span><span class='test-duration'>7ms</span></div></div><div class='test-item'><div><div class='test-name'>✅ Database Service Status</div><div class='test-message'>Database service is running on localhost:3306</div></div><div><span class='test-status status-pass'>PASS</span><span class='test-duration'>1ms</span></div></div><div class='test-item'><div><div class='test-name'>❌ Migration Status</div><div class='test-message'>Failed to check migration status: SQLSTATE[08006] [7] received invalid response to SSL negotiation: J</div></div><div><span class='test-status status-fail'>FAIL</span><span class='test-duration'>4ms</span></div></div><div class='test-item'><div><div class='test-name'>❌ Table Structure Validation</div><div class='test-message'>Failed to check table structure: SQLSTATE[08006] [7] received invalid response to SSL negotiation: J</div></div><div><span class='test-status status-fail'>FAIL</span><span class='test-duration'>1ms</span></div></div><div class='test-item'><div><div class='test-name'>❌ Seeder Data Verification</div><div class='test-message'>Failed to check seeder data: SQLSTATE[08006] [7] received invalid response to SSL negotiation: J</div></div><div><span class='test-status status-fail'>FAIL</span><span class='test-duration'>2ms</span></div></div><div class='test-item'><div><div class='test-name'>❌ Foreign Key Constraints</div><div class='test-message'>Failed to check foreign keys: SQLSTATE[08006] [7] received invalid response to SSL negotiation: J</div></div><div><span class='test-status status-fail'>FAIL</span><span class='test-duration'>2ms</span></div></div><div class='test-item'><div><div class='test-name'>❌ Database Performance</div><div class='test-message'>Failed to test database performance: SQLSTATE[08006] [7] received invalid response to SSL negotiation: J</div></div><div><span class='test-status status-fail'>FAIL</span><span class='test-duration'>19ms</span></div></div><div class='test-item'><div><div class='test-name'>✅ Backend Server Availability</div><div class='test-message'>Backend server is running (HTTP 200)</div></div><div><span class='test-status status-pass'>PASS</span><span class='test-duration'>131ms</span></div></div><div class='test-item'><div><div class='test-name'>❌ Authentication Endpoints</div><div class='test-message'>Some authentication endpoints are not accessible (1/{count(Array)})</div></div><div><span class='test-status status-fail'>FAIL</span><span class='test-duration'>973ms</span></div></div><div class='test-item'><div><div class='test-name'>❌ User Login Process</div><div class='test-message'>User login failed: CodeIgniter\HTTP\Request::fetchGlobal(): Argument #3 ($filter) must be of type ?int, bool given, called in C:\laragon\www\rpswebid\backend\vendor\codeigniter4\framework\system\HTTP\IncomingRequest.php on line 714</div></div><div><span class='test-status status-fail'>FAIL</span><span class='test-duration'>95ms</span></div></div><div class='test-item'><div><div class='test-name'>❌ JWT Token Validation</div><div class='test-message'>No auth token available for validation</div></div><div><span class='test-status status-fail'>FAIL</span></div></div><div class='test-item'><div><div class='test-name'>❌ Protected Route Access</div><div class='test-message'>Protected routes not working correctly</div></div><div><span class='test-status status-fail'>FAIL</span><span class='test-duration'>113ms</span></div></div><div class='test-item'><div><div class='test-name'>❌ Role-Based Access Control</div><div class='test-message'>No auth token available for role testing</div></div><div><span class='test-status status-fail'>FAIL</span></div></div><div class='test-item'><div><div class='test-name'>❌ Token Refresh Mechanism</div><div class='test-message'>No refresh token available for testing</div></div><div><span class='test-status status-fail'>FAIL</span></div></div><div class='test-item'><div><div class='test-name'>❌ Logout Process</div><div class='test-message'>No auth token available for logout testing</div></div><div><span class='test-status status-fail'>FAIL</span></div></div><div class='test-item'><div><div class='test-name'>❌ Authentication Endpoints</div><div class='test-message'>Auth endpoints: 3/4 working</div></div><div><span class='test-status status-fail'>FAIL</span><span class='test-duration'>439ms</span></div></div><div class='test-item'><div><div class='test-name'>✅ User Management Endpoints</div><div class='test-message'>User endpoints: 5/5 accessible</div></div><div><span class='test-status status-pass'>PASS</span><span class='test-duration'>494ms</span></div></div><div class='test-item'><div><div class='test-name'>✅ Faculty Management Endpoints</div><div class='test-message'>Faculty endpoints: 5/5 accessible</div></div><div><span class='test-status status-pass'>PASS</span><span class='test-duration'>458ms</span></div></div><div class='test-item'><div><div class='test-name'>✅ Study Program Endpoints</div><div class='test-message'>Study program endpoints: 5/5 accessible</div></div><div><span class='test-status status-pass'>PASS</span><span class='test-duration'>512ms</span></div></div><div class='test-item'><div><div class='test-name'>✅ Course Management Endpoints</div><div class='test-message'>Course endpoints: 7/7 accessible</div></div><div><span class='test-status status-pass'>PASS</span><span class='test-duration'>686ms</span></div></div><div class='test-item'><div><div class='test-name'>✅ CPMK Management Endpoints</div><div class='test-message'>CPMK endpoints: 7/7 accessible</div></div><div><span class='test-status status-pass'>PASS</span><span class='test-duration'>692ms</span></div></div><div class='test-item'><div><div class='test-name'>✅ CPL Management Endpoints</div><div class='test-message'>CPL endpoints: 5/5 accessible</div></div><div><span class='test-status status-pass'>PASS</span><span class='test-duration'>463ms</span></div></div><div class='test-item'><div><div class='test-name'>✅ Assessment Endpoints</div><div class='test-message'>Assessment endpoints: 2/2 accessible</div></div><div><span class='test-status status-pass'>PASS</span><span class='test-duration'>217ms</span></div></div><div class='test-item'><div><div class='test-name'>✅ Report Endpoints</div><div class='test-message'>Report endpoints: 4/4 accessible</div></div><div><span class='test-status status-pass'>PASS</span><span class='test-duration'>420ms</span></div></div><div class='test-item'><div><div class='test-name'>❌ Error Handling</div><div class='test-message'>Error handling: 1/3 tests passed</div></div><div><span class='test-status status-fail'>FAIL</span><span class='test-duration'>365ms</span></div></div><div class='test-item'><div><div class='test-name'>✅ Model Files Existence</div><div class='test-message'>Model files found: UserModel.php</div></div><div><span class='test-status status-pass'>PASS</span><span class='test-duration'>1ms</span></div></div><div class='test-item'><div><div class='test-name'>❌ Database Connection for Models</div><div class='test-message'>Database connection failed: SQLSTATE[08006] [7] received invalid response to SSL negotiation: J</div></div><div><span class='test-status status-fail'>FAIL</span><span class='test-duration'>2ms</span></div></div><div class='test-item'><div><div class='test-name'>✅ User Model Functionality</div><div class='test-message'>UserModel has all required properties</div></div><div><span class='test-status status-pass'>PASS</span></div></div><div class='test-item'><div><div class='test-name'>✅ Model Validation Rules</div><div class='test-message'>Model validation rules found: 3</div></div><div><span class='test-status status-pass'>PASS</span></div></div><div class='test-item'><div><div class='test-name'>✅ Model Relationships</div><div class='test-message'>UserModel structure is valid (relationships: )</div></div><div><span class='test-status status-pass'>PASS</span></div></div><div class='test-item'><div><div class='test-name'>❌ CRUD Operations</div><div class='test-message'>CRUD operations test failed: SQLSTATE[08006] [7] received invalid response to SSL negotiation: J</div></div><div><span class='test-status status-fail'>FAIL</span><span class='test-duration'>3ms</span></div></div><div class='test-item'><div><div class='test-name'>❌ Data Integrity Checks</div><div class='test-message'>Data integrity test failed: SQLSTATE[08006] [7] received invalid response to SSL negotiation: J</div></div><div><span class='test-status status-fail'>FAIL</span><span class='test-duration'>1ms</span></div></div><div class='test-item'><div><div class='test-name'>✅ Frontend-Backend Communication</div><div class='test-message'>Frontend-backend communication is working</div></div><div><span class='test-status status-pass'>PASS</span><span class='test-duration'>195ms</span></div></div><div class='test-item'><div><div class='test-name'>❌ API Workflow Testing</div><div class='test-message'>API workflow has issues</div></div><div><span class='test-status status-fail'>FAIL</span><span class='test-duration'>90ms</span></div></div><div class='test-item'><div><div class='test-name'>❌ Cross-Module Functionality</div><div class='test-message'>Cross-module functionality test failed: SQLSTATE[08006] [7] received invalid response to SSL negotiation: J</div></div><div><span class='test-status status-fail'>FAIL</span><span class='test-duration'>2ms</span></div></div><div class='test-item'><div><div class='test-name'>❌ Data Flow Validation</div><div class='test-message'>Data flow validation failed</div></div><div><span class='test-status status-fail'>FAIL</span><span class='test-duration'>100ms</span></div></div><div class='test-item'><div><div class='test-name'>❌ Session Management</div><div class='test-message'>Could not obtain session token for testing</div></div><div><span class='test-status status-fail'>FAIL</span><span class='test-duration'>95ms</span></div></div><div class='test-item'><div><div class='test-name'>✅ File Upload Integration</div><div class='test-message'>File upload integration ready</div></div><div><span class='test-status status-pass'>PASS</span><span class='test-duration'>2ms</span></div></div><div class='test-item'><div><div class='test-name'>❌ Error Propagation</div><div class='test-message'>Error propagation has issues</div></div><div><span class='test-status status-fail'>FAIL</span><span class='test-duration'>315ms</span></div></div><div class='test-item'><div><div class='test-name'>✅ Response Time Measurement</div><div class='test-message'>Response times are good (avg: 126.33333333333ms, 100% fast)</div></div><div><span class='test-status status-pass'>PASS</span><span class='test-duration'>379ms</span></div></div><div class='test-item'><div><div class='test-name'>✅ Memory Usage Tracking</div><div class='test-message'>Memory usage is within limits (peak: 2MB)</div></div><div><span class='test-status status-pass'>PASS</span></div></div><div class='test-item'><div><div class='test-name'>❌ Database Query Performance</div><div class='test-message'>Database query performance test failed: SQLSTATE[08006] [7] received invalid response to SSL negotiation: J</div></div><div><span class='test-status status-fail'>FAIL</span><span class='test-duration'>2ms</span></div></div><div class='test-item'><div><div class='test-name'>✅ API Endpoint Performance</div><div class='test-message'>API endpoint performance is acceptable</div></div><div><span class='test-status status-pass'>PASS</span><span class='test-duration'>411ms</span></div></div><div class='test-item'><div><div class='test-name'>✅ Concurrent Request Handling</div><div class='test-message'>Concurrent request handling is acceptable</div></div><div><span class='test-status status-pass'>PASS</span><span class='test-duration'>1195ms</span></div></div><div class='test-item'><div><div class='test-name'>✅ Basic Load Testing</div><div class='test-message'>Load testing passed (success rate: 100%, avg time: 136.9ms)</div></div><div><span class='test-status status-pass'>PASS</span><span class='test-duration'>1369ms</span></div></div><div class='test-item'><div><div class='test-name'>✅ Resource Utilization</div><div class='test-message'>Resource utilization is acceptable</div></div><div><span class='test-status status-pass'>PASS</span><span class='test-duration'>29ms</span></div></div></div>        </div>
        
        <div class="footer">
            <p>🚀 RPS Management System Backend Testing Framework</p>
            <p>Built with ❤️ for comprehensive backend validation</p>
        </div>
    </div>
</body>
</html>