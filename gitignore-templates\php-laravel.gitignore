# =====================================================
# PHP LARAVEL .GITIGNORE TEMPLATE
# =====================================================
# Optimized for Laravel backend development
# =====================================================

# =====================================================
# LARAVEL SPECIFIC
# =====================================================

# Laravel Framework
/vendor/
/node_modules/
/public/hot
/public/storage
/storage/*.key
/storage/app/public/*
!/storage/app/public/.gitkeep

# Laravel Storage Directories
/storage/framework/cache/data/*
!/storage/framework/cache/data/.gitkeep
/storage/framework/sessions/*
!/storage/framework/sessions/.gitkeep
/storage/framework/testing/*
!/storage/framework/testing/.gitkeep
/storage/framework/views/*
!/storage/framework/views/.gitkeep
/storage/logs/*
!/storage/logs/.gitkeep

# Laravel Bootstrap Cache
/bootstrap/cache/*
!/bootstrap/cache/.gitkeep

# Laravel Environment Files
.env
.env.backup
.env.production
.env.local
.env.testing
.env.example.local

# Laravel Configuration
Homestead.json
Homestead.yaml
npm-debug.log
yarn-error.log

# =====================================================
# PHP SPECIFIC
# =====================================================

# Composer
composer.phar
composer.lock
vendor/

# PHP Unit Testing
.phpunit.result.cache
.phpunit.cache
phpunit.xml
coverage/
.coverage

# PHP CS Fixer
.php_cs.cache
.php-cs-fixer.cache

# PHP Storm
.idea/
*.iml

# =====================================================
# UPLOADS & USER CONTENT
# =====================================================

# File Uploads
/public/uploads/*
!/public/uploads/.gitkeep
/storage/app/uploads/*
!/storage/app/uploads/.gitkeep

# User Generated Content
/public/avatars/*
!/public/avatars/.gitkeep
/public/documents/*
!/public/documents/.gitkeep

# =====================================================
# LOGS & CACHE
# =====================================================

# Application Logs
*.log
laravel.log
error.log
access.log

# Cache Files
*.cache
/cache/
/tmp/

# =====================================================
# DATABASE
# =====================================================

# SQLite
*.sqlite
*.sqlite3
*.db

# Database Dumps
*.sql
!database/migrations/*.sql
!database/seeders/*.sql

# =====================================================
# SECURITY & SECRETS
# =====================================================

# API Keys & Secrets
/config/secrets.php
/config/services.local.php
*.pem
*.key
*.crt

# JWT Keys
/storage/oauth-private.key
/storage/oauth-public.key

# =====================================================
# DEVELOPMENT TOOLS
# =====================================================

# Laravel Telescope
/storage/telescope/

# Laravel Horizon
/storage/horizon/

# Laravel Debugbar
/storage/debugbar/

# =====================================================
# ASSETS & BUILD
# =====================================================

# Compiled Assets
/public/css/
/public/js/
/public/mix-manifest.json
mix-manifest.json

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# =====================================================
# DEPLOYMENT
# =====================================================

# Docker
docker-compose.override.yml
.docker/

# CI/CD
.env.ci
.env.staging

# =====================================================
# END OF PHP LARAVEL .GITIGNORE
# =====================================================
