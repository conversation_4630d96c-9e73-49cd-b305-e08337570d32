// Alerts
// *******************************************************************************

/* Alert icon styles */
.alert {
  --#{$prefix}alert-link-hover-color: var(--#{$prefix}primary);
  --#{$prefix}alert-hr: var(--#{$prefix}black);
  --#{$prefix}alert-close-icon: var(--#{$prefix}black);
  line-height: 1.375rem;
  &[class*="alert-"] {
    hr {
      background-color: var(--#{$prefix}alert-hr);
      color: var(--#{$prefix}alert-hr);
    }
  }
  .alert-link {
    &:hover {
      color: var(--#{$prefix}alert-link-hover-color);
    }
  }
}

/* Adjust close link position */
.alert-dismissible {
  padding-inline-end: $alert-dismissible-padding-r;
  padding-inline-start: $alert-padding-x;
  .btn-close {
    padding: 0;
    background: var(--#{$prefix}alert-close-icon);
    block-size: .8125rem;
    filter: none;
    inline-size: .8125rem;
    inset-inline: auto 0;
    margin-block: calc(#{$alert-padding-y} * 1.3);
    margin-inline: calc(#{$alert-padding-x} * .9);
    mask-image: str-replace($btn-close-bg, "#{$btn-close-color}", currentColor);
    mask-repeat: no-repeat;
    mask-size: 100% 100%;
  }
}

// scss-docs-start alert-modifiers

// Generate contextual modifier classes for colorizing the alert
@each $state in map-keys($theme-colors) {
  .alert-#{$state} {
    @if $state == "light" {
      --#{$prefix}alert-color: var(--#{$prefix}#{$state}-contrast);
      --#{$prefix}alert-close-icon: var(--#{$prefix}#{$state}-contrast);
    } @else {
      --#{$prefix}alert-color: var(--#{$prefix}#{$state});
      --#{$prefix}alert-close-icon: var(--#{$prefix}#{$state});
    }
    --#{$prefix}alert-link-color: var(--#{$prefix}#{$state});
    --#{$prefix}alert-border-color: var(--#{$prefix}#{$state}-bg-subtle);
    --#{$prefix}alert-link-hover-color: var(--#{$prefix}#{$state});
    --#{$prefix}alert-hr: var(--#{$prefix}#{$state});
  }
}

// scss-docs-end alert-modifiers
