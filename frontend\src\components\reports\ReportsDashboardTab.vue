<template>
  <div>
    <!-- Quick Stats Cards -->
    <v-row class="mb-6">
      <v-col cols="12" sm="6" md="3">
        <v-card color="primary" variant="tonal">
          <v-card-text>
            <div class="d-flex align-center justify-space-between">
              <div>
                <p class="text-subtitle-2 text-medium-emphasis mb-1">Total Reports</p>
                <h2 class="text-h4 font-weight-bold">{{ dashboardStats.totalReports }}</h2>
              </div>
              <v-icon size="48" color="primary">mdi-file-chart</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
      
      <v-col cols="12" sm="6" md="3">
        <v-card color="success" variant="tonal">
          <v-card-text>
            <div class="d-flex align-center justify-space-between">
              <div>
                <p class="text-subtitle-2 text-medium-emphasis mb-1">This Month</p>
                <h2 class="text-h4 font-weight-bold">{{ dashboardStats.monthlyReports }}</h2>
              </div>
              <v-icon size="48" color="success">mdi-calendar-month</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
      
      <v-col cols="12" sm="6" md="3">
        <v-card color="warning" variant="tonal">
          <v-card-text>
            <div class="d-flex align-center justify-space-between">
              <div>
                <p class="text-subtitle-2 text-medium-emphasis mb-1">Scheduled</p>
                <h2 class="text-h4 font-weight-bold">{{ dashboardStats.scheduledReports }}</h2>
              </div>
              <v-icon size="48" color="warning">mdi-calendar-clock</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
      
      <v-col cols="12" sm="6" md="3">
        <v-card color="info" variant="tonal">
          <v-card-text>
            <div class="d-flex align-center justify-space-between">
              <div>
                <p class="text-subtitle-2 text-medium-emphasis mb-1">Templates</p>
                <h2 class="text-h4 font-weight-bold">{{ dashboardStats.templates }}</h2>
              </div>
              <v-icon size="48" color="info">mdi-file-document-multiple</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Quick Actions -->
    <v-row class="mb-6">
      <v-col cols="12">
        <v-card>
          <v-card-title class="d-flex align-center">
            <v-icon class="mr-2">mdi-lightning-bolt</v-icon>
            Quick Actions
          </v-card-title>
          
          <v-card-text>
            <v-row>
              <v-col cols="12" sm="6" md="3">
                <v-btn
                  block
                  color="primary"
                  size="large"
                  @click="generateCPMKReport"
                >
                  <v-icon start>mdi-target</v-icon>
                  CPMK Achievement
                </v-btn>
              </v-col>
              
              <v-col cols="12" sm="6" md="3">
                <v-btn
                  block
                  color="secondary"
                  size="large"
                  @click="generateCPLReport"
                >
                  <v-icon start>mdi-bullseye-arrow</v-icon>
                  CPL Mapping
                </v-btn>
              </v-col>
              
              <v-col cols="12" sm="6" md="3">
                <v-btn
                  block
                  color="success"
                  size="large"
                  @click="generateAssessmentReport"
                >
                  <v-icon start>mdi-clipboard-check</v-icon>
                  Assessment Analytics
                </v-btn>
              </v-col>
              
              <v-col cols="12" sm="6" md="3">
                <v-btn
                  block
                  color="warning"
                  size="large"
                  @click="generateCourseReport"
                >
                  <v-icon start>mdi-book-open-page-variant</v-icon>
                  Course Performance
                </v-btn>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Recent Reports -->
    <v-row class="mb-6">
      <v-col cols="12" md="8">
        <v-card>
          <v-card-title class="d-flex align-center justify-space-between">
            <div class="d-flex align-center">
              <v-icon class="mr-2">mdi-history</v-icon>
              Recent Reports
            </div>
            <v-btn
              variant="text"
              size="small"
              @click="$emit('switchTab', 'instances')"
            >
              View All
            </v-btn>
          </v-card-title>
          
          <v-card-text>
            <v-list>
              <v-list-item
                v-for="report in recentReports"
                :key="report.id"
                class="px-0"
              >
                <template #prepend>
                  <v-icon :color="getReportStatusColor(report.status)">
                    {{ getReportStatusIcon(report.status) }}
                  </v-icon>
                </template>
                
                <v-list-item-title>{{ report.name }}</v-list-item-title>
                <v-list-item-subtitle>
                  {{ formatDate(report.generated_at) }} • {{ report.format.toUpperCase() }}
                </v-list-item-subtitle>
                
                <template #append>
                  <v-btn
                    icon
                    size="small"
                    variant="text"
                    @click="downloadReport(report.id)"
                    :disabled="report.status !== 'completed'"
                  >
                    <v-icon>mdi-download</v-icon>
                  </v-btn>
                </template>
              </v-list-item>
            </v-list>
          </v-card-text>
        </v-card>
      </v-col>
      
      <v-col cols="12" md="4">
        <v-card>
          <v-card-title class="d-flex align-center">
            <v-icon class="mr-2">mdi-chart-pie</v-icon>
            Report Types Distribution
          </v-card-title>
          
          <v-card-text>
            <div class="text-center py-8">
              <v-icon size="64" color="grey-lighten-2">mdi-chart-pie</v-icon>
              <p class="text-body-2 text-medium-emphasis mt-4">
                Chart showing distribution of report types
              </p>
              <p class="text-caption text-medium-emphasis">
                Chart visualization will be implemented in the next phase
              </p>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Popular Templates -->
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title class="d-flex align-center justify-space-between">
            <div class="d-flex align-center">
              <v-icon class="mr-2">mdi-star</v-icon>
              Popular Report Templates
            </div>
            <v-btn
              variant="text"
              size="small"
              @click="$emit('switchTab', 'templates')"
            >
              View All Templates
            </v-btn>
          </v-card-title>
          
          <v-card-text>
            <v-row>
              <v-col
                v-for="template in popularTemplates"
                :key="template.id"
                cols="12" sm="6" md="4"
              >
                <v-card variant="outlined" class="h-100">
                  <v-card-text>
                    <div class="d-flex align-center mb-2">
                      <v-icon :color="getTemplateTypeColor(template.type)" class="mr-2">
                        {{ getTemplateTypeIcon(template.type) }}
                      </v-icon>
                      <span class="font-weight-medium">{{ template.name }}</span>
                    </div>
                    
                    <p class="text-body-2 text-medium-emphasis mb-3">
                      {{ template.description }}
                    </p>
                    
                    <div class="d-flex align-center justify-space-between">
                      <v-chip
                        :color="getCategoryColor(template.category)"
                        size="small"
                        variant="tonal"
                      >
                        {{ formatCategory(template.category) }}
                      </v-chip>
                      
                      <div class="text-caption text-medium-emphasis">
                        {{ template.usage_count }} uses
                      </div>
                    </div>
                  </v-card-text>
                  
                  <v-card-actions>
                    <v-btn
                      color="primary"
                      variant="text"
                      size="small"
                      @click="generateFromTemplate(template.id)"
                    >
                      Generate
                    </v-btn>
                    
                    <v-btn
                      variant="text"
                      size="small"
                      @click="viewTemplate(template.id)"
                    >
                      View
                    </v-btn>
                  </v-card-actions>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Success/Error Snackbars -->
    <v-snackbar
      v-model="showSuccess"
      color="success"
      timeout="3000"
    >
      {{ successMessage }}
      <template v-slot:actions>
        <v-btn @click="showSuccess = false">Close</v-btn>
      </template>
    </v-snackbar>

    <v-snackbar
      v-model="showError"
      color="error"
      timeout="5000"
    >
      {{ errorMessage }}
      <template v-slot:actions>
        <v-btn @click="showError = false">Close</v-btn>
      </template>
    </v-snackbar>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { reportsAPI } from '@/services/api'
import type { ReportTemplate, ReportInstance } from '@/types/auth'

// Emits
defineEmits<{
  switchTab: [tab: string]
}>()

// State
const loading = ref(false)
const showSuccess = ref(false)
const showError = ref(false)
const successMessage = ref('')
const errorMessage = ref('')

const dashboardStats = reactive({
  totalReports: 0,
  monthlyReports: 0,
  scheduledReports: 0,
  templates: 0
})

const recentReports = ref<ReportInstance[]>([])
const popularTemplates = ref<ReportTemplate[]>([])

// Methods
const loadDashboardData = async () => {
  loading.value = true
  try {
    const [statsResponse, recentResponse, popularResponse] = await Promise.all([
      reportsAPI.getDashboard(),
      reportsAPI.getReportInstances({ limit: 5, sort: 'generated_at', order: 'desc' }),
      reportsAPI.getPopularReports({ limit: 6 })
    ])
    
    Object.assign(dashboardStats, statsResponse.data)
    recentReports.value = recentResponse.data.data || []
    popularTemplates.value = popularResponse.data.data || []
  } catch (error: any) {
    errorMessage.value = 'Failed to load dashboard data'
    showError.value = true
    console.error('Load dashboard error:', error)
  } finally {
    loading.value = false
  }
}

const generateCPMKReport = async () => {
  try {
    const response = await reportsAPI.generateReport(1, { type: 'cpmk_achievement' })
    successMessage.value = 'CPMK Achievement report generation started'
    showSuccess.value = true
  } catch (error: any) {
    errorMessage.value = 'Failed to generate CPMK report'
    showError.value = true
  }
}

const generateCPLReport = async () => {
  try {
    const response = await reportsAPI.generateReport(2, { type: 'cpl_mapping' })
    successMessage.value = 'CPL Mapping report generation started'
    showSuccess.value = true
  } catch (error: any) {
    errorMessage.value = 'Failed to generate CPL report'
    showError.value = true
  }
}

const generateAssessmentReport = async () => {
  try {
    const response = await reportsAPI.generateReport(3, { type: 'assessment_analytics' })
    successMessage.value = 'Assessment Analytics report generation started'
    showSuccess.value = true
  } catch (error: any) {
    errorMessage.value = 'Failed to generate Assessment report'
    showError.value = true
  }
}

const generateCourseReport = async () => {
  try {
    const response = await reportsAPI.generateReport(4, { type: 'course_performance' })
    successMessage.value = 'Course Performance report generation started'
    showSuccess.value = true
  } catch (error: any) {
    errorMessage.value = 'Failed to generate Course report'
    showError.value = true
  }
}

const downloadReport = async (reportId: number) => {
  try {
    const response = await reportsAPI.downloadReport(reportId)
    // Handle blob download
    const url = window.URL.createObjectURL(response.data)
    const link = document.createElement('a')
    link.href = url
    link.download = `report-${reportId}.pdf`
    link.click()
    window.URL.revokeObjectURL(url)
  } catch (error: any) {
    errorMessage.value = 'Failed to download report'
    showError.value = true
  }
}

const generateFromTemplate = async (templateId: number) => {
  try {
    const response = await reportsAPI.generateReport(templateId, {})
    successMessage.value = 'Report generation started from template'
    showSuccess.value = true
  } catch (error: any) {
    errorMessage.value = 'Failed to generate report from template'
    showError.value = true
  }
}

const viewTemplate = (templateId: number) => {
  // Navigate to template view
  console.log('View template:', templateId)
}

// Utility functions
const getReportStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    pending: 'warning',
    processing: 'info',
    completed: 'success',
    failed: 'error'
  }
  return colors[status] || 'grey'
}

const getReportStatusIcon = (status: string) => {
  const icons: Record<string, string> = {
    pending: 'mdi-clock',
    processing: 'mdi-loading',
    completed: 'mdi-check-circle',
    failed: 'mdi-alert-circle'
  }
  return icons[status] || 'mdi-help-circle'
}

const getTemplateTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    cpmk_achievement: 'primary',
    cpl_mapping: 'secondary',
    assessment_analytics: 'success',
    course_performance: 'warning',
    student_progress: 'info',
    custom: 'purple'
  }
  return colors[type] || 'grey'
}

const getTemplateTypeIcon = (type: string) => {
  const icons: Record<string, string> = {
    cpmk_achievement: 'mdi-target',
    cpl_mapping: 'mdi-bullseye-arrow',
    assessment_analytics: 'mdi-clipboard-check',
    course_performance: 'mdi-book-open-page-variant',
    student_progress: 'mdi-account-school',
    custom: 'mdi-file-document'
  }
  return icons[type] || 'mdi-file'
}

const getCategoryColor = (category: string) => {
  const colors: Record<string, string> = {
    academic: 'blue',
    administrative: 'green',
    analytics: 'purple',
    compliance: 'orange'
  }
  return colors[category] || 'grey'
}

const formatCategory = (category: string) => {
  const categories: Record<string, string> = {
    academic: 'Academic',
    administrative: 'Administrative',
    analytics: 'Analytics',
    compliance: 'Compliance'
  }
  return categories[category] || category
}

const formatDate = (dateString?: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString('id-ID', {
    day: 'numeric',
    month: 'short',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Lifecycle
onMounted(() => {
  loadDashboardData()
})
</script>
