@echo off
echo Starting RPS Management System - Development Mode
echo.

echo [1/4] Starting Backend (CodeIgniter 4)...
cd /d "c:\laragon\www\rpswebid\backend"
start "Backend Server" cmd /k "php spark serve --host=0.0.0.0 --port=8080"

echo [2/4] Waiting for backend to start...
timeout /t 3 /nobreak >nul

echo [3/4] Starting Frontend (Vue.js 3)...
cd /d "c:\laragon\www\rpswebid\frontend"
start "Frontend Server" cmd /k "npm run dev"

echo [4/4] Opening browser...
timeout /t 5 /nobreak >nul
start http://localhost:3000

echo.
echo ✅ Development servers started:
echo    Frontend: http://localhost:3000
echo    Backend:  http://localhost:8080
echo    API:      http://localhost:8080/api/v1
echo.
pause