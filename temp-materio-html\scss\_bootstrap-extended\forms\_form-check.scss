/* Checkboxes and Radios
******************************************************************************* */
.form-check {
  position: relative;
  padding-inline: $form-check-padding-start 0;
  .form-check-input {
    float: inline-start;
    margin-inline-start: calc($form-check-padding-start * -1);
  }
  &.form-check-reverse {
    padding-inline: 0 $form-check-padding-start;
    .form-check-input {
      float: inline-end;
      margin-inline-end: calc($form-check-padding-start * -1);
    }
  }
}
.form-check-input {
  --#{$prefix}form-check-input-checked-bg: #{$form-check-input-checked-bg-color};
  --#{$prefix}form-check-input-checked-border-color: #{$form-check-input-checked-border-color};
  --#{$prefix}form-check-box-shadow: var(--#{$prefix}box-shadow-xs);
  cursor: $form-check-label-cursor;
  &:disabled {
    --#{$prefix}form-check-bg: #{$form-check-input-disabled-bg};
    border-color: $form-check-input-disabled-bg;
  }
  &:checked {
    border-color: var(--#{$prefix}form-check-input-checked-border-color);
    background-color: var(--#{$prefix}form-check-input-checked-bg);
    box-shadow: var(--#{$prefix}form-check-box-shadow);
  }
  &[type="checkbox"]:indeterminate {
    border-color: var(--#{$prefix}form-check-input-checked-border-color);
    background-color: var(--#{$prefix}form-check-input-checked-bg);
    box-shadow: var(--#{$prefix}form-check-box-shadow);
  }
  &:active {
    filter: none;
  }
}

/* Only for checkbox and radio (not for bs default switch)
? .dt-checkboxes-cell class is used for DataTables checkboxes */
.form-check:not(.form-switch),
.dt-select {
  // Checkbox and Radio bg size for animation transition
  .form-check-input[type="checkbox"] {
    background-size: 1.1rem;
    &:indeterminate {
      box-shadow: var(--#{$prefix}form-check-box-shadow);
    }
  }
  .form-check-input[type="radio"] {
    background-size: 1.3125rem;
    &:not(:checked) {
      background-size: .75rem;
    }
  }
  // Material shadow display on hover & active
  .form-check-input {
    position: relative;
    @include transition (all .2s);
    &:active,
    &:hover {
      &::after {
        opacity: 1;
        transform: scale(2) translateZ(0);
      }
    }

    &:disabled,
    &[disabled] {
      &::after {
        display: none;
      }
    }
  }
}

.form-check-inline {
  margin-inline: 0 $form-check-inline-margin-end;
}

// Switches
// *******************************************************************************

.form-switch {
  padding-inline-start: $form-switch-padding-start;
  .form-check-input {
    &:not(:checked) {
      background-color: $form-switch-bg;
      box-shadow: $form-switch-box-shadow;
    }
    border: 0;
    margin-inline-start: $form-switch-padding-start * -1;
    &:checked {
      box-shadow: none;
    }
  }
  &.form-check-reverse {
    padding-inline-end: $form-switch-padding-start;
    .form-check-input {
      margin-inline-end: $form-switch-padding-start * -1;
    }
  }
}

// Generate contextual modifier classes for colorizing the form check
@each $state in map-keys($custom-theme-colors) {
  .form-check-#{$state} {
    .form-check-input {
      --#{$prefix}form-check-input-checked-bg: var(--#{$prefix}#{$state});
      --#{$prefix}form-check-input-checked-border-color: var(--#{$prefix}#{$state});
    }
    &.custom-option.checked {
      --#{$prefix}custom-option-border-color: var(--#{$prefix}#{$state});
    }
  }
}
