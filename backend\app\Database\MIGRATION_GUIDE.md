# RPS Management System - Complete Migration Guide

## 📋 Overview

Comprehensive guide untuk database migration system RPS Management System menggunakan CodeIgniter 4. Guide ini mencakup setup, execution, troubleshooting, dan best practices untuk production deployment.

## 🗂️ **File Structure**

```
migrations/
├── 001_create_roles_table.php              # User roles
├── 002_create_users_table.php              # User accounts
├── 003_create_faculties_table.php          # Faculty management
├── 004_create_study_programs_table.php     # Study programs
├── 005_create_cpl_table.php                # Graduate learning outcomes
├── 006_create_courses_table.php            # Course information
├── 007_create_course_references_table.php  # Course bibliography
├── 008_create_course_topics_table.php      # Weekly topics
├── 009_create_cpmk_table.php               # Course learning outcomes
├── 010_create_cpmk_cpl_relations_table.php # CPMK-CPL mappings
├── 011_create_sub_cpmk_table.php           # Sub-course outcomes
├── 012_create_assessment_methods_table.php # Assessment types
├── 013_create_assessment_plans_table.php   # Assessment planning
├── run_migrations.php                       # Migration runner script
├── README.md                                # Basic documentation
└── MIGRATION_GUIDE.md                       # This comprehensive guide

seeders/
├── DatabaseSeeder.php                       # Main seeder
├── RoleSeeder.php                          # Role data
├── UserSeeder.php                          # User data
├── FacultySeeder.php                       # Faculty data
└── StudyProgramSeeder.php                  # Study program data
```

## 🚀 **Quick Start**

### **1. Environment Setup**
```bash
# Copy environment file
cp .env.example .env

# Configure database in .env
database.default.hostname = localhost
database.default.database = rps_management
database.default.username = your_username
database.default.password = your_password
database.default.DBDriver = MySQLi
database.default.port = 3306
```

### **2. Create Database**
```sql
-- Create database
CREATE DATABASE rps_management CHARACTER SET utf8 COLLATE utf8_unicode_ci;

-- Grant permissions
GRANT ALL PRIVILEGES ON rps_management.* TO 'your_username'@'localhost';
FLUSH PRIVILEGES;
```

### **3. Run Migrations**
```bash
# Method 1: Using CodeIgniter CLI
php spark migrate

# Method 2: Using migration runner
php migrations/run_migrations.php migrate

# Method 3: With seeders
php spark migrate
php spark db:seed DatabaseSeeder
```

### **4. Verify Installation**
```bash
# Check migration status
php migrations/run_migrations.php status

# Check tables
php spark db:table users
```

## 🔧 **Detailed Migration Process**

### **Migration Order & Dependencies**

| Order | Table | Dependencies | Description |
|-------|-------|--------------|-------------|
| 001 | `roles` | None | Base user roles |
| 002 | `users` | roles | User accounts |
| 003 | `faculties` | users | Faculty structure |
| 004 | `study_programs` | faculties, users | Academic programs |
| 005 | `cpl` | study_programs, users | Learning outcomes |
| 006 | `courses` | study_programs, users | Course catalog |
| 007 | `course_references` | courses, users | Bibliography |
| 008 | `course_topics` | courses, users | Weekly content |
| 009 | `cpmk` | courses, users | Course outcomes |
| 010 | `cpmk_cpl_relations` | cpmk, cpl, users | Outcome mappings |
| 011 | `sub_cpmk` | cpmk, users | Sub-outcomes |
| 012 | `assessment_methods` | users | Assessment types |
| 013 | `assessment_plans` | cpmk, sub_cpmk, assessment_methods, users | Assessment planning |

### **Foreign Key Relationships**
```sql
-- Key relationships
users.role_id → roles.id
faculties.dean_id → users.id
study_programs.faculty_id → faculties.id
study_programs.head_id → users.id
cpl.study_program_id → study_programs.id
courses.study_program_id → study_programs.id
courses.coordinator_id → users.id
cpmk.course_id → courses.id
cpmk_cpl_relations.cpmk_id → cpmk.id
cpmk_cpl_relations.cpl_id → cpl.id
```

## 📊 **Database Schema Details**

### **Core Tables**

#### **1. Roles Table**
```sql
CREATE TABLE `roles` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(50) NOT NULL UNIQUE,
    `description` TEXT NULL,
    `permissions` TEXT NULL COMMENT 'JSON formatted permissions',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### **2. Users Table**
```sql
CREATE TABLE `users` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `username` VARCHAR(100) NOT NULL UNIQUE,
    `email` VARCHAR(255) NOT NULL UNIQUE,
    `password_hash` VARCHAR(255) NOT NULL,
    `full_name` VARCHAR(255) NOT NULL,
    `nip` VARCHAR(50) NULL,
    `role_id` INT UNSIGNED NULL,
    `is_active` TINYINT(1) DEFAULT 1,
    `last_login` TIMESTAMP NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (`role_id`) REFERENCES `roles`(`id`) ON DELETE SET NULL
);
```

#### **3. Academic Structure**
```sql
-- Faculties → Study Programs → Courses → CPMK → Assessment
faculties (id, code, name, dean_id)
    ↓
study_programs (id, code, name, faculty_id, head_id)
    ↓
courses (id, code, name, study_program_id, coordinator_id)
    ↓
cpmk (id, code, course_id, description)
    ↓
assessment_plans (id, cpmk_id, assessment_method_id)
```

## 🛠️ **Migration Commands**

### **Basic Commands**
```bash
# Run all pending migrations
php spark migrate

# Check migration status
php spark migrate:status

# Rollback last migration
php spark migrate:rollback

# Rollback to specific version
php spark migrate:rollback -b 5

# Rollback all migrations
php spark migrate:rollback -b 0

# Refresh all migrations
php spark migrate:refresh
```

### **Advanced Commands**
```bash
# Run specific migration
php spark migrate -n "App\Database\Migrations\CreateRolesTable"

# Run migrations with seeders
php spark migrate:refresh --seed

# Create new migration
php spark make:migration CreateNewTable

# Create new seeder
php spark make:seeder NewSeeder
```

### **Migration Runner Script**
```bash
# Using custom migration runner
php migrations/run_migrations.php migrate
php migrations/run_migrations.php rollback
php migrations/run_migrations.php refresh
php migrations/run_migrations.php status
php migrations/run_migrations.php reset
php migrations/run_migrations.php help
```

## 🔍 **Data Seeding**

### **Seeder Execution Order**
```bash
# Run all seeders
php spark db:seed DatabaseSeeder

# Run specific seeder
php spark db:seed RoleSeeder
php spark db:seed UserSeeder
php spark db:seed FacultySeeder
php spark db:seed StudyProgramSeeder
```

### **Default Data Created**

#### **Roles (6 roles)**
- Super Admin (full access)
- Admin Fakultas (faculty level)
- Koordinator Prodi (study program level)
- Dosen (course level)
- Mahasiswa (student access)
- Staff Akademik (administrative access)

#### **Users (8 users)**
- superadmin (Super Admin)
- admin.teknik (Admin Fakultas)
- kaprodi.ti (Koordinator Prodi)
- dosen.pemrograman (Dosen)
- dosen.database (Dosen)
- dosen.jaringan (Dosen)
- staff.akademik (Staff Akademik)
- mahasiswa.demo (Mahasiswa)

#### **Faculties (4 faculties)**
- FT (Fakultas Teknik)
- FE (Fakultas Ekonomi)
- FKIP (Fakultas Keguruan dan Ilmu Pendidikan)
- FMIPA (Fakultas MIPA)

#### **Study Programs (10 programs)**
- TI, SI, TE, TM (Fakultas Teknik)
- MJ, AK (Fakultas Ekonomi)
- PGSD, PMAT (FKIP)
- MAT, FIS (FMIPA)

#### **Assessment Methods (8 methods)**
- Tugas Individu, Tugas Kelompok
- Kuis Mingguan
- UTS, UAS
- Praktikum, Proyek Akhir, Presentasi

## 🔒 **Security & Best Practices**

### **1. Environment Security**
```bash
# Never commit .env files
echo ".env" >> .gitignore
echo ".env.*" >> .gitignore
echo "!.env.example" >> .gitignore

# Use strong database passwords
database.default.password = "complex_password_here"

# Restrict database user permissions
GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, INDEX, ALTER ON rps_management.* TO 'rps_user'@'localhost';
```

### **2. Migration Security**
```php
// Always use proper data types
'password_hash' => [
    'type'       => 'VARCHAR',
    'constraint' => 255,  // Sufficient for bcrypt
    'null'       => false,
],

// Use foreign key constraints
$this->forge->addForeignKey('role_id', 'roles', 'id', 'SET NULL', 'CASCADE');

// Add proper indexes
$this->forge->addKey('email');
$this->forge->addUniqueKey('username');
```

### **3. Data Validation**
```php
// Use ENUM for fixed values
'degree_level' => [
    'type'       => 'ENUM',
    'constraint' => ['D3', 'S1', 'S2', 'S3'],
    'null'       => false,
],

// Use DECIMAL for precise numbers
'weight_percentage' => [
    'type'       => 'DECIMAL',
    'constraint' => '5,2',
    'null'       => false,
],
```

## 🚨 **Troubleshooting**

### **Common Issues**

#### **1. Foreign Key Constraint Errors**
```bash
# Error: Cannot add foreign key constraint
# Solution: Check if referenced table exists
SHOW TABLES;
DESCRIBE roles;

# Temporarily disable foreign key checks (development only)
SET FOREIGN_KEY_CHECKS = 0;
# Run migration
SET FOREIGN_KEY_CHECKS = 1;
```

#### **2. Migration Already Exists**
```bash
# Error: Migration already exists
# Solution: Check migration status
php spark migrate:status

# Rollback and re-run
php spark migrate:rollback
php spark migrate
```

#### **3. Database Connection Issues**
```bash
# Error: Unable to connect to database
# Solution: Check .env configuration
php spark db:table users  # Test connection

# Check MySQL service
sudo systemctl status mysql
sudo systemctl start mysql
```

#### **4. Permission Denied**
```bash
# Error: Access denied for user
# Solution: Grant proper permissions
GRANT ALL PRIVILEGES ON rps_management.* TO 'username'@'localhost';
FLUSH PRIVILEGES;
```

### **Debug Commands**
```bash
# Enable debug mode
CI_ENVIRONMENT = development

# Check database configuration
php spark env

# Test database connection
php -r "
$db = new PDO('mysql:host=localhost;dbname=rps_management', 'username', 'password');
echo 'Connection successful!';
"
```

## 📈 **Performance Optimization**

### **1. Index Optimization**
```sql
-- Add composite indexes for common queries
CREATE INDEX idx_courses_study_program_semester ON courses(study_program_id, semester);
CREATE INDEX idx_users_role_active ON users(role_id, is_active);
CREATE INDEX idx_cpmk_course_active ON cpmk(course_id, is_active);
```

### **2. Query Optimization**
```sql
-- Use EXPLAIN to analyze queries
EXPLAIN SELECT * FROM courses 
JOIN study_programs ON courses.study_program_id = study_programs.id 
WHERE study_programs.faculty_id = 1;

-- Optimize with proper indexes
CREATE INDEX idx_study_programs_faculty ON study_programs(faculty_id);
```

### **3. Database Configuration**
```sql
-- MySQL optimization for RPS system
SET GLOBAL innodb_buffer_pool_size = 1073741824;  -- 1GB
SET GLOBAL innodb_log_file_size = 268435456;      -- 256MB
SET GLOBAL query_cache_size = 67108864;           -- 64MB
```

## 🎯 **Production Deployment**

### **Pre-deployment Checklist**
- [ ] Test all migrations on staging environment
- [ ] Backup production database
- [ ] Verify environment configuration
- [ ] Check disk space and permissions
- [ ] Plan maintenance window

### **Deployment Steps**
```bash
# 1. Backup production database
mysqldump -u root -p rps_management > backup_$(date +%Y%m%d_%H%M%S).sql

# 2. Put application in maintenance mode
touch writable/maintenance.flag

# 3. Run migrations
php spark migrate

# 4. Run seeders (if needed)
php spark db:seed DatabaseSeeder

# 5. Verify deployment
php migrations/run_migrations.php status

# 6. Remove maintenance mode
rm writable/maintenance.flag
```

### **Post-deployment Verification**
```bash
# Check application functionality
curl -I http://your-domain.com/api/health

# Verify database integrity
php spark db:table users
php spark db:table courses

# Check logs for errors
tail -f writable/logs/log-$(date +%Y-%m-%d).log
```

---

**Note:** Migration system ini telah dioptimasi untuk production deployment dengan proper error handling, rollback capabilities, dan comprehensive logging untuk RPS Management System.
