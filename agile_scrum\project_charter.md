# RPS Management System - Project Charter

## 📋 Project Information

**Project Name:** RPS (Rencana Pembelajaran Semester) Management System  
**Project Code:** RPS-MS-2025  
**Version:** 1.0.0  
**Project Manager/Product Owner:** <PERSON><PERSON><PERSON><PERSON>, S.Kom, MT  
**Start Date:** January 25, 2025  
**Target Go-Live:** April 18, 2025 (12 weeks)  
**Project Type:** New Development  

## 🎯 Project Vision & Mission

### Vision
Menciptakan sistem manajemen RPS yang komprehensif, user-friendly, dan terintegrasi untuk meningkatkan kualitas perencanaan pembelajaran di perguruan tinggi.

### Mission
- Mengotomatisasi proses perencanaan pembelajaran semester
- Menyediakan platform terintegrasi untuk manajemen CPMK dan CPL
- Memfasilitasi tracking dan evaluasi pencapaian pembelajaran
- Meningkatkan efisiensi dan akurasi dalam pelaporan akademik

## 🏢 Stakeholder Analysis

### Primary Stakeholders
1. **Admin <PERSON>**
   - Role: System Administrator
   - Responsibility: Mengelola sistem secara k<PERSON>, user management, backup data
   - Authority: Full system access

2. **Dekan**
   - Role: Faculty Dean
   - Responsibility: Oversight fakultas, approval kebijakan akademik
   - Authority: View all faculty data, approve major changes

3. **Wakil Dekan**
   - Role: Vice Dean
   - Responsibility: Assist dekan, koordinasi program studi
   - Authority: View faculty data, limited approval rights

4. **Kepala Program Studi**
   - Role: Head of Study Program
   - Responsibility: Mengelola kurikulum prodi, koordinasi dosen
   - Authority: Full access to study program data, course management

5. **Sekretaris Program Studi**
   - Role: Study Program Secretary
   - Responsibility: Administrative support, data entry, reporting
   - Authority: Data entry and reporting within study program

6. **Dosen Pengampu**
   - Role: Course Lecturer
   - Responsibility: Mengelola RPS mata kuliah, assessment planning
   - Authority: Manage assigned courses, CPMK, assessments

### Secondary Stakeholders
- Mahasiswa (view-only access untuk transparansi)
- Staff IT (technical support)
- Quality Assurance (audit dan compliance)

## 🎯 Project Objectives

### Primary Objectives
1. **Digitalisasi RPS**: Mengubah proses manual menjadi digital
2. **Standardisasi**: Memastikan konsistensi format dan kualitas RPS
3. **Integrasi**: Menghubungkan CPMK, CPL, dan assessment dalam satu sistem
4. **Transparency**: Memberikan visibilitas proses pembelajaran kepada stakeholder
5. **Compliance**: Memenuhi standar akreditasi dan regulasi pendidikan

### Success Criteria
- 100% RPS mata kuliah aktif ter-digitalisasi
- 95% user adoption rate dalam 3 bulan
- Pengurangan waktu pembuatan RPS sebesar 60%
- Zero data loss dan 99.9% uptime
- Compliance dengan standar SNPT dan akreditasi

## 📊 Project Scope

### In Scope
- User management dengan RBAC granular
- Master data management (fakultas, prodi, mata kuliah)
- RPS creation dan management
- CPMK dan CPL management
- Assessment planning dan tracking
- Reporting dan analytics
- Integration dengan sistem akademik existing (future phase)

### Out of Scope
- Student Information System (SIS)
- Learning Management System (LMS)
- Financial management
- HR management
- Mobile application (future phase)

## 🏗️ High-Level Architecture

### Technology Stack
- **Backend**: CodeIgniter 4 (PHP 8.1+)
- **Frontend**: Vue.js 3 + Vuetify 3
- **Database**: PostgreSQL 14+
- **Cache**: Redis
- **Web Server**: Nginx + Apache
- **Containerization**: Docker

### Integration Points
- Single Sign-On (SSO) capability
- API-first design for future integrations
- Export/Import functionality
- Backup and disaster recovery

## 📅 Project Timeline

### Sprint Overview (2-week sprints)
- **Sprint 0**: Project Setup & Planning (Week 1-2)
- **Sprint 1**: Authentication & User Management (Week 3-4)
- **Sprint 2**: Master Data & Course Management (Week 5-6)
- **Sprint 3**: CPMK & CPL Management (Week 7-8)
- **Sprint 4**: Assessment System (Week 9-10)
- **Sprint 5**: Reporting & Analytics (Week 11-12)
- **Sprint 6**: Testing & Deployment (Week 13-14) - Buffer

### Key Milestones
- **Week 2**: Development environment ready
- **Week 4**: User authentication complete
- **Week 6**: Course management functional
- **Week 8**: CPMK/CPL system operational
- **Week 10**: Assessment system complete
- **Week 12**: System ready for UAT
- **Week 14**: Production deployment

## 💰 Budget & Resources

### Human Resources
- **Product Owner**: Syahroni Wahyu Iriananda, S.Kom, MT (100%)
- **Backend Developer**: 1 FTE (12 weeks)
- **Frontend Developer**: 1 FTE (12 weeks)
- **Database Administrator**: 0.5 FTE (12 weeks)
- **QA Tester**: 0.5 FTE (8 weeks)
- **UI/UX Designer**: 0.3 FTE (6 weeks)

### Infrastructure
- Development servers
- Testing environment
- Production infrastructure
- Backup and monitoring tools

### Software Licenses
- Development tools and IDEs
- Testing tools
- Monitoring and analytics tools

## ⚠️ Risk Assessment

### High Risk
1. **Data Migration**: Kompleksitas migrasi data existing
   - Mitigation: Thorough data analysis dan testing
2. **User Adoption**: Resistance to change
   - Mitigation: Training program dan change management
3. **Integration Complexity**: Integrasi dengan sistem existing
   - Mitigation: API-first design dan phased integration

### Medium Risk
1. **Performance**: System performance dengan data besar
   - Mitigation: Performance testing dan optimization
2. **Security**: Data security dan privacy
   - Mitigation: Security audit dan best practices

### Low Risk
1. **Technology**: Maturity teknologi yang dipilih
2. **Team Capability**: Keahlian tim development

## 📋 Quality Standards

### Code Quality
- Code coverage minimum 80%
- Code review mandatory untuk semua changes
- Automated testing pipeline
- Documentation standards

### Performance Standards
- Page load time < 3 seconds
- API response time < 200ms
- 99.9% uptime target
- Support 1000+ concurrent users

### Security Standards
- OWASP compliance
- Data encryption at rest dan in transit
- Regular security audits
- Access logging dan monitoring

## 📞 Communication Plan

### Regular Meetings
- **Daily Standups**: 15 minutes, development team
- **Sprint Planning**: 2 hours, every 2 weeks
- **Sprint Review**: 1 hour, every 2 weeks
- **Sprint Retrospective**: 1 hour, every 2 weeks
- **Stakeholder Updates**: Weekly progress reports

### Communication Channels
- **Project Management**: Jira/Azure DevOps
- **Team Communication**: Slack/Microsoft Teams
- **Documentation**: Confluence/SharePoint
- **Code Repository**: Git (GitHub/GitLab)

## ✅ Definition of Done

### Feature Level
- [ ] Code implemented dan tested
- [ ] Unit tests written dan passing
- [ ] Integration tests passing
- [ ] Code reviewed dan approved
- [ ] Documentation updated
- [ ] Security review completed
- [ ] Performance tested
- [ ] User acceptance criteria met

### Sprint Level
- [ ] All planned features completed
- [ ] Sprint goals achieved
- [ ] Demo prepared dan delivered
- [ ] Retrospective completed
- [ ] Next sprint planned

### Release Level
- [ ] All features tested end-to-end
- [ ] Performance benchmarks met
- [ ] Security audit passed
- [ ] User training completed
- [ ] Production deployment successful
- [ ] Monitoring dan alerting active

## 📈 Success Metrics

### Technical Metrics
- Code quality scores
- Test coverage percentage
- Performance benchmarks
- Security vulnerability count
- System uptime percentage

### Business Metrics
- User adoption rate
- Task completion time reduction
- Error rate reduction
- User satisfaction scores
- ROI calculation

## 🔄 Change Management

### Change Request Process
1. Change identification
2. Impact assessment
3. Stakeholder approval
4. Implementation planning
5. Execution dan monitoring

### Approval Authority
- Minor changes: Product Owner
- Major changes: Steering Committee
- Budget changes: Project Sponsor

---

**Document Control:**
- **Created by**: Syahroni Wahyu Iriananda, S.Kom, MT
- **Created date**: January 25, 2025
- **Version**: 1.0
- **Next review**: February 8, 2025
