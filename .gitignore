# =====================================================
# BACKEND .GITIGNORE FOR RPS MANAGEMENT SYSTEM
# =====================================================
# Created by: Syahroni Wahyu Iriananda, S.Kom, MT
# Date: 2025-01-25
# Description: Comprehensive .gitignore for backend development
# Supports: PHP, Node.js, Python, Java, .NET, Go, and more
# =====================================================

# =====================================================
# GENERAL FILES & DIRECTORIES
# =====================================================

# Operating System Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Editor and IDE Files
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.settings/
*.sublime-project
*.sublime-workspace
.atom/
.brackets.json

# Temporary Files
*.tmp
*.temp
*.log
*.cache
*.pid
*.seed
*.pid.lock

# =====================================================
# PHP & LARAVEL
# =====================================================

# Laravel Specific
/vendor/
/node_modules/
/public/hot
/public/storage
/storage/*.key
/storage/app/public/*
!/storage/app/public/.gitkeep
/storage/framework/cache/data/*
!/storage/framework/cache/data/.gitkeep
/storage/framework/sessions/*
!/storage/framework/sessions/.gitkeep
/storage/framework/testing/*
!/storage/framework/testing/.gitkeep
/storage/framework/views/*
!/storage/framework/views/.gitkeep
/storage/logs/*
!/storage/logs/.gitkeep
/bootstrap/cache/*
!/bootstrap/cache/.gitkeep


# Composer
composer.phar
composer.lock
vendor/

# PHP
*.php~
.phpunit.cache

# =====================================================
# NODE.JS & JAVASCRIPT
# =====================================================

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Optional npm cache directory
.npm


# dotenv environment variables file
.env.local
.env.development.local
.env.test.local
.env.production.local


# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/


# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/



# Visual Studio
.vs/
*.user
*.userosscache
*.sln.docstates
*.userprefs


# =====================================================
# DATABASE FILES
# =====================================================

# SQLite
*.sqlite
*.sqlite3
*.db
*.db3

# MySQL
*.sql
!schema.sql
!migrations/*.sql
!seeds/*.sql

# PostgreSQL
*.backup
*.dump

# MongoDB
*.bson

# Redis
dump.rdb

# =====================================================
# CONFIGURATION & SECRETS
# =====================================================

# Environment Variables
.env*
!.env.example
!.env.template

# Configuration Files
config/local.php
config/local.json
config/production.php
config/development.php
config/database.php
config/secrets.php

# SSL Certificates
*.pem
*.key
*.crt
*.csr
*.p12
*.pfx

# API Keys & Secrets
secrets/
.secrets
api-keys.txt
credentials.json
service-account.json

# =====================================================
# LOGS & MONITORING
# =====================================================

# Log Files
logs/
*.log
*.log.*
log/
storage/logs/
var/log/

# Monitoring
newrelic.ini
.newrelic.yml

# =====================================================
# DEPLOYMENT & DOCKER
# =====================================================

# Docker
.dockerignore
docker-compose.override.yml
.docker/

# Kubernetes
*.kubeconfig

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Ansible
*.retry

# =====================================================
# BACKUP & TEMPORARY FILES
# =====================================================

# Backup Files
*.bak
*.backup
*.old
*.orig
*.save
*~

# Archive Files
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# =====================================================
# PROJECT SPECIFIC
# =====================================================

# RPS Management System Specific
/storage/app/uploads/*
!/storage/app/uploads/.gitkeep
/public/uploads/*
!/public/uploads/.gitkeep
/public/documents/*
!/public/documents/.gitkeep

# Reports & Exports
/exports/
/reports/
*.pdf
*.xlsx
*.docx
!templates/*.pdf
!templates/*.xlsx
!templates/*.docx

# Cache
/cache/
/tmp/
*.cache

# =====================================================
# END OF .GITIGNORE
# =====================================================
