<template>
  <div>
    <!-- Statistics Cards -->
    <v-row class="mb-6">
      <v-col cols="12" sm="6" md="3">
        <v-card color="primary" variant="tonal">
          <v-card-text>
            <div class="d-flex align-center justify-space-between">
              <div>
                <p class="text-subtitle-2 text-medium-emphasis mb-1">Total Methods</p>
                <h2 class="text-h4 font-weight-bold">{{ totalMethods }}</h2>
              </div>
              <v-icon size="48" color="primary">mdi-clipboard-list</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
      
      <v-col cols="12" sm="6" md="3">
        <v-card color="success" variant="tonal">
          <v-card-text>
            <div class="d-flex align-center justify-space-between">
              <div>
                <p class="text-subtitle-2 text-medium-emphasis mb-1">Formative</p>
                <h2 class="text-h4 font-weight-bold">{{ formativeMethods }}</h2>
              </div>
              <v-icon size="48" color="success">mdi-clipboard-check</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
      
      <v-col cols="12" sm="6" md="3">
        <v-card color="warning" variant="tonal">
          <v-card-text>
            <div class="d-flex align-center justify-space-between">
              <div>
                <p class="text-subtitle-2 text-medium-emphasis mb-1">Summative</p>
                <h2 class="text-h4 font-weight-bold">{{ summativeMethods }}</h2>
              </div>
              <v-icon size="48" color="warning">mdi-clipboard-text</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
      
      <v-col cols="12" sm="6" md="3">
        <v-card color="info" variant="tonal">
          <v-card-text>
            <div class="d-flex align-center justify-space-between">
              <div>
                <p class="text-subtitle-2 text-medium-emphasis mb-1">Active</p>
                <h2 class="text-h4 font-weight-bold">{{ activeMethods }}</h2>
              </div>
              <v-icon size="48" color="info">mdi-check-circle</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Data Table -->
    <DataTable
      title="Assessment Methods"
      icon="mdi-clipboard-list"
      item-name="Assessment Method"
      :headers="tableHeaders"
      :items="assessmentMethods"
      :loading="loading"
      :total-items="totalItems"
      :filters="tableFilters"
      @add="openCreateModal"
      @edit="openEditModal"
      @delete="openDeleteDialog"
      @view="openViewModal"
      @refresh="loadAssessmentMethods"
      @search="handleSearch"
      @filter="handleFilter"
      @update:options="handleTableOptions"
    >
      <!-- Custom slots for specific columns -->
      <template #item.name="{ item }">
        <div class="d-flex align-center">
          <v-icon
            :color="getTypeColor(item.type)"
            class="mr-2"
            size="small"
          >
            {{ getTypeIcon(item.type) }}
          </v-icon>
          <span class="font-weight-medium">{{ item.name }}</span>
        </div>
      </template>

      <template #item.type="{ item }">
        <v-chip
          :color="getTypeColor(item.type)"
          variant="tonal"
          size="small"
        >
          {{ formatType(item.type) }}
        </v-chip>
      </template>

      <template #item.category="{ item }">
        <v-chip
          :color="getCategoryColor(item.category)"
          variant="tonal"
          size="small"
        >
          <v-icon start size="small">{{ getCategoryIcon(item.category) }}</v-icon>
          {{ formatCategory(item.category) }}
        </v-chip>
      </template>

      <template #item.description="{ item }">
        <div class="text-truncate" style="max-width: 300px;">
          {{ item.description || '-' }}
        </div>
      </template>

      <template #item.actions="{ item }">
        <div class="d-flex align-center gap-1">
          <v-btn
            icon
            size="small"
            variant="text"
            @click="openViewModal(item)"
          >
            <v-icon size="small">mdi-eye</v-icon>
            <v-tooltip activator="parent">View Details</v-tooltip>
          </v-btn>
          
          <v-btn
            icon
            size="small"
            variant="text"
            @click="openEditModal(item)"
          >
            <v-icon size="small">mdi-pencil</v-icon>
            <v-tooltip activator="parent">Edit Method</v-tooltip>
          </v-btn>
          
          <v-btn
            icon
            size="small"
            variant="text"
            @click="openUsageAnalytics(item)"
          >
            <v-icon size="small">mdi-chart-bar</v-icon>
            <v-tooltip activator="parent">Usage Analytics</v-tooltip>
          </v-btn>
          
          <v-btn
            icon
            size="small"
            variant="text"
            color="error"
            @click="openDeleteDialog(item)"
          >
            <v-icon size="small">mdi-delete</v-icon>
            <v-tooltip activator="parent">Delete Method</v-tooltip>
          </v-btn>
        </div>
      </template>
    </DataTable>

    <!-- Assessment Method Form Modal -->
    <FormModal
      v-model="showModal"
      :title="modalTitle"
      :icon="modalIcon"
      :mode="modalMode"
      :loading="modalLoading"
      max-width="700"
      @submit="handleSubmit"
      @close="closeModal"
    >
      <v-row>
        <v-col cols="12">
          <v-text-field
            v-model="formData.name"
            :rules="nameRules"
            label="Method Name *"
            variant="outlined"
            prepend-inner-icon="mdi-clipboard-list"
            :disabled="modalLoading || modalMode === 'view'"
            placeholder="e.g., Quiz Mingguan"
          />
        </v-col>
        
        <v-col cols="12" md="6">
          <v-select
            v-model="formData.type"
            :items="typeOptions"
            :rules="typeRules"
            label="Assessment Type *"
            variant="outlined"
            prepend-inner-icon="mdi-format-list-bulleted-type"
            :disabled="modalLoading || modalMode === 'view'"
          />
        </v-col>
        
        <v-col cols="12" md="6">
          <v-select
            v-model="formData.category"
            :items="categoryOptions"
            :rules="categoryRules"
            label="Assessment Category *"
            variant="outlined"
            prepend-inner-icon="mdi-tag"
            :disabled="modalLoading || modalMode === 'view'"
          />
        </v-col>
        
        <v-col cols="12">
          <v-textarea
            v-model="formData.description"
            label="Description"
            variant="outlined"
            prepend-inner-icon="mdi-text"
            rows="3"
            :disabled="modalLoading || modalMode === 'view'"
            hint="Optional description of the assessment method"
          />
        </v-col>
        
        <v-col cols="12" md="6">
          <v-switch
            v-model="formData.is_active"
            label="Active"
            color="primary"
            :disabled="modalLoading || modalMode === 'view'"
          />
        </v-col>
      </v-row>
    </FormModal>

    <!-- Usage Analytics Modal -->
    <v-dialog v-model="showAnalyticsModal" max-width="800" scrollable>
      <v-card>
        <v-card-title class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <v-icon class="mr-2">mdi-chart-bar</v-icon>
            <span class="text-h6 font-weight-bold">Usage Analytics - {{ selectedMethod?.name }}</span>
          </div>
          <v-btn icon variant="text" @click="showAnalyticsModal = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>
        
        <v-divider />
        
        <v-card-text>
          <AssessmentMethodAnalytics
            v-if="selectedMethod"
            :method="selectedMethod"
            @close="showAnalyticsModal = false"
          />
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- Delete Confirmation Dialog -->
    <v-dialog v-model="showDeleteDialog" max-width="400">
      <v-card>
        <v-card-title class="text-h6">Confirm Delete</v-card-title>
        <v-card-text>
          Are you sure you want to delete assessment method "{{ selectedMethod?.name }}"?
          This action cannot be undone and will affect all related assessment plans.
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn @click="showDeleteDialog = false">Cancel</v-btn>
          <v-btn
            color="error"
            :loading="deleteLoading"
            @click="confirmDelete"
          >
            Delete
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Success/Error Snackbars -->
    <v-snackbar
      v-model="showSuccess"
      color="success"
      timeout="3000"
    >
      {{ successMessage }}
      <template v-slot:actions>
        <v-btn @click="showSuccess = false">Close</v-btn>
      </template>
    </v-snackbar>

    <v-snackbar
      v-model="showError"
      color="error"
      timeout="5000"
    >
      {{ errorMessage }}
      <template v-slot:actions>
        <v-btn @click="showError = false">Close</v-btn>
      </template>
    </v-snackbar>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import DataTable from '@/components/common/DataTable.vue'
import FormModal from '@/components/common/FormModal.vue'
import AssessmentMethodAnalytics from '@/components/assessments/AssessmentMethodAnalytics.vue'
import { assessmentMethodsAPI } from '@/services/api'
import type { AssessmentMethod, PaginationParams } from '@/types/auth'

// State
const loading = ref(false)
const modalLoading = ref(false)
const deleteLoading = ref(false)
const showModal = ref(false)
const showAnalyticsModal = ref(false)
const showDeleteDialog = ref(false)
const showSuccess = ref(false)
const showError = ref(false)
const successMessage = ref('')
const errorMessage = ref('')

const assessmentMethods = ref<AssessmentMethod[]>([])
const totalItems = ref(0)
const selectedMethod = ref<AssessmentMethod | null>(null)
const modalMode = ref<'create' | 'edit' | 'view'>('create')

// Form data
const formData = reactive({
  name: '',
  description: '',
  type: 'formatif' as 'formatif' | 'sumatif',
  category: 'tugas' as 'tugas' | 'kuis' | 'uts' | 'uas' | 'praktikum' | 'proyek' | 'presentasi' | 'lainnya',
  is_active: true
})

// Pagination and filtering
const searchQuery = ref('')
const filters = ref<Record<string, any>>({})
const pagination = ref<PaginationParams>({
  page: 1,
  per_page: 20,
  sort_by: 'created_at',
  sort_order: 'desc'
})

// Statistics
const totalMethods = computed(() => assessmentMethods.value.length)
const formativeMethods = computed(() => assessmentMethods.value.filter(m => m.type === 'formatif').length)
const summativeMethods = computed(() => assessmentMethods.value.filter(m => m.type === 'sumatif').length)
const activeMethods = computed(() => assessmentMethods.value.filter(m => m.is_active).length)

// Table configuration
const tableHeaders = [
  { key: 'name', title: 'Method Name', sortable: true, type: 'text' as const },
  { key: 'type', title: 'Type', sortable: true, type: 'text' as const },
  { key: 'category', title: 'Category', sortable: true, type: 'text' as const },
  { key: 'description', title: 'Description', sortable: false, type: 'text' as const },
  { key: 'is_active', title: 'Status', sortable: true, type: 'boolean' as const },
  { key: 'created_at', title: 'Created', sortable: true, type: 'date' as const }
]

const tableFilters = [
  {
    key: 'type',
    label: 'Assessment Type',
    options: [
      { title: 'Formative', value: 'formatif' },
      { title: 'Summative', value: 'sumatif' }
    ]
  },
  {
    key: 'category',
    label: 'Category',
    options: [
      { title: 'Assignment (Tugas)', value: 'tugas' },
      { title: 'Quiz (Kuis)', value: 'kuis' },
      { title: 'Midterm Exam (UTS)', value: 'uts' },
      { title: 'Final Exam (UAS)', value: 'uas' },
      { title: 'Lab Work (Praktikum)', value: 'praktikum' },
      { title: 'Project (Proyek)', value: 'proyek' },
      { title: 'Presentation (Presentasi)', value: 'presentasi' },
      { title: 'Other (Lainnya)', value: 'lainnya' }
    ]
  },
  {
    key: 'is_active',
    label: 'Status',
    options: [
      { title: 'Active', value: true },
      { title: 'Inactive', value: false }
    ]
  }
]

// Computed options
const modalTitle = computed(() => {
  switch (modalMode.value) {
    case 'create':
      return 'Create New Assessment Method'
    case 'edit':
      return 'Edit Assessment Method'
    case 'view':
      return 'View Assessment Method Details'
    default:
      return 'Assessment Method Form'
  }
})

const modalIcon = computed(() => {
  switch (modalMode.value) {
    case 'create':
      return 'mdi-clipboard-plus'
    case 'edit':
      return 'mdi-clipboard-edit'
    case 'view':
      return 'mdi-clipboard-list'
    default:
      return 'mdi-clipboard-list'
  }
})

const typeOptions = [
  { title: 'Formative (Formatif)', value: 'formatif' },
  { title: 'Summative (Sumatif)', value: 'sumatif' }
]

const categoryOptions = [
  { title: 'Assignment (Tugas)', value: 'tugas' },
  { title: 'Quiz (Kuis)', value: 'kuis' },
  { title: 'Midterm Exam (UTS)', value: 'uts' },
  { title: 'Final Exam (UAS)', value: 'uas' },
  { title: 'Lab Work (Praktikum)', value: 'praktikum' },
  { title: 'Project (Proyek)', value: 'proyek' },
  { title: 'Presentation (Presentasi)', value: 'presentasi' },
  { title: 'Other (Lainnya)', value: 'lainnya' }
]

// Validation rules
const nameRules = [
  (v: string) => !!v || 'Method name is required',
  (v: string) => v.length >= 3 || 'Method name must be at least 3 characters'
]

const typeRules = [
  (v: string) => !!v || 'Assessment type is required'
]

const categoryRules = [
  (v: string) => !!v || 'Assessment category is required'
]

// Methods
const loadAssessmentMethods = async () => {
  loading.value = true
  try {
    const params = {
      ...pagination.value,
      search: searchQuery.value,
      ...filters.value
    }

    const response = await assessmentMethodsAPI.getAll(params)
    assessmentMethods.value = response.data.data
    totalItems.value = response.data.meta?.total || 0
  } catch (error: any) {
    errorMessage.value = 'Failed to load assessment methods'
    showError.value = true
    console.error('Load assessment methods error:', error)
  } finally {
    loading.value = false
  }
}

const openCreateModal = () => {
  modalMode.value = 'create'
  resetForm()
  showModal.value = true
}

const openEditModal = (method: AssessmentMethod) => {
  modalMode.value = 'edit'
  selectedMethod.value = method
  populateForm(method)
  showModal.value = true
}

const openViewModal = (method: AssessmentMethod) => {
  modalMode.value = 'view'
  selectedMethod.value = method
  populateForm(method)
  showModal.value = true
}

const openUsageAnalytics = (method: AssessmentMethod) => {
  selectedMethod.value = method
  showAnalyticsModal.value = true
}

const openDeleteDialog = (method: AssessmentMethod) => {
  selectedMethod.value = method
  showDeleteDialog.value = true
}

const resetForm = () => {
  Object.assign(formData, {
    name: '',
    description: '',
    type: 'formatif',
    category: 'tugas',
    is_active: true
  })
}

const populateForm = (method: AssessmentMethod) => {
  Object.assign(formData, {
    name: method.name,
    description: method.description || '',
    type: method.type,
    category: method.category,
    is_active: method.is_active
  })
}

const handleSubmit = async () => {
  modalLoading.value = true
  try {
    if (modalMode.value === 'create') {
      await assessmentMethodsAPI.create(formData)
      successMessage.value = 'Assessment method created successfully!'
    } else if (modalMode.value === 'edit' && selectedMethod.value) {
      await assessmentMethodsAPI.update(selectedMethod.value.id, formData)
      successMessage.value = 'Assessment method updated successfully!'
    }

    showSuccess.value = true
    closeModal()
    await loadAssessmentMethods()
  } catch (error: any) {
    errorMessage.value = error.response?.data?.message || 'Operation failed'
    showError.value = true
  } finally {
    modalLoading.value = false
  }
}

const confirmDelete = async () => {
  if (!selectedMethod.value) return

  deleteLoading.value = true
  try {
    await assessmentMethodsAPI.delete(selectedMethod.value.id)
    successMessage.value = 'Assessment method deleted successfully!'
    showSuccess.value = true
    showDeleteDialog.value = false
    await loadAssessmentMethods()
  } catch (error: any) {
    errorMessage.value = error.response?.data?.message || 'Delete failed'
    showError.value = true
  } finally {
    deleteLoading.value = false
  }
}

const closeModal = () => {
  showModal.value = false
  selectedMethod.value = null
  resetForm()
}

const handleSearch = (query: string) => {
  searchQuery.value = query
  pagination.value.page = 1
  loadAssessmentMethods()
}

const handleFilter = (filterValues: Record<string, any>) => {
  filters.value = filterValues
  pagination.value.page = 1
  loadAssessmentMethods()
}

const handleTableOptions = (options: any) => {
  pagination.value = {
    ...pagination.value,
    page: options.page,
    per_page: options.itemsPerPage,
    sort_by: options.sortBy?.[0]?.key || 'created_at',
    sort_order: options.sortBy?.[0]?.order || 'desc'
  }
  loadAssessmentMethods()
}

// Utility functions
const getTypeColor = (type: string) => {
  return type === 'formatif' ? 'success' : 'warning'
}

const getTypeIcon = (type: string) => {
  return type === 'formatif' ? 'mdi-clipboard-check' : 'mdi-clipboard-text'
}

const formatType = (type: string) => {
  return type === 'formatif' ? 'Formative' : 'Summative'
}

const getCategoryColor = (category: string) => {
  const colors: Record<string, string> = {
    tugas: 'blue',
    kuis: 'green',
    uts: 'orange',
    uas: 'red',
    praktikum: 'purple',
    proyek: 'indigo',
    presentasi: 'teal',
    lainnya: 'grey'
  }
  return colors[category] || 'primary'
}

const getCategoryIcon = (category: string) => {
  const icons: Record<string, string> = {
    tugas: 'mdi-file-document',
    kuis: 'mdi-help-circle',
    uts: 'mdi-school',
    uas: 'mdi-certificate',
    praktikum: 'mdi-flask',
    proyek: 'mdi-folder-multiple',
    presentasi: 'mdi-presentation',
    lainnya: 'mdi-dots-horizontal'
  }
  return icons[category] || 'mdi-clipboard-list'
}

const formatCategory = (category: string) => {
  const categories: Record<string, string> = {
    tugas: 'Assignment',
    kuis: 'Quiz',
    uts: 'Midterm',
    uas: 'Final Exam',
    praktikum: 'Lab Work',
    proyek: 'Project',
    presentasi: 'Presentation',
    lainnya: 'Other'
  }
  return categories[category] || category
}

// Lifecycle
onMounted(() => {
  loadAssessmentMethods()
})
</script>
