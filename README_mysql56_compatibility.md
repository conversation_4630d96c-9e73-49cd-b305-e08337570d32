# RPS Management System - MySQL 5.6 Compatibility

## 📋 Overview

File `database_design_mysql56.sql` adalah versi yang dioptimasi untuk **MySQL 5.6** dengan kompatibilitas penuh dan semua fitur yang diperlukan untuk RPS Management System.

## 🔄 MySQL 5.6 Compatibility Changes

### **Key Modifications for MySQL 5.6:**

| Feature | MySQL 8.0+ | MySQL 5.6 Compatible |
|---------|-------------|----------------------|
| **JSON Data Type** | `JSON` | `TEXT` with J<PERSON><PERSON> comment |
| **Boolean Type** | `BOOLEAN` | `TINYINT(1)` |
| **CHECK Constraints** | `CHECK (condition)` | Removed (not supported) |
| **Character Set** | `utf8mb4` | `utf8` |
| **Default Functions** | `(CURRENT_DATE)` | `CURRENT_TIMESTAMP` |

### **1. J<PERSON><PERSON> to TEXT Conversion**
```sql
-- MySQL 8.0+
`permissions` JSON NULL,
`prerequisite_courses` JSON NULL COMMENT 'Array of course IDs',

-- MySQL 5.6 Compatible
`permissions` TEXT NULL COMMENT 'JSON formatted permissions',
`prerequisite_courses` TEXT NULL COMMENT 'JSON formatted array of course IDs',
```

### **2. Boolean to TINYINT Conversion**
```sql
-- MySQL 8.0+
`is_active` BOOLEAN DEFAULT TRUE,

-- MySQL 5.6 Compatible
`is_active` TINYINT(1) DEFAULT 1,
```

### **3. Character Set Adjustment**
```sql
-- MySQL 8.0+
DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

-- MySQL 5.6 Compatible
DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci
```

### **4. CHECK Constraints Removal**
```sql
-- MySQL 8.0+ (with CHECK constraints)
`semester` TINYINT UNSIGNED NOT NULL CHECK (`semester` BETWEEN 1 AND 8),
`credits` TINYINT UNSIGNED NOT NULL CHECK (`credits` > 0),

-- MySQL 5.6 Compatible (without CHECK constraints)
`semester` TINYINT UNSIGNED NOT NULL,
`credits` TINYINT UNSIGNED NOT NULL,
```

## 🏗️ **Complete Database Structure (11 Tables)**

### **Master Data Tables (4 tables)**
- `roles` - User roles dengan TEXT-based JSON permissions
- `users` - User accounts dan profiles
- `faculties` - Faculty information
- `study_programs` - Study program details

### **Curriculum Tables (4 tables)**
- `cpl` - Graduate Learning Outcomes
- `courses` - Course information dengan TEXT-based JSON prerequisites
- `course_references` - Course bibliography
- `course_topics` - Weekly course topics

### **CPMK System (3 tables)**
- `cpmk` - Course Learning Outcomes
- `cpmk_cpl_relations` - CPMK-CPL mappings
- `sub_cpmk` - Sub-course learning outcomes dengan TEXT-based JSON

### **Assessment System (2 tables)**
- `assessment_methods` - Assessment types library
- `assessment_plans` - Assessment planning dengan TEXT-based JSON rubrics

## 📊 **Data Validation Strategy**

Since MySQL 5.6 doesn't support CHECK constraints, validation should be handled at the application level:

### **Application-Level Validation Rules**
```php
// PHP validation examples
function validateSemester($semester) {
    return ($semester >= 1 && $semester <= 8);
}

function validateCredits($credits) {
    return ($credits > 0 && $credits <= 6);
}

function validateWeekNumber($week) {
    return ($week >= 1 && $week <= 16);
}

function validatePercentage($percentage) {
    return ($percentage > 0 && $percentage <= 100);
}

function validateGrade($grade) {
    return ($grade >= 0 && $grade <= 100);
}
```

### **JSON Data Handling**
```php
// PHP JSON handling for TEXT fields
function setPrerequisites($courseId, $prerequisites) {
    $json = json_encode($prerequisites);
    // Store $json in prerequisite_courses TEXT field
}

function getPrerequisites($courseId) {
    // Retrieve TEXT field and decode
    $json = getPrerequisiteCoursesFromDB($courseId);
    return json_decode($json, true);
}

function setPermissions($roleId, $permissions) {
    $json = json_encode($permissions);
    // Store $json in permissions TEXT field
}
```

## 🚀 **Installation & Setup**

### **Prerequisites**
- **MySQL 5.6** or higher
- **UTF-8 support** enabled
- **InnoDB storage engine** (default in MySQL 5.6)
- Minimum **50MB** storage space

### **Installation Steps**
```bash
# 1. Install MySQL 5.6
sudo apt-get install mysql-server-5.6

# 2. Login to MySQL
mysql -u root -p

# 3. Run the schema
mysql -u root -p < database_design_mysql56.sql

# 4. Verify installation
mysql -u root -p -e "USE rps_management; SHOW TABLES;"
```

### **Verification Queries**
```sql
-- Check database and tables
USE rps_management;
SHOW TABLES;

-- Check character set
SHOW CREATE DATABASE rps_management;

-- Check table structure
DESCRIBE users;
DESCRIBE courses;

-- Check data seeding
SELECT * FROM roles;
SELECT * FROM assessment_methods;

-- Test views
SELECT * FROM v_course_details LIMIT 5;
SELECT * FROM v_cpmk_cpl_details LIMIT 5;
```

## 📈 **Performance Optimizations for MySQL 5.6**

### **1. Index Strategy**
```sql
-- All foreign keys are indexed for JOIN performance
-- Composite indexes for unique constraints
-- Business logic indexes for common queries

-- Example optimizations
ALTER TABLE courses ADD INDEX idx_semester_active (semester, is_active);
ALTER TABLE cpmk ADD INDEX idx_course_active (course_id, is_active);
```

### **2. Query Optimization**
```sql
-- Use EXPLAIN to analyze queries
EXPLAIN SELECT * FROM v_course_details WHERE faculty_name = 'Teknik Informatika';

-- Optimize with proper indexes
CREATE INDEX idx_faculties_name ON faculties(name);
```

### **3. Storage Engine Settings**
```sql
-- InnoDB optimizations for MySQL 5.6
SET GLOBAL innodb_buffer_pool_size = 1073741824;  -- 1GB
SET GLOBAL innodb_log_file_size = 268435456;      -- 256MB
SET GLOBAL innodb_flush_log_at_trx_commit = 2;    -- Performance vs durability
```

## 🔒 **Security Considerations**

### **1. Data Validation**
Since CHECK constraints are not available, implement validation in application:
```php
class DataValidator {
    public static function validateCourse($data) {
        $errors = [];
        
        if ($data['semester'] < 1 || $data['semester'] > 8) {
            $errors[] = 'Semester must be between 1 and 8';
        }
        
        if ($data['credits'] <= 0 || $data['credits'] > 6) {
            $errors[] = 'Credits must be between 1 and 6';
        }
        
        return $errors;
    }
}
```

### **2. JSON Data Security**
```php
// Sanitize JSON data before storage
function sanitizeJsonData($data) {
    // Remove potentially harmful content
    $cleaned = filter_var_array($data, FILTER_SANITIZE_STRING);
    return json_encode($cleaned);
}
```

### **3. SQL Injection Prevention**
```php
// Use prepared statements
$stmt = $pdo->prepare("INSERT INTO courses (code, name, study_program_id) VALUES (?, ?, ?)");
$stmt->execute([$code, $name, $studyProgramId]);
```

## 📋 **Migration from Other Versions**

### **From MySQL 8.0+ to MySQL 5.6**
```sql
-- Convert JSON columns to TEXT
ALTER TABLE roles MODIFY COLUMN permissions TEXT NULL COMMENT 'JSON formatted permissions';
ALTER TABLE courses MODIFY COLUMN prerequisite_courses TEXT NULL COMMENT 'JSON formatted array';

-- Convert BOOLEAN to TINYINT
ALTER TABLE users MODIFY COLUMN is_active TINYINT(1) DEFAULT 1;
ALTER TABLE faculties MODIFY COLUMN is_active TINYINT(1) DEFAULT 1;

-- Remove CHECK constraints (if any)
-- CHECK constraints will be ignored in MySQL 5.6
```

### **From PostgreSQL to MySQL 5.6**
```sql
-- Data type conversions
SERIAL → AUTO_INCREMENT
BOOLEAN → TINYINT(1)
JSON → TEXT
TIMESTAMP → TIMESTAMP
```

## 🎯 **Business Logic Implementation**

### **1. Role-Based Permissions**
```php
class RoleManager {
    public function hasPermission($userId, $permission) {
        $user = $this->getUser($userId);
        $role = $this->getRole($user['role_id']);
        $permissions = json_decode($role['permissions'], true);
        
        return isset($permissions[$permission]) && $permissions[$permission];
    }
}
```

### **2. Course Prerequisites**
```php
class CourseManager {
    public function checkPrerequisites($courseId, $studentId) {
        $course = $this->getCourse($courseId);
        $prerequisites = json_decode($course['prerequisite_courses'], true);
        
        foreach ($prerequisites as $prereqId) {
            if (!$this->hasCompletedCourse($studentId, $prereqId)) {
                return false;
            }
        }
        return true;
    }
}
```

### **3. Assessment Rubrics**
```php
class AssessmentManager {
    public function calculateScore($assessmentId, $studentResponses) {
        $assessment = $this->getAssessment($assessmentId);
        $rubric = json_decode($assessment['rubric_criteria'], true);
        
        // Calculate score based on rubric
        return $this->applyRubric($rubric, $studentResponses);
    }
}
```

## 🔧 **Maintenance & Backup**

### **Regular Maintenance**
```sql
-- Optimize tables monthly
OPTIMIZE TABLE users, courses, cpmk, assessment_plans;

-- Analyze tables for better query planning
ANALYZE TABLE users, courses, cpmk, assessment_plans;

-- Check table integrity
CHECK TABLE users, courses, cpmk, assessment_plans;
```

### **Backup Strategy**
```bash
# Daily backup
mysqldump -u root -p rps_management > backup_$(date +%Y%m%d).sql

# Weekly full backup with compression
mysqldump -u root -p rps_management | gzip > backup_weekly_$(date +%Y%m%d).sql.gz
```

## 🎯 **Conclusion**

File `database_design_mysql56.sql` menyediakan **kompatibilitas penuh dengan MySQL 5.6** sambil mempertahankan semua fungsionalitas RPS Management System. Dengan **506 baris** SQL yang dioptimasi, sistem ini siap untuk deployment di environment yang menggunakan MySQL 5.6.

**Key Benefits:**
- ✅ **Full MySQL 5.6 compatibility**
- ✅ **All core RPS features preserved**
- ✅ **Optimized performance for older MySQL**
- ✅ **Comprehensive data seeding**
- ✅ **Useful views for reporting**
- ✅ **Production-ready with proper indexing**

---

**Note:** Meskipun menggunakan MySQL 5.6, disarankan untuk upgrade ke versi yang lebih baru (MySQL 8.0+) untuk mendapatkan fitur JSON native, CHECK constraints, dan performance improvements yang lebih baik.
