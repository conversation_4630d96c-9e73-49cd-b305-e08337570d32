// Buttons
// *******************************************************************************

// condition needed as we using gray scale for disabled color
// $base-color: if($dark-style, $base, $black) !default;

.btn {
  --#{$prefix}btn-box-shadow: #{$btn-box-shadow};
  --#{$prefix}btn-focus-box-shadow: #{$btn-box-shadow};
  --#{$prefix}btn-active-border-color: transparent;
  --#{$prefix}btn-active-shadow: #{$btn-box-shadow};
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-transform: $btn-text-transform;

  .btn-group &,
  .input-group & {
    border-inline-end: $input-btn-border-width solid var(--#{$prefix}btn-group-border-color);
    border-inline-start: $input-btn-border-width solid var(--#{$prefix}btn-group-border-color);
  }
  .btn-group-vertical & {
    border-block-end: $input-btn-border-width solid var(--#{$prefix}btn-group-border-color);
    border-block-start: $input-btn-border-width solid var(--#{$prefix}btn-group-border-color);
  }
  .btn-group & {
    --#{$prefix}btn-box-shadow: none;
  }

  &.waves-effect:not(.waves-light) {
    .waves-ripple {
      background:
        radial-gradient(
          rgba(var(--#{$prefix}btn-waves-effect-color), .2) 0,
          rgba(var(--#{$prefix}btn-waves-effect-color), .3) 40%,
          rgba(var(--#{$prefix}btn-waves-effect-color), .4) 50%,
          rgba(var(--#{$prefix}btn-waves-effect-color), .5) 60%,
          rgba(var(--#{$prefix}white-rgb), 0) 70%
        );
    }
  }

  &[class*="btn-"] {
    box-shadow: var(--#{$prefix}btn-box-shadow);
  }

  .btn-check:checked + &,
  :not(.btn-check) + &:active:not([class*="btn-label-"]),
  &:first-child:active:not([class*="btn-label-"]),
  &.active:not([class*="btn-label-"]) {
    box-shadow: var(--#{$prefix}btn-active-shadow);
  }

  &:disabled,
  &.disabled,
  fieldset:disabled & {
    box-shadow: none;
  }

  &:focus {
    --#{$prefix}btn-color: var(--#{$prefix}btn-active-color);
    --#{$prefix}btn-bg: var(--#{$prefix}btn-active-bg);
  }

  /* Table Action Dropdown fix */
  &:not([class*="btn-"]):active,
  &:not([class*="btn-"]).active,
  &:not([class*="btn-"]).show,
  &:not([class*="btn-"]) {
    --#{$prefix}btn-border-width: 0;
  }

  /* Buttons Variant */

  /* Label */
  &[class*="btn-label-"] {
    --#{$prefix}btn-box-shadow: none;
    &:focus {
      --#{$prefix}btn-color: var(--#{$prefix}btn-active-color);
      --#{$prefix}btn-bg: var(--#{$prefix}btn-active-bg);
    }
  }

  /* Outline */
  &[class*="btn-outline-"] {
    &:not(:focus) {
      --#{$prefix}btn-bg: transparent;
    }
    --#{$prefix}btn-box-shadow: none;
    --#{$prefix}btn-focus-box-shadow: none;
    --#{$prefix}btn-active-shadow: none;
    --#{$prefix}btn-disabled-bg: transparent;

    .badge {
      --#{$prefix}badge-bg-color: var(--#{$prefix}btn-color);
      --#{$prefix}badge-color: var(--#{$prefix}btn-hover-color);
    }

    &:hover .badge,
    &:focus:hover .badge,
    &:focus:not(:hover) .badge,
    &:active .badge,
    &.active .badge,
    .show > &.dropdown-toggle .badge {
      &:not([class*="badge-outline"]) {
        --#{$prefix}badge-bg-color: var(--#{$prefix}btn-hover-color);
        --#{$prefix}badge-color: var(--#{$prefix}btn-hover-bg);
      }
    }
  }

  &.btn-white {
    --#{$prefix}btn-bg: var(--#{$prefix}white);
    --#{$prefix}btn-color: var(--#{$prefix}body-color);
    --#{$prefix}btn-border-color: var(--#{$prefix}white);
    --#{$prefix}btn-hover-color: var(--#{$prefix}black);
    --#{$prefix}btn-hover-bg: var(--#{$prefix}btn-bg);
    --#{$prefix}btn-hover-border-color: var(--#{$prefix}btn-border-color);
    --#{$prefix}btn-active-color: var(--#{$prefix}btn-hover-color);
    --#{$prefix}btn-active-bg: var(--#{$prefix}btn-hover-bg);
    --#{$prefix}btn-active-border-color: var(--#{$prefix}btn-hover-border-color);
    --#{$prefix}btn-box-shadow-rgb: var(--#{$prefix}white-rgb);
    --#{$prefix}btn-active-shadow-rgb: var(--#{$prefix}btn-box-shadow-rgb);
  }

  /* Text */
  &[class*="btn-text-"] {
    &:not(:focus) {
      --#{$prefix}btn-bg: transparent;
    }
    --#{$prefix}btn-border-color: transparent;
    --#{$prefix}btn-box-shadow: none;
    --#{$prefix}btn-focus-box-shadow: none;
    --#{$prefix}btn-active-shadow: none;
    --#{$prefix}btn-disabled-bg: transparent;
    --#{$prefix}btn-disabled-border-color: transparent;
    &:not(.btn-icon) {
      padding-inline: .875rem;
      &[class*="btn-sm"] {
        padding-inline: .625rem;
      }
      &[class*="btn-lg"] {
        padding-inline: 1.125rem;
      }
    }
  }
}

/* Badge within button */
.btn .badge {
  @include transition($btn-transition);
  inset-block-start: 0;
}

label.btn {
  margin-block-end: 0;
}

/* Button Sizes */

.btn-xl {
  @include button-size($btn-padding-y-xl, $btn-padding-x-xl, $btn-font-size-xl, $btn-border-radius-xl);
}

.btn-xs {
  @include button-size($btn-padding-y-xs, $btn-padding-x-xs, $btn-font-size-xs, $btn-border-radius-xs);
}

/* Icon button */

.btn-icon {
  $btn-icon-size-xl: ($btn-font-size-xl * $btn-line-height-xl) + ($btn-padding-y-xl * 2);
  $btn-icon-font-size-xl: $btn-font-size-xl * $btn-line-height-xl;
  $btn-icon-size-lg: ($btn-font-size-lg * $btn-line-height-lg) + ($btn-padding-y-lg * 2);
  $btn-icon-font-size-lg: $btn-font-size-lg * $btn-line-height-lg;
  $btn-icon-size: ($btn-font-size * $btn-line-height) + ($btn-padding-y * 2);
  $btn-icon-font-size: $btn-font-size * $btn-line-height;
  $btn-icon-size-sm: ($btn-font-size-sm * $btn-line-height-sm) + ($btn-padding-y-sm * 2);
  $btn-icon-font-size-sm: $btn-font-size-sm;
  $btn-icon-size-xs: ($btn-font-size-xs * $btn-line-height-xs) + ($btn-padding-y-xs * 2);
  $btn-icon-font-size-xs: $btn-font-size-xs;
  $borders-width: calc(#{$btn-border-width} * 2);

  display: inline-flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  padding: 0;
  block-size: calc(#{$btn-icon-size} + #{$borders-width});
  font-size: $btn-icon-font-size;
  inline-size: calc(#{$btn-icon-size} + #{$borders-width});

  .icon-base{
    @include icon-base($btn-icon-font-size);
  }

  &.btn-fab {
    block-size: calc(#{$btn-icon-size} + #{$borders-width});
    inline-size: calc(#{$btn-icon-size} + #{$borders-width});
  }

  &.btn-xl {
    block-size: calc(#{$btn-icon-size-xl} + #{$borders-width});
    inline-size: calc(#{$btn-icon-size-xl} + #{$borders-width});
    .icon-base {
      @include icon-base($btn-icon-font-size-xl);
    }
  }

  &.btn-lg {
    block-size: calc(#{$btn-icon-size-lg} + #{$borders-width});
    font-size: $btn-icon-font-size-lg;
    inline-size: calc(#{$btn-icon-size-lg} + #{$borders-width});
    .icon-base {
      @include icon-base($btn-icon-font-size-lg);
    }
  }

  &.btn-sm {
    block-size: calc(#{$btn-icon-size-sm} + #{$borders-width});
    font-size: $btn-icon-font-size-sm;
    inline-size: calc(#{$btn-icon-size-sm} + #{$borders-width});
    .icon-base {
      @include icon-base($btn-font-size-sm);
    }
  }

  &.btn-xs {
    block-size: calc(#{$btn-icon-size-xs} + #{$borders-width});
    font-size: $btn-icon-font-size-xs;
    inline-size: calc(#{$btn-icon-size-xs} + #{$borders-width});
    .icon-base {
      @include icon-base($btn-font-size-xs);
    }
  }
}

/* Link buttons */
.btn.btn-link {
  font-size: inherit;
}

.btn-pinned {
  position: absolute;
  inset-block-start: .75rem;
  inset-inline-end: .75rem;
}

/* Button focus */
button:focus,
button:focus-visible {
  outline: 0;
}

/* Generate contextual modifier classes for colorizing the button */

/* The $custom-theme-colors variable is used to ensure that the colors for the social buttons
 come from a separate array. To achieve this, both the $custom-colors and $theme-colors
 arrays are merged in the _color.scss file. */
@each $state in map-keys($custom-theme-colors) {
  /* Default */
  .btn-#{$state} {
    --#{$prefix}btn-bg: var(--#{$prefix}#{$state});
    --#{$prefix}btn-color: var(--#{$prefix}#{$state}-contrast);
    --#{$prefix}btn-border-color: var(--#{$prefix}#{$state});
    --#{$prefix}btn-hover-color: var(--#{$prefix}#{$state}-contrast);
    --#{$prefix}btn-hover-bg: color-mix(in sRGB, #{$color-contrast-dark} #{$btn-hover-bg-shade-amount}, var(--#{$prefix}#{$state}));
    --#{$prefix}btn-hover-border-color: var(--#{$prefix}btn-hover-bg);
    --#{$prefix}btn-active-color: var(--#{$prefix}#{$state}-contrast);
    --#{$prefix}btn-active-bg: color-mix(in sRGB, #{$color-contrast-dark} #{$btn-active-bg-shade-amount}, var(--#{$prefix}#{$state}));
    --#{$prefix}btn-active-border-color: var(--#{$prefix}btn-active-bg);
    --#{$prefix}btn-disabled-color: var(--#{$prefix}#{$state}-contrast);
    --#{$prefix}btn-disabled-bg: var(--#{$prefix}#{$state});
    --#{$prefix}btn-disabled-border-color: var(--#{$prefix}#{$state});
    --#{$prefix}btn-group-border-color: color-mix(in sRGB, #{$color-contrast-dark} #{$btn-active-bg-shade-amount}, var(--#{$prefix}#{$state}));
    --#{$prefix}btn-waves-effect-color: var(--#{$prefix}#{$state}-rgb);
  }

  /* Label */
  .btn-label-#{$state}{
    @if $state == "light" {
      --#{$prefix}btn-color: var(--#{$prefix}#{$state}-contrast);
    } @else {
      --#{$prefix}btn-color: var(--#{$prefix}#{$state});
    }
    --#{$prefix}btn-bg: color-mix(in sRGB, var(--#{$prefix}paper-bg) #{$btn-label-tint-amount}, var(--#{$prefix}#{$state}));
    --#{$prefix}btn-border-color: var(--#{$prefix}btn-bg);
    --#{$prefix}btn-hover-color: var(--#{$prefix}#{$state});
    --#{$prefix}btn-hover-bg: color-mix(in sRGB, var(--#{$prefix}paper-bg) #{$btn-label-active-shade-amount}, var(--#{$prefix}#{$state}));
    --#{$prefix}btn-hover-border-color: var(--#{$prefix}btn-hover-bg);
    --#{$prefix}btn-active-color: var(--#{$prefix}#{$state});
    --#{$prefix}btn-active-bg: color-mix(in sRGB, var(--#{$prefix}paper-bg) #{$btn-label-active-shade-amount}, var(--#{$prefix}#{$state}));
    --#{$prefix}btn-active-border-color: var(--#{$prefix}btn-active-bg);
    --#{$prefix}btn-disabled-color: var(--#{$prefix}btn-color);
    --#{$prefix}btn-disabled-bg: var(--#{$prefix}btn-bg);
    --#{$prefix}btn-disabled-border-color: var(--#{$prefix}btn-border-color);
    --#{$prefix}btn-waves-effect-color: var(--#{$prefix}#{$state}-rgb);
    --#{$prefix}btn-group-border-color: color-mix(in sRGB, var(--#{$prefix}paper-bg) #{$btn-label-group-border-shade-amount}, var(--#{$prefix}#{$state}));
  }

  /* Outline */
  .btn-outline-#{$state}{
    --#{$prefix}btn-color: var(--#{$prefix}#{$state});
    --#{$prefix}btn-border-color: var(--#{$prefix}#{$state});
    --#{$prefix}btn-hover-color: var(--#{$prefix}#{$state});
    --#{$prefix}btn-hover-bg: color-mix(in sRGB, var(--#{$prefix}paper-bg) #{$btn-outline-tint-amount}, var(--#{$prefix}#{$state}));
    --#{$prefix}btn-hover-border-color: var(--#{$prefix}btn-border-color);
    --#{$prefix}btn-active-color: var(--#{$prefix}#{$state});
    --#{$prefix}btn-active-bg: color-mix(in sRGB, var(--#{$prefix}paper-bg) #{$btn-outline-tint-amount}, var(--#{$prefix}#{$state}));
    --#{$prefix}btn-active-border-color: var(--#{$prefix}btn-border-color);
    --#{$prefix}btn-disabled-color: var(--#{$prefix}#{$state});
    --#{$prefix}btn-disabled-border-color: var(--#{$prefix}#{$state});
    --#{$prefix}btn-group-border-color: var(--#{$prefix}#{$state});
    --#{$prefix}btn-waves-effect-color: var(--#{$prefix}#{$state}-rgb);
  }

  /* Text */
  .btn-text-#{$state} {
    @if #{$state} == "secondary" {
      --#{$prefix}btn-color: var(--#{$prefix}body-color);
      --#{$prefix}btn-hover-color: var(--#{$prefix}body-color);
      --#{$prefix}btn-active-color: var(--#{$prefix}body-color);
    } @else {
      --#{$prefix}btn-color: var(--#{$prefix}#{$state});
      --#{$prefix}btn-hover-color: var(--#{$prefix}#{$state});
      --#{$prefix}btn-active-color: var(--#{$prefix}#{$state});
      --#{$prefix}btn-group-border-color: var(--#{$prefix}#{$state});
    }
    --#{$prefix}btn-active-color: var(--#{$prefix}#{$state});
    --#{$prefix}btn-group-border-color: var(--#{$prefix}#{$state});
    --#{$prefix}btn-waves-effect-color: var(--#{$prefix}#{$state}-rgb);
    --#{$prefix}btn-hover-bg: color-mix(in sRGB, var(--#{$prefix}paper-bg) #{$btn-text-tint-amount}, var(--#{$prefix}#{$state}));
    --#{$prefix}btn-hover-border-color: var(--#{$prefix}btn-hover-bg);
    --#{$prefix}btn-active-bg: color-mix(in sRGB, var(--#{$prefix}paper-bg) #{$btn-text-tint-amount}, var(--#{$prefix}#{$state}));
    --#{$prefix}btn-active-border-color: var(--#{$prefix}btn-active-bg);
    --#{$prefix}btn-disabled-color: var(--#{$prefix}#{$state});
  }
}
