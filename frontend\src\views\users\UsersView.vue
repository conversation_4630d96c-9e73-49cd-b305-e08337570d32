<template>
  <div>
    <!-- <PERSON>er -->
    <v-row class="mb-6">
      <v-col>
        <h1 class="text-h4 font-weight-bold text-primary">User Management</h1>
        <p class="text-subtitle-1 text-medium-emphasis">
          Manage system users, roles, and permissions
        </p>
      </v-col>
    </v-row>

    <!-- Data Table -->
    <DataTable
      title="System Users"
      icon="mdi-account-group"
      item-name="User"
      :headers="tableHeaders"
      :items="users"
      :loading="loading"
      :total-items="totalUsers"
      :filters="tableFilters"
      @add="openCreateModal"
      @edit="openEditModal"
      @delete="openDeleteDialog"
      @view="openViewModal"
      @refresh="loadUsers"
      @search="handleSearch"
      @filter="handleFilter"
      @update:options="handleTableOptions"
    >
      <!-- Custom slots for specific columns -->
      <template #item.avatar="{ item }">
        <v-avatar size="32">
          <v-img
            v-if="item.avatar"
            :src="item.avatar"
            :alt="item.full_name"
          />
          <v-icon v-else>mdi-account</v-icon>
        </v-avatar>
      </template>

      <template #item.roles="{ item }">
        <div class="d-flex flex-wrap gap-1">
          <v-chip
            v-for="role in item.roles"
            :key="role"
            size="small"
            color="primary"
            variant="tonal"
          >
            {{ formatRole(role) }}
          </v-chip>
        </div>
      </template>

      <template #item.faculty="{ item }">
        <span v-if="item.faculty_name">{{ item.faculty_name }}</span>
        <span v-else class="text-medium-emphasis">-</span>
      </template>

      <template #item.study_program="{ item }">
        <span v-if="item.study_program_name">{{ item.study_program_name }}</span>
        <span v-else class="text-medium-emphasis">-</span>
      </template>
    </DataTable>

    <!-- User Form Modal -->
    <FormModal
      v-model="showModal"
      :title="modalTitle"
      :icon="modalIcon"
      :mode="modalMode"
      :loading="modalLoading"
      @submit="handleSubmit"
      @close="closeModal"
    >
      <v-row>
        <v-col cols="12" md="6">
          <v-text-field
            v-model="formData.full_name"
            :rules="nameRules"
            label="Full Name *"
            variant="outlined"
            prepend-inner-icon="mdi-account"
            :disabled="modalLoading || modalMode === 'view'"
          />
        </v-col>

        <v-col cols="12" md="6">
          <v-text-field
            v-model="formData.username"
            :rules="usernameRules"
            label="Username *"
            variant="outlined"
            prepend-inner-icon="mdi-at"
            :disabled="modalLoading || modalMode === 'view'"
          />
        </v-col>

        <v-col cols="12" md="6">
          <v-text-field
            v-model="formData.email"
            :rules="emailRules"
            label="Email *"
            type="email"
            variant="outlined"
            prepend-inner-icon="mdi-email"
            :disabled="modalLoading || modalMode === 'view'"
          />
        </v-col>

        <v-col cols="12" md="6">
          <v-text-field
            v-model="formData.phone"
            label="Phone Number"
            variant="outlined"
            prepend-inner-icon="mdi-phone"
            :disabled="modalLoading || modalMode === 'view'"
          />
        </v-col>

        <v-col cols="12" md="6">
          <v-select
            v-model="formData.roles"
            :items="roleOptions"
            :rules="rolesRules"
            label="Roles *"
            variant="outlined"
            prepend-inner-icon="mdi-shield-account"
            multiple
            chips
            :disabled="modalLoading || modalMode === 'view'"
          />
        </v-col>

        <v-col cols="12" md="6">
          <v-select
            v-model="formData.faculty_id"
            :items="facultyOptions"
            label="Faculty"
            variant="outlined"
            prepend-inner-icon="mdi-domain"
            clearable
            :disabled="modalLoading || modalMode === 'view'"
          />
        </v-col>

        <v-col cols="12" md="6">
          <v-select
            v-model="formData.study_program_id"
            :items="studyProgramOptions"
            label="Study Program"
            variant="outlined"
            prepend-inner-icon="mdi-school"
            clearable
            :disabled="modalLoading || modalMode === 'view' || !formData.faculty_id"
          />
        </v-col>

        <v-col cols="12" md="6">
          <v-switch
            v-model="formData.is_active"
            label="Active"
            color="primary"
            :disabled="modalLoading || modalMode === 'view'"
          />
        </v-col>

        <v-col v-if="modalMode === 'create'" cols="12">
          <v-text-field
            v-model="formData.password"
            :rules="passwordRules"
            :type="showPassword ? 'text' : 'password'"
            label="Password *"
            variant="outlined"
            prepend-inner-icon="mdi-lock"
            :append-inner-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
            @click:append-inner="showPassword = !showPassword"
            :disabled="modalLoading"
          />
        </v-col>
      </v-row>
    </FormModal>

    <!-- Delete Confirmation Dialog -->
    <v-dialog v-model="showDeleteDialog" max-width="400">
      <v-card>
        <v-card-title class="text-h6">Confirm Delete</v-card-title>
        <v-card-text>
          Are you sure you want to delete user "{{ selectedUser?.full_name }}"?
          This action cannot be undone.
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn @click="showDeleteDialog = false">Cancel</v-btn>
          <v-btn
            color="error"
            :loading="deleteLoading"
            @click="confirmDelete"
          >
            Delete
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Success/Error Snackbars -->
    <v-snackbar
      v-model="showSuccess"
      color="success"
      timeout="3000"
    >
      {{ successMessage }}
      <template v-slot:actions>
        <v-btn @click="showSuccess = false">Close</v-btn>
      </template>
    </v-snackbar>

    <v-snackbar
      v-model="showError"
      color="error"
      timeout="5000"
    >
      {{ errorMessage }}
      <template v-slot:actions>
        <v-btn @click="showError = false">Close</v-btn>
      </template>
    </v-snackbar>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import DataTable from '@/components/common/DataTable.vue'
import FormModal from '@/components/common/FormModal.vue'
import { usersAPI, facultiesAPI, studyProgramsAPI } from '@/services/api'
import type { User, Faculty, StudyProgram, PaginationParams } from '@/types/auth'

// State
const loading = ref(false)
const modalLoading = ref(false)
const deleteLoading = ref(false)
const showModal = ref(false)
const showDeleteDialog = ref(false)
const showPassword = ref(false)
const showSuccess = ref(false)
const showError = ref(false)
const successMessage = ref('')
const errorMessage = ref('')

const users = ref<User[]>([])
const faculties = ref<Faculty[]>([])
const studyPrograms = ref<StudyProgram[]>([])
const totalUsers = ref(0)
const selectedUser = ref<User | null>(null)
const modalMode = ref<'create' | 'edit' | 'view'>('create')

// Form data
const formData = reactive({
  full_name: '',
  username: '',
  email: '',
  phone: '',
  roles: [] as string[],
  faculty_id: null as number | null,
  study_program_id: null as number | null,
  is_active: true,
  password: ''
})

// Pagination and filtering
const searchQuery = ref('')
const filters = ref<Record<string, any>>({})
const pagination = ref<PaginationParams>({
  page: 1,
  per_page: 20,
  sort_by: 'created_at',
  sort_order: 'desc'
})

// Table configuration
const tableHeaders = [
  { key: 'avatar', title: 'Avatar', sortable: false, width: '60px', type: 'text' as const },
  { key: 'full_name', title: 'Full Name', sortable: true, type: 'text' as const },
  { key: 'username', title: 'Username', sortable: true, type: 'text' as const },
  { key: 'email', title: 'Email', sortable: true, type: 'text' as const },
  { key: 'roles', title: 'Roles', sortable: false, type: 'text' as const },
  { key: 'faculty', title: 'Faculty', sortable: false, type: 'text' as const },
  { key: 'study_program', title: 'Study Program', sortable: false, type: 'text' as const },
  { key: 'is_active', title: 'Status', sortable: true, type: 'boolean' as const },
  { key: 'created_at', title: 'Created', sortable: true, type: 'date' as const }
]

const tableFilters = [
  {
    key: 'role',
    label: 'Role',
    options: [
      { title: 'Admin', value: 'admin' },
      { title: 'Dekan', value: 'dekan' },
      { title: 'Wakil Dekan', value: 'wakil_dekan' },
      { title: 'Kepala Prodi', value: 'kepala_prodi' },
      { title: 'Sekretaris Prodi', value: 'sekretaris_prodi' },
      { title: 'Dosen', value: 'dosen' }
    ]
  },
  {
    key: 'is_active',
    label: 'Status',
    options: [
      { title: 'Active', value: true },
      { title: 'Inactive', value: false }
    ]
  }
]

// Computed
const modalTitle = computed(() => {
  switch (modalMode.value) {
    case 'create':
      return 'Create New User'
    case 'edit':
      return 'Edit User'
    case 'view':
      return 'View User Details'
    default:
      return 'User Form'
  }
})

const modalIcon = computed(() => {
  switch (modalMode.value) {
    case 'create':
      return 'mdi-account-plus'
    case 'edit':
      return 'mdi-account-edit'
    case 'view':
      return 'mdi-account'
    default:
      return 'mdi-account'
  }
})

const roleOptions = [
  { title: 'Administrator', value: 'admin' },
  { title: 'Dekan', value: 'dekan' },
  { title: 'Wakil Dekan', value: 'wakil_dekan' },
  { title: 'Kepala Program Studi', value: 'kepala_prodi' },
  { title: 'Sekretaris Program Studi', value: 'sekretaris_prodi' },
  { title: 'Dosen', value: 'dosen' }
]

const facultyOptions = computed(() =>
  faculties.value.map(faculty => ({
    title: faculty.name,
    value: faculty.id
  }))
)

const studyProgramOptions = computed(() => {
  if (!formData.faculty_id) return []
  return studyPrograms.value
    .filter(sp => sp.faculty_id === formData.faculty_id)
    .map(sp => ({
      title: sp.name,
      value: sp.id
    }))
})

// Validation rules
const nameRules = [
  (v: string) => !!v || 'Full name is required',
  (v: string) => v.length >= 2 || 'Name must be at least 2 characters'
]

const usernameRules = [
  (v: string) => !!v || 'Username is required',
  (v: string) => v.length >= 3 || 'Username must be at least 3 characters',
  (v: string) => /^[a-zA-Z0-9_]+$/.test(v) || 'Username can only contain letters, numbers, and underscores'
]

const emailRules = [
  (v: string) => !!v || 'Email is required',
  (v: string) => /.+@.+\..+/.test(v) || 'Email must be valid'
]

const rolesRules = [
  (v: string[]) => v.length > 0 || 'At least one role is required'
]

const passwordRules = [
  (v: string) => !!v || 'Password is required',
  (v: string) => v.length >= 6 || 'Password must be at least 6 characters'
]

// Methods
const loadUsers = async () => {
  loading.value = true
  try {
    const params = {
      ...pagination.value,
      search: searchQuery.value,
      ...filters.value
    }

    const response = await usersAPI.getAll(params)
    users.value = response.data.data
    totalUsers.value = response.data.meta?.total || 0
  } catch (error: any) {
    errorMessage.value = 'Failed to load users'
    showError.value = true
    console.error('Load users error:', error)
  } finally {
    loading.value = false
  }
}

const loadFaculties = async () => {
  try {
    const response = await facultiesAPI.getAll({ per_page: 100 })
    faculties.value = response.data.data
  } catch (error) {
    console.error('Load faculties error:', error)
  }
}

const loadStudyPrograms = async () => {
  try {
    const response = await studyProgramsAPI.getAll({ per_page: 1000 })
    studyPrograms.value = response.data.data
  } catch (error) {
    console.error('Load study programs error:', error)
  }
}

const openCreateModal = () => {
  modalMode.value = 'create'
  resetForm()
  showModal.value = true
}

const openEditModal = (user: User) => {
  modalMode.value = 'edit'
  selectedUser.value = user
  populateForm(user)
  showModal.value = true
}

const openViewModal = (user: User) => {
  modalMode.value = 'view'
  selectedUser.value = user
  populateForm(user)
  showModal.value = true
}

const openDeleteDialog = (user: User) => {
  selectedUser.value = user
  showDeleteDialog.value = true
}

const resetForm = () => {
  Object.assign(formData, {
    full_name: '',
    username: '',
    email: '',
    phone: '',
    roles: [],
    faculty_id: null,
    study_program_id: null,
    is_active: true,
    password: ''
  })
}

const populateForm = (user: User) => {
  Object.assign(formData, {
    full_name: user.full_name,
    username: user.username,
    email: user.email,
    phone: user.phone || '',
    roles: user.roles,
    faculty_id: user.faculty_id,
    study_program_id: user.study_program_id,
    is_active: user.is_active,
    password: ''
  })
}

const handleSubmit = async () => {
  modalLoading.value = true
  try {
    if (modalMode.value === 'create') {
      await usersAPI.create(formData)
      successMessage.value = 'User created successfully!'
    } else if (modalMode.value === 'edit' && selectedUser.value) {
      const { password, ...updateData } = formData // Exclude password from edit mode
      await usersAPI.update(selectedUser.value.id, updateData)
      successMessage.value = 'User updated successfully!'
    }

    showSuccess.value = true
    closeModal()
    await loadUsers()
  } catch (error: any) {
    errorMessage.value = error.response?.data?.message || 'Operation failed'
    showError.value = true
  } finally {
    modalLoading.value = false
  }
}

const confirmDelete = async () => {
  if (!selectedUser.value) return

  deleteLoading.value = true
  try {
    await usersAPI.delete(selectedUser.value.id)
    successMessage.value = 'User deleted successfully!'
    showSuccess.value = true
    showDeleteDialog.value = false
    await loadUsers()
  } catch (error: any) {
    errorMessage.value = error.response?.data?.message || 'Delete failed'
    showError.value = true
  } finally {
    deleteLoading.value = false
  }
}

const closeModal = () => {
  showModal.value = false
  selectedUser.value = null
  resetForm()
}

const handleSearch = (query: string) => {
  searchQuery.value = query
  pagination.value.page = 1
  loadUsers()
}

const handleFilter = (filterValues: Record<string, any>) => {
  filters.value = filterValues
  pagination.value.page = 1
  loadUsers()
}

const handleTableOptions = (options: any) => {
  pagination.value = {
    ...pagination.value,
    page: options.page,
    per_page: options.itemsPerPage,
    sort_by: options.sortBy?.[0]?.key || 'created_at',
    sort_order: options.sortBy?.[0]?.order || 'desc'
  }
  loadUsers()
}

const formatRole = (role: string) => {
  const roleMap: Record<string, string> = {
    admin: 'Administrator',
    dekan: 'Dekan',
    wakil_dekan: 'Wakil Dekan',
    kepala_prodi: 'Kepala Program Studi',
    sekretaris_prodi: 'Sekretaris Program Studi',
    dosen: 'Dosen'
  }
  return roleMap[role] || role
}

// Watch for faculty changes to reset study program
watch(() => formData.faculty_id, () => {
  formData.study_program_id = null
})

// Lifecycle
onMounted(async () => {
  await Promise.all([
    loadUsers(),
    loadFaculties(),
    loadStudyPrograms()
  ])
})
</script>
