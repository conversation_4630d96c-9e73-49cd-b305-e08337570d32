<template>
  <v-container fluid class="fill-height">
    <v-row align="center" justify="center" class="fill-height">
      <v-col cols="12" sm="8" md="6" class="text-center">
        <div class="error-container">
          <h1 class="error-code">404</h1>
          <h2 class="error-title">Page Not Found</h2>
          <p class="error-description">
            The page you're looking for doesn't exist or has been moved.
          </p>
          
          <div class="mt-8">
            <v-btn
              color="primary"
              size="large"
              @click="$router.push('/dashboard')"
              class="mr-4"
            >
              <v-icon start>mdi-home</v-icon>
              Go to Dashboard
            </v-btn>
            
            <v-btn
              variant="outlined"
              size="large"
              @click="$router.back()"
            >
              <v-icon start>mdi-arrow-left</v-icon>
              Go Back
            </v-btn>
          </div>
        </div>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup lang="ts">
// No script needed for this simple component
</script>

<style scoped>
.fill-height {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.error-container {
  padding: 2rem;
}

.error-code {
  font-size: 8rem;
  font-weight: 900;
  color: #1976d2;
  line-height: 1;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.error-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 1rem;
}

.error-description {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 2rem;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

@media (max-width: 600px) {
  .error-code {
    font-size: 6rem;
  }
  
  .error-title {
    font-size: 2rem;
  }
  
  .error-description {
    font-size: 1rem;
  }
}
</style>
