/* Select
******************************************************************************* */

.form-select {
  background-clip: padding-box;
  padding-block: calc($form-select-padding-y - $input-border-width);
  padding-inline-end: calc($form-select-indicator-padding - $input-border-width);
  padding-inline-start: calc($form-select-padding-x - $input-border-width);
  &[multiple],
  &[size]:not([size="1"]) {
    padding-inline-end: $form-select-padding-x;
  }
  &:hover {
    &:not(:focus):not(:disabled) {
      border-color: $input-hover-border-color;
    }
  }
  &:disabled {
    background-image: escape-svg($form-select-disabled-indicator);
  }
  &:focus,
  &:focus-within {
    border-width: $input-focus-border-width;
    background-position: right calc($form-select-padding-x - 1px) center;
    padding-block: calc($form-select-padding-y - $input-focus-border-width);
    padding-inline-end: calc($form-select-indicator-padding - $input-focus-border-width);
    padding-inline-start: calc($form-select-padding-x - $input-focus-border-width);
  }
  &.form-select-lg {
    background-size: 24px 24px;
    min-block-size: $input-height-lg;
    padding-block: calc($form-select-padding-y-lg - $input-border-width);
    padding-inline-start: calc($form-select-padding-x-lg - $input-border-width);
    &:focus {
      padding-block: calc($form-select-padding-y-lg - $input-focus-border-width);
      padding-inline-start: calc($form-select-padding-x-lg - $input-focus-border-width);
    }
  }
  &.form-select-sm {
    background-size: 20px 20px;
    min-block-size: $input-height-sm;
    padding-block: calc($form-select-padding-y-sm - $input-border-width);
    padding-inline-start: calc($form-select-padding-x-sm - $input-border-width);
    &:focus {
      padding-block: calc($form-select-padding-y-sm - $input-focus-border-width);
      padding-inline-start: calc($form-select-padding-x-sm - $input-focus-border-width);
    }
  }
  &[multiple]:focus {
    padding-inline-end: .875rem !important;
  }
}
