@echo off
echo.
echo ========================================
echo  RPS Backend MySQL Setup
echo ========================================
echo.

cd /d "%~dp0"

echo [INFO] Setting up RPS Management System with MySQL backend...
echo.

REM Check if PHP is available
php --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] PHP is not available in PATH
    echo [ERROR] Please ensure PHP 8.1+ is installed and accessible
    pause
    exit /b 1
)

echo [1/8] Checking PHP MySQL extensions...
php -m | findstr mysqli >nul
if errorlevel 1 (
    echo [ERROR] MySQLi extension is not loaded
    echo [ERROR] Please enable extension=mysqli in php.ini
    pause
    exit /b 1
) else (
    echo [SUCCESS] MySQLi extension is loaded
)

php -m | findstr pdo_mysql >nul
if errorlevel 1 (
    echo [WARNING] PDO MySQL extension is not loaded
    echo [WARNING] Please enable extension=pdo_mysql in php.ini for better compatibility
) else (
    echo [SUCCESS] PDO MySQL extension is loaded
)

echo.
echo [2/8] Checking MySQL service...
REM Test MySQL connection
php -r "
try {
    \$mysqli = new mysqli('localhost', 'root', '', 'information_schema');
    if (\$mysqli->connect_error) {
        echo '[ERROR] MySQL connection failed: ' . \$mysqli->connect_error . PHP_EOL;
        exit(1);
    } else {
        echo '[SUCCESS] MySQL service is running' . PHP_EOL;
        \$mysqli->close();
    }
} catch (Exception \$e) {
    echo '[ERROR] MySQL connection failed: ' . \$e->getMessage() . PHP_EOL;
    exit(1);
}
"

if errorlevel 1 (
    echo [ERROR] Please start MySQL service in Laragon or XAMPP
    pause
    exit /b 1
)

echo.
echo [3/8] Creating database...
php -r "
try {
    \$mysqli = new mysqli('localhost', 'root', '');
    if (\$mysqli->connect_error) {
        echo '[ERROR] Cannot connect to MySQL: ' . \$mysqli->connect_error . PHP_EOL;
        exit(1);
    }
    
    // Create database if not exists
    \$result = \$mysqli->query('CREATE DATABASE IF NOT EXISTS rps_management CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci');
    if (\$result) {
        echo '[SUCCESS] Database rps_management created or already exists' . PHP_EOL;
    } else {
        echo '[ERROR] Failed to create database: ' . \$mysqli->error . PHP_EOL;
        exit(1);
    }
    
    \$mysqli->close();
} catch (Exception \$e) {
    echo '[ERROR] Database creation failed: ' . \$e->getMessage() . PHP_EOL;
    exit(1);
}
"

if errorlevel 1 (
    echo [ERROR] Database creation failed
    pause
    exit /b 1
)

echo.
echo [4/8] Testing database connection with configured credentials...
cd backend
php -r "
// Load environment
if (file_exists('.env')) {
    \$lines = file('.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach (\$lines as \$line) {
        if (strpos(\$line, '=') !== false && !str_starts_with(trim(\$line), '#')) {
            list(\$key, \$value) = explode('=', \$line, 2);
            \$_ENV[trim(\$key)] = trim(\$value);
        }
    }
}

\$host = \$_ENV['database.default.hostname'] ?? 'localhost';
\$database = \$_ENV['database.default.database'] ?? 'rps_management';
\$username = \$_ENV['database.default.username'] ?? 'root';
\$password = \$_ENV['database.default.password'] ?? '';

try {
    \$mysqli = new mysqli(\$host, \$username, \$password, \$database);
    if (\$mysqli->connect_error) {
        echo '[ERROR] Database connection failed: ' . \$mysqli->connect_error . PHP_EOL;
        exit(1);
    } else {
        echo '[SUCCESS] Connected to database: ' . \$database . PHP_EOL;
        \$mysqli->close();
    }
} catch (Exception \$e) {
    echo '[ERROR] Database connection failed: ' . \$e->getMessage() . PHP_EOL;
    exit(1);
}
"

if errorlevel 1 (
    echo [ERROR] Database connection test failed
    echo [INFO] Please check your .env database configuration
    pause
    exit /b 1
)

echo.
echo [5/8] Running database migrations...
php spark migrate

if errorlevel 1 (
    echo [ERROR] Migration failed
    echo [INFO] Please check the migration files and database connection
    pause
    exit /b 1
) else (
    echo [SUCCESS] Migrations completed successfully
)

echo.
echo [6/8] Running database seeders...
php spark db:seed

if errorlevel 1 (
    echo [ERROR] Seeding failed
    echo [INFO] Please check the seeder files
    pause
    exit /b 1
) else (
    echo [SUCCESS] Database seeding completed successfully
)

echo.
echo [7/8] Verifying database setup...
php -r "
\$host = 'localhost';
\$database = 'rps_management';
\$username = 'root';
\$password = '';

try {
    \$mysqli = new mysqli(\$host, \$username, \$password, \$database);
    if (\$mysqli->connect_error) {
        echo '[ERROR] Verification failed: ' . \$mysqli->connect_error . PHP_EOL;
        exit(1);
    }
    
    // Check tables
    \$tables = ['roles', 'users', 'faculties', 'study_programs'];
    foreach (\$tables as \$table) {
        \$result = \$mysqli->query(\"SELECT COUNT(*) as count FROM \$table\");
        if (\$result) {
            \$row = \$result->fetch_assoc();
            echo \"[INFO] Table '\$table': \" . \$row['count'] . \" records\" . PHP_EOL;
        } else {
            echo \"[WARNING] Table '\$table' not found or empty\" . PHP_EOL;
        }
    }
    
    \$mysqli->close();
    echo '[SUCCESS] Database verification completed' . PHP_EOL;
} catch (Exception \$e) {
    echo '[ERROR] Verification failed: ' . \$e->getMessage() . PHP_EOL;
    exit(1);
}
"

echo.
echo [8/8] Starting backend server for testing...
echo [INFO] Starting CodeIgniter development server...
echo [INFO] Backend will be available at: http://localhost:8080
echo [INFO] Press Ctrl+C to stop the server
echo.

start "RPS Backend Server" cmd /k "php spark serve --host=0.0.0.0 --port=8080"

echo.
echo ========================================
echo  MySQL Backend Setup Complete!
echo ========================================
echo.
echo ✅ Database: rps_management created
echo ✅ Migrations: All tables created
echo ✅ Seeders: Sample data inserted
echo ✅ Server: Running on http://localhost:8080
echo.
echo Next steps:
echo 1. Test the backend: http://localhost:8080
echo 2. Run the testing framework: cd testing_backend && run_all_tests.bat
echo 3. Start the frontend: cd frontend && npm run dev
echo.
pause
