<?php

/**
 * Environment & Setup Tests
 * 
 * Tests PHP version, extensions, CodeIgniter framework, and basic configuration
 */

// Ensure we have access to the test runner
if (!isset($testRunner)) {
    die("This test must be run through the TestRunner\n");
}

$testRunner->logSubSection('PHP Environment Tests');

$tests = [
    'php_version' => 'PHP Version Check',
    'php_extensions' => 'Required PHP Extensions',
    'codeigniter_framework' => 'CodeIgniter Framework',
    'file_permissions' => 'File Permissions',
    'configuration_files' => 'Configuration Files',
    'environment_variables' => 'Environment Variables'
];

$totalTests = count($tests);
$currentTest = 0;
$passedTests = 0;

foreach ($tests as $testKey => $testName) {
    $currentTest++;
    $testRunner->logProgress($currentTest, $totalTests, "Running {$testName}");
    
    $startTime = microtime(true);
    $testPassed = false;
    $message = '';
    $details = [];

    try {
        switch ($testKey) {
            case 'php_version':
                $requiredVersion = $config['required_php_version'];
                $currentVersion = PHP_VERSION;
                $testPassed = $testRunner->checkPhpVersion($requiredVersion);
                $message = $testPassed 
                    ? "PHP {$currentVersion} meets requirement (>= {$requiredVersion})"
                    : "PHP {$currentVersion} does not meet requirement (>= {$requiredVersion})";
                $details = [
                    'current_version' => $currentVersion,
                    'required_version' => $requiredVersion,
                    'php_sapi' => php_sapi_name(),
                    'php_os' => PHP_OS
                ];
                break;

            case 'php_extensions':
                $requiredExtensions = $config['required_extensions'];
                $missingExtensions = [];
                $loadedExtensions = [];
                
                foreach ($requiredExtensions as $extension) {
                    if ($testRunner->checkPhpExtension($extension)) {
                        $loadedExtensions[] = $extension;
                    } else {
                        $missingExtensions[] = $extension;
                    }
                }
                
                $testPassed = empty($missingExtensions);
                $message = $testPassed 
                    ? "All required extensions loaded (" . count($loadedExtensions) . "/" . count($requiredExtensions) . ")"
                    : "Missing extensions: " . implode(', ', $missingExtensions);
                $details = [
                    'required' => $requiredExtensions,
                    'loaded' => $loadedExtensions,
                    'missing' => $missingExtensions
                ];
                break;

            case 'codeigniter_framework':
                // Check if we're in a CodeIgniter environment
                $ciPath = __DIR__ . '/../../../backend/app/Config/App.php';
                $testPassed = file_exists($ciPath);
                
                if ($testPassed) {
                    // Try to get CI version
                    $ciVersion = 'Unknown';
                    if (defined('CodeIgniter\CodeIgniter::CI_VERSION')) {
                        $ciVersion = \CodeIgniter\CodeIgniter::CI_VERSION;
                    }
                    $message = "CodeIgniter framework detected (Version: {$ciVersion})";
                    $details = [
                        'version' => $ciVersion,
                        'config_path' => $ciPath,
                        'framework_detected' => true
                    ];
                } else {
                    $message = "CodeIgniter framework not detected";
                    $details = [
                        'config_path' => $ciPath,
                        'framework_detected' => false
                    ];
                }
                break;

            case 'file_permissions':
                $pathsToCheck = [
                    __DIR__ . '/../../../backend/writable' => 0755,
                    __DIR__ . '/../../../backend/writable/cache' => 0755,
                    __DIR__ . '/../../../backend/writable/logs' => 0755,
                    __DIR__ . '/../../../backend/writable/session' => 0755,
                    __DIR__ . '/../../../backend/writable/uploads' => 0755,
                ];
                
                $permissionIssues = [];
                $checkedPaths = [];
                
                foreach ($pathsToCheck as $path => $requiredPerm) {
                    $exists = file_exists($path);
                    $writable = is_writable($path);
                    $permissions = $exists ? substr(sprintf('%o', fileperms($path)), -4) : 'N/A';
                    
                    $checkedPaths[] = [
                        'path' => $path,
                        'exists' => $exists,
                        'writable' => $writable,
                        'permissions' => $permissions,
                        'required' => sprintf('%o', $requiredPerm)
                    ];
                    
                    if (!$exists || !$writable) {
                        $permissionIssues[] = basename($path);
                    }
                }
                
                $testPassed = empty($permissionIssues);
                $message = $testPassed 
                    ? "All required directories have proper permissions"
                    : "Permission issues with: " . implode(', ', $permissionIssues);
                $details = [
                    'checked_paths' => $checkedPaths,
                    'issues' => $permissionIssues
                ];
                break;

            case 'configuration_files':
                $configFiles = [
                    'App.php' => __DIR__ . '/../../../backend/app/Config/App.php',
                    'Database.php' => __DIR__ . '/../../../backend/app/Config/Database.php',
                    'Routes.php' => __DIR__ . '/../../../backend/app/Config/Routes.php',
                    '.env' => __DIR__ . '/../../../backend/.env'
                ];
                
                $missingFiles = [];
                $foundFiles = [];
                
                foreach ($configFiles as $name => $path) {
                    if (file_exists($path)) {
                        $foundFiles[] = $name;
                    } else {
                        $missingFiles[] = $name;
                    }
                }
                
                $testPassed = empty($missingFiles);
                $message = $testPassed 
                    ? "All configuration files found (" . count($foundFiles) . "/" . count($configFiles) . ")"
                    : "Missing configuration files: " . implode(', ', $missingFiles);
                $details = [
                    'required_files' => array_keys($configFiles),
                    'found_files' => $foundFiles,
                    'missing_files' => $missingFiles
                ];
                break;

            case 'environment_variables':
                $envFile = __DIR__ . '/../../../backend/.env';
                $envVars = [];
                $testPassed = false;
                
                if (file_exists($envFile)) {
                    $envContent = file_get_contents($envFile);
                    $lines = explode("\n", $envContent);
                    
                    foreach ($lines as $line) {
                        $line = trim($line);
                        if (!empty($line) && !str_starts_with($line, '#') && str_contains($line, '=')) {
                            list($key, $value) = explode('=', $line, 2);
                            $envVars[trim($key)] = trim($value);
                        }
                    }
                    
                    $requiredVars = ['CI_ENVIRONMENT', 'app.baseURL', 'database.default.hostname'];
                    $missingVars = [];
                    
                    foreach ($requiredVars as $var) {
                        if (!isset($envVars[$var]) || empty($envVars[$var])) {
                            $missingVars[] = $var;
                        }
                    }
                    
                    $testPassed = empty($missingVars);
                    $message = $testPassed 
                        ? "Environment variables configured (" . count($envVars) . " variables found)"
                        : "Missing required variables: " . implode(', ', $missingVars);
                    $details = [
                        'total_variables' => count($envVars),
                        'required_variables' => $requiredVars,
                        'missing_variables' => $missingVars,
                        'environment' => $envVars['CI_ENVIRONMENT'] ?? 'not set'
                    ];
                } else {
                    $message = "Environment file (.env) not found";
                    $details = ['env_file_path' => $envFile];
                }
                break;
        }

    } catch (Exception $e) {
        $testPassed = false;
        $message = "Exception: " . $e->getMessage();
        $details = [
            'exception' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ];
    }

    $duration = round((microtime(true) - $startTime) * 1000);
    $status = $testPassed ? 'PASS' : 'FAIL';
    
    $testRunner->logTestResult($testName, $status, $message, $duration, $details);
    
    if ($testPassed) {
        $passedTests++;
    }
}

// Summary for this test category
$testRunner->logSubSection('Environment Tests Summary');
$testRunner->log('info', "Passed: {$passedTests}/{$totalTests} tests", 'SUMMARY');

if ($passedTests === $totalTests) {
    $testRunner->logSuccess("All environment tests passed! System is ready for testing.");
    return true;
} else {
    $failedTests = $totalTests - $passedTests;
    $testRunner->logError("Environment tests failed! {$failedTests} test(s) need attention before proceeding.");
    return false;
}
