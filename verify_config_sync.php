<?php

/**
 * Configuration Synchronization Verification
 * 
 * This script ensures that testing configuration matches the backend .env file exactly
 */

echo "🔍 Verifying Configuration Synchronization...\n";
echo "=" . str_repeat("=", 50) . "\n\n";

// 1. Load backend .env configuration
echo "1. Loading backend .env configuration...\n";
$envFile = __DIR__ . '/backend/.env';
$envConfig = [];

if (!file_exists($envFile)) {
    echo "❌ ERROR: .env file not found at: $envFile\n";
    exit(1);
}

$envLines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
foreach ($envLines as $line) {
    $line = trim($line);
    if (!empty($line) && !str_starts_with($line, '#') && str_contains($line, '=')) {
        list($key, $value) = explode('=', $line, 2);
        $envConfig[trim($key)] = trim($value);
    }
}

echo "✅ Backend .env loaded successfully\n";

// 2. Load testing configuration
echo "\n2. Loading testing configuration...\n";
$testConfigFile = __DIR__ . '/testing_backend/config/test_config.php';

if (!file_exists($testConfigFile)) {
    echo "❌ ERROR: Test config file not found at: $testConfigFile\n";
    exit(1);
}

$testConfig = include $testConfigFile;
echo "✅ Testing config loaded successfully\n";

// 3. Compare database configurations
echo "\n3. Comparing database configurations...\n";
echo "-" . str_repeat("-", 40) . "\n";

$dbMappings = [
    'hostname' => 'database.default.hostname',
    'database' => 'database.default.database',
    'username' => 'database.default.username',
    'password' => 'database.default.password',
    'DBDriver' => 'database.default.DBDriver',
    'port' => 'database.default.port',
    'charset' => 'database.default.charset',
    'DBCollat' => 'database.default.DBCollat'
];

$allMatch = true;
$mismatches = [];

foreach ($dbMappings as $testKey => $envKey) {
    $envValue = $envConfig[$envKey] ?? '';
    $testValue = $testConfig['database'][$testKey] ?? '';
    
    // Convert port to string for comparison
    if ($testKey === 'port') {
        $testValue = (string)$testValue;
    }
    
    $match = $envValue === $testValue;
    $status = $match ? '✅' : '❌';
    
    echo sprintf("%-15s: %s ENV='%s' | TEST='%s'\n", 
        $testKey, $status, $envValue, $testValue);
    
    if (!$match) {
        $allMatch = false;
        $mismatches[] = [
            'key' => $testKey,
            'env_key' => $envKey,
            'env_value' => $envValue,
            'test_value' => $testValue
        ];
    }
}

// 4. Check API configuration
echo "\n4. Checking API configuration...\n";
echo "-" . str_repeat("-", 40) . "\n";

$expectedApiUrl = 'http://localhost:8080/api/v1';
$testApiUrl = $testConfig['api']['base_url'] ?? '';

$apiMatch = $expectedApiUrl === $testApiUrl;
echo sprintf("API Base URL    : %s Expected='%s' | Test='%s'\n", 
    $apiMatch ? '✅' : '❌', $expectedApiUrl, $testApiUrl);

if (!$apiMatch) {
    $allMatch = false;
    $mismatches[] = [
        'key' => 'api.base_url',
        'env_key' => 'expected',
        'env_value' => $expectedApiUrl,
        'test_value' => $testApiUrl
    ];
}

// 5. Summary and recommendations
echo "\n5. Configuration Summary\n";
echo "=" . str_repeat("=", 30) . "\n";

if ($allMatch) {
    echo "🎉 SUCCESS: All configurations are synchronized!\n";
    echo "\n✅ Database Configuration:\n";
    echo "   - Hostname: {$envConfig['database.default.hostname']}\n";
    echo "   - Database: {$envConfig['database.default.database']}\n";
    echo "   - Username: {$envConfig['database.default.username']}\n";
    echo "   - Driver:   {$envConfig['database.default.DBDriver']}\n";
    echo "   - Port:     {$envConfig['database.default.port']}\n";
    echo "   - Charset:  {$envConfig['database.default.charset']}\n";
    
    echo "\n✅ API Configuration:\n";
    echo "   - Base URL: {$testApiUrl}\n";
    
    echo "\n🚀 Ready to run tests with synchronized configuration!\n";
} else {
    echo "⚠️  WARNING: Configuration mismatches found!\n";
    echo "\n❌ Mismatches:\n";
    foreach ($mismatches as $mismatch) {
        echo "   - {$mismatch['key']}: ENV='{$mismatch['env_value']}' vs TEST='{$mismatch['test_value']}'\n";
    }
    
    echo "\n🔧 Recommendations:\n";
    echo "   1. Update testing_backend/config/test_config.php\n";
    echo "   2. Ensure all database values match backend/.env\n";
    echo "   3. Re-run this verification script\n";
}

// 6. Test database connection with current config
echo "\n6. Testing database connection...\n";
echo "-" . str_repeat("-", 40) . "\n";

try {
    $hostname = $envConfig['database.default.hostname'] ?? 'localhost';
    $database = $envConfig['database.default.database'] ?? 'rpswebid';
    $username = $envConfig['database.default.username'] ?? 'root';
    $password = $envConfig['database.default.password'] ?? '';
    $port = $envConfig['database.default.port'] ?? 3306;
    
    $mysqli = new mysqli($hostname, $username, $password, $database, $port);
    
    if ($mysqli->connect_error) {
        echo "❌ Database connection failed: " . $mysqli->connect_error . "\n";
    } else {
        echo "✅ Database connection successful!\n";
        
        // Get database info
        $result = $mysqli->query("SELECT DATABASE() as db, VERSION() as version, COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = DATABASE()");
        if ($result) {
            $info = $result->fetch_assoc();
            echo "   - Current DB: {$info['db']}\n";
            echo "   - MySQL Version: {$info['version']}\n";
            echo "   - Tables: {$info['table_count']}\n";
        }
        
        $mysqli->close();
    }
} catch (Exception $e) {
    echo "❌ Database connection error: " . $e->getMessage() . "\n";
}

// 7. Test backend server
echo "\n7. Testing backend server...\n";
echo "-" . str_repeat("-", 40) . "\n";

$backendUrl = 'http://localhost:8080';
$context = stream_context_create([
    'http' => [
        'timeout' => 5,
        'method' => 'GET'
    ]
]);

$response = @file_get_contents($backendUrl, false, $context);
if ($response !== false) {
    echo "✅ Backend server is running at: $backendUrl\n";
    echo "   - Response length: " . strlen($response) . " bytes\n";
} else {
    echo "❌ Backend server is not accessible at: $backendUrl\n";
    echo "   - Please start with: php spark serve --host=0.0.0.0 --port=8080\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "Configuration verification completed!\n";

exit($allMatch ? 0 : 1);
