# =====================================================
# CODEIGNITER 4 RPS MANAGEMENT SYSTEM .GITIGNORE
# =====================================================
# Customized for RPS Management System using CodeIgniter 4
# Includes all standard CI4 ignores plus RPS-specific rules
# =====================================================

# =====================================================
# CODEIGNITER 4 FRAMEWORK
# =====================================================

# System Files
/system/
!/system/index.html

# Writable Cache
/writable/cache/*
!/writable/cache/index.html
!/writable/cache/.htaccess

# Writable Logs
/writable/logs/*
!/writable/logs/index.html
!/writable/logs/.htaccess

# Writable Sessions
/writable/session/*
!/writable/session/index.html
!/writable/session/.htaccess

# Writable Uploads
/writable/uploads/*
!/writable/uploads/index.html
!/writable/uploads/.htaccess

# Debugbar
/writable/debugbar/*
!/writable/debugbar/.htaccess

# =====================================================
# RPS MANAGEMENT SPECIFIC
# =====================================================

# RPS Documents Storage
/writable/rps-documents/*
!/writable/rps-documents/.htaccess
!/writable/rps-documents/index.html

# Generated Reports
/writable/reports/*
!/writable/reports/.htaccess
!/writable/reports/index.html

# Export Files (Excel, PDF, etc.)
/writable/exports/*
!/writable/exports/.htaccess
!/writable/exports/index.html

# QR Code Generation
/public/qr-codes/*
!/public/qr-codes/.htaccess
!/public/qr-codes/index.html

# User Profile Pictures
/public/avatars/*
!/public/avatars/.htaccess
!/public/avatars/index.html

# Course Materials
/public/course-materials/*
!/public/course-materials/.htaccess
!/public/course-materials/index.html

# Assessment Files
/writable/assessments/*
!/writable/assessments/.htaccess
!/writable/assessments/index.html

# Email Templates (compiled)
/writable/email-templates/compiled/*
!/writable/email-templates/compiled/.htaccess

# Temporary Processing Files
/writable/processing/*
!/writable/processing/.htaccess
!/writable/processing/index.html

# Backup Files
/writable/backups/*
!/writable/backups/.htaccess
!/writable/backups/index.html

# =====================================================
# ENVIRONMENT & CONFIGURATION
# =====================================================

# Environment Files
.env
.env.local
.env.development
.env.testing
.env.production
.env.backup

# RPS Specific Configuration
/app/Config/RPS.local.php
/app/Config/Database.local.php
/app/Config/Email.local.php
/app/Config/JWT.local.php
/app/Config/OAuth.local.php
/app/Config/Payment.local.php
/app/Config/Storage.local.php
/app/Config/ApiKeys.php
/app/Config/Secrets.php

# =====================================================
# DEPENDENCIES & PACKAGES
# =====================================================

# Composer
/vendor/
composer.phar
composer.lock

# Node.js (for asset compilation)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# =====================================================
# DATABASE & STORAGE
# =====================================================

# SQLite Databases
*.sqlite
*.sqlite3
*.db
*.db3

# Database Dumps & Backups
*.sql
*.dump
*.backup
!app/Database/Migrations/*.sql
!app/Database/Seeds/*.sql

# =====================================================
# TESTING & DEVELOPMENT
# =====================================================

# PHPUnit
.phpunit.result.cache
.phpunit.cache
phpunit.xml
coverage/
.coverage

# CodeIgniter 4 Testing
/tests/_output/*
/tests/_support/_generated/*
/tests/writable/*

# Development Tools
/app/Views/errors/development/
spark

# =====================================================
# LOGS & CACHE
# =====================================================

# Application Logs
*.log
error.log
access.log
debug.log
ci.log
rps.log

# Cache Files
*.cache
/cache/
/tmp/
/temp/

# =====================================================
# SECURITY & SECRETS
# =====================================================

# SSL Certificates
*.pem
*.key
*.crt
*.csr
*.p12
*.pfx

# JWT & OAuth Keys
/writable/jwt/
jwt-private.key
jwt-public.key
oauth-private.key
oauth-public.key

# API Keys & Secrets
api-keys.txt
secrets.txt
credentials.json
service-account.json

# =====================================================
# ASSETS & BUILD
# =====================================================

# Compiled Assets
/public/assets/dist/
/public/css/compiled/
/public/js/compiled/
/public/build/

# Source Maps
*.map

# Webpack
webpack-stats.json
.webpack/

# =====================================================
# IDE & EDITORS
# =====================================================

# PhpStorm
.idea/
*.iml

# VS Code
.vscode/
*.code-workspace

# Sublime Text
*.sublime-project
*.sublime-workspace

# =====================================================
# OPERATING SYSTEM
# =====================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~
.directory

# =====================================================
# DEPLOYMENT & DOCKER
# =====================================================

# Docker
.dockerignore
docker-compose.override.yml
docker-compose.local.yml
docker-compose.prod.yml

# CI/CD
.github/workflows/local.yml
.gitlab-ci.local.yml

# Deployment Scripts
deploy.sh
deploy-local.sh
deploy-prod.sh

# =====================================================
# BACKUP & TEMPORARY
# =====================================================

# Backup Files
*.bak
*.backup
*.old
*.orig

# Temporary Files
*.tmp
*.temp
tmp/
temp/

# Archive Files
*.7z
*.zip
*.rar
*.tar.gz

# =====================================================
# RPS SPECIFIC FILE TYPES
# =====================================================

# Generated Documents (keep templates)
*.pdf
*.docx
*.xlsx
!templates/*.pdf
!templates/*.docx
!templates/*.xlsx

# QR Codes
*.png
*.jpg
*.jpeg
!assets/images/*.png
!assets/images/*.jpg
!assets/images/*.jpeg

# =====================================================
# END OF RPS MANAGEMENT CODEIGNITER 4 .GITIGNORE
# =====================================================
