<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateCoursesTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'code' => [
                'type'       => 'VARCHAR',
                'constraint' => 20,
                'null'       => false,
            ],
            'name' => [
                'type'       => 'VARCHAR',
                'constraint' => 255,
                'null'       => false,
            ],
            'study_program_id' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
                'null'       => false,
            ],
            'semester' => [
                'type'       => 'TINYINT',
                'constraint' => 3,
                'unsigned'   => true,
                'null'       => false,
            ],
            'credits' => [
                'type'       => 'TINYINT',
                'constraint' => 3,
                'unsigned'   => true,
                'null'       => false,
            ],
            'course_type' => [
                'type'       => 'ENUM',
                'constraint' => ['wajib', 'pilihan'],
                'null'       => false,
            ],
            'prerequisite_courses' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'JSON formatted array of course IDs',
            ],
            'description' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'learning_objectives' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'coordinator_id' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
                'null'       => true,
            ],
            'is_active' => [
                'type'       => 'TINYINT',
                'constraint' => 1,
                'default'    => 1,
            ],
            'created_by' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
                'null'       => true,
            ],
            'created_at' => [
                'type'    => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
            ],
            'updated_at' => [
                'type'    => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
                'on_update' => 'CURRENT_TIMESTAMP',
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addUniqueKey(['code', 'study_program_id']);
        $this->forge->addKey('study_program_id');
        $this->forge->addKey('semester');
        $this->forge->addKey('coordinator_id');
        $this->forge->addKey('course_type');
        $this->forge->addKey('is_active');
        $this->forge->addKey('created_by');
        
        $this->forge->addForeignKey('study_program_id', 'study_programs', 'id', 'RESTRICT', 'CASCADE');
        $this->forge->addForeignKey('coordinator_id', 'users', 'id', 'SET NULL', 'CASCADE');
        $this->forge->addForeignKey('created_by', 'users', 'id', 'SET NULL', 'CASCADE');
        
        $this->forge->createTable('courses');
    }

    public function down()
    {
        $this->forge->dropTable('courses');
    }
}
