# RPS Management System - Frontend Environment Configuration

# API Configuration
VITE_API_BASE_URL=http://localhost:8080/api/v1
VITE_API_TIMEOUT=30000

# Application Configuration
VITE_APP_NAME=RPS Management System
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=Sistem Manajemen Rencana Pembelajaran Semester
VITE_ORGANIZATION_NAME=Universitas Example
VITE_ORGANIZATION_LOGO=/logo.png

# Development Configuration
VITE_DEV_MODE=true
VITE_DEBUG_MODE=true
VITE_SHOW_CONSOLE_LOGS=true

# Authentication Configuration
VITE_JWT_STORAGE_KEY=rps_auth_token
VITE_JWT_REFRESH_KEY=rps_refresh_token
VITE_SESSION_TIMEOUT=3600000

# UI Configuration
VITE_DEFAULT_THEME=light
VITE_DEFAULT_LOCALE=id
VITE_ITEMS_PER_PAGE=20
VITE_MAX_FILE_SIZE=52428800

# Feature Flags
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_PWA=false
VITE_ENABLE_NOTIFICATIONS=true
VITE_ENABLE_DARK_MODE=true

# External Services
VITE_GOOGLE_ANALYTICS_ID=
VITE_SENTRY_DSN=
VITE_FIREBASE_CONFIG=

# Build Configuration
VITE_BUILD_TARGET=es2015
VITE_BUILD_SOURCEMAP=true
VITE_BUILD_MINIFY=true
