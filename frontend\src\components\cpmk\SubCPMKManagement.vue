<template>
  <div class="pa-6">
    <!-- Header -->
    <div class="d-flex align-center justify-space-between mb-4">
      <div>
        <h3 class="text-h6 font-weight-bold">{{ cpmk.code }} - Sub-CPMK</h3>
        <p class="text-subtitle-2 text-medium-emphasis">
          Manage detailed learning indicators for this CPMK
        </p>
      </div>
      
      <v-btn
        color="primary"
        @click="openCreateModal"
      >
        <v-icon start>mdi-plus</v-icon>
        Add Sub-CPMK
      </v-btn>
    </div>

    <!-- Sub-CPMK List -->
    <v-row>
      <v-col
        v-for="subCpmk in subCpmks"
        :key="subCpmk.id"
        cols="12"
      >
        <v-card variant="outlined" class="mb-3">
          <v-card-text>
            <div class="d-flex align-start justify-space-between">
              <div class="flex-grow-1">
                <div class="d-flex align-center mb-2">
                  <v-chip
                    color="primary"
                    size="small"
                    variant="tonal"
                    class="mr-2"
                  >
                    {{ subCpmk.code }}
                  </v-chip>
                  
                  <v-chip
                    v-if="subCpmk.weight_percentage"
                    size="small"
                    variant="outlined"
                  >
                    {{ subCpmk.weight_percentage }}%
                  </v-chip>
                </div>
                
                <h4 class="text-subtitle-1 font-weight-bold mb-1">
                  {{ subCpmk.description }}
                </h4>
                
                <p v-if="subCpmk.learning_indicator" class="text-body-2 text-medium-emphasis mb-2">
                  <v-icon size="small" class="mr-1">mdi-target</v-icon>
                  {{ subCpmk.learning_indicator }}
                </p>
                
                <div v-if="subCpmk.week_coverage && subCpmk.week_coverage.length > 0" class="mb-2">
                  <span class="text-caption text-medium-emphasis mr-2">Week Coverage:</span>
                  <v-chip
                    v-for="week in subCpmk.week_coverage"
                    :key="week"
                    size="x-small"
                    variant="outlined"
                    class="ma-1"
                  >
                    Week {{ week }}
                  </v-chip>
                </div>
              </div>
              
              <div class="d-flex flex-column gap-1 ml-4">
                <v-btn
                  icon
                  size="small"
                  variant="text"
                  @click="openEditModal(subCpmk)"
                >
                  <v-icon size="small">mdi-pencil</v-icon>
                  <v-tooltip activator="parent">Edit</v-tooltip>
                </v-btn>
                
                <v-btn
                  icon
                  size="small"
                  variant="text"
                  color="error"
                  @click="openDeleteDialog(subCpmk)"
                >
                  <v-icon size="small">mdi-delete</v-icon>
                  <v-tooltip activator="parent">Delete</v-tooltip>
                </v-btn>
              </div>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Empty State -->
    <div v-if="subCpmks.length === 0" class="text-center py-8">
      <v-icon size="64" color="grey-lighten-2">mdi-format-list-bulleted</v-icon>
      <p class="text-h6 text-medium-emphasis mt-4">No sub-CPMK defined yet</p>
      <v-btn
        color="primary"
        @click="openCreateModal"
        class="mt-2"
      >
        <v-icon start>mdi-plus</v-icon>
        Add First Sub-CPMK
      </v-btn>
    </div>

    <!-- Sub-CPMK Form Modal -->
    <FormModal
      v-model="showModal"
      :title="modalTitle"
      icon="mdi-format-list-bulleted"
      :mode="modalMode"
      :loading="modalLoading"
      max-width="700"
      @submit="handleSubmit"
      @close="closeModal"
    >
      <v-row>
        <v-col cols="12" md="6">
          <v-text-field
            v-model="formData.code"
            :rules="codeRules"
            label="Sub-CPMK Code *"
            variant="outlined"
            prepend-inner-icon="mdi-identifier"
            :disabled="modalLoading"
            placeholder="e.g., CPMK-01.1"
          />
        </v-col>
        
        <v-col cols="12" md="6">
          <v-text-field
            v-model="formData.weight_percentage"
            label="Weight Percentage"
            type="number"
            variant="outlined"
            prepend-inner-icon="mdi-scale-balance"
            suffix="%"
            :disabled="modalLoading"
            min="0"
            max="100"
          />
        </v-col>
        
        <v-col cols="12">
          <v-textarea
            v-model="formData.description"
            :rules="descriptionRules"
            label="Description *"
            variant="outlined"
            prepend-inner-icon="mdi-text"
            rows="3"
            :disabled="modalLoading"
          />
        </v-col>
        
        <v-col cols="12">
          <v-textarea
            v-model="formData.learning_indicator"
            :rules="indicatorRules"
            label="Learning Indicator *"
            variant="outlined"
            prepend-inner-icon="mdi-target"
            rows="3"
            :disabled="modalLoading"
          />
        </v-col>
        
        <v-col cols="12">
          <v-select
            v-model="formData.week_coverage"
            :items="weekOptions"
            label="Week Coverage"
            variant="outlined"
            prepend-inner-icon="mdi-calendar-week"
            multiple
            chips
            :disabled="modalLoading"
          />
        </v-col>
      </v-row>
    </FormModal>

    <!-- Delete Confirmation Dialog -->
    <v-dialog v-model="showDeleteDialog" max-width="400">
      <v-card>
        <v-card-title class="text-h6">Confirm Delete</v-card-title>
        <v-card-text>
          Are you sure you want to delete this sub-CPMK?
          This action cannot be undone.
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn @click="showDeleteDialog = false">Cancel</v-btn>
          <v-btn
            color="error"
            :loading="deleteLoading"
            @click="confirmDelete"
          >
            Delete
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Success/Error Snackbars -->
    <v-snackbar
      v-model="showSuccess"
      color="success"
      timeout="3000"
    >
      {{ successMessage }}
    </v-snackbar>

    <v-snackbar
      v-model="showError"
      color="error"
      timeout="5000"
    >
      {{ errorMessage }}
    </v-snackbar>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import FormModal from '@/components/common/FormModal.vue'
import { cpmkAPI } from '@/services/api'
import type { CPMK, SubCPMK } from '@/types/auth'

interface Props {
  cpmk: CPMK
}

const props = defineProps<Props>()
const emit = defineEmits<{
  close: []
}>()

// State
const loading = ref(false)
const modalLoading = ref(false)
const deleteLoading = ref(false)
const showModal = ref(false)
const showDeleteDialog = ref(false)
const showSuccess = ref(false)
const showError = ref(false)
const successMessage = ref('')
const errorMessage = ref('')

const subCpmks = ref<SubCPMK[]>([])
const selectedSubCpmk = ref<SubCPMK | null>(null)
const modalMode = ref<'create' | 'edit'>('create')

// Form data
const formData = reactive({
  code: '',
  description: '',
  learning_indicator: '',
  week_coverage: [] as number[],
  weight_percentage: null as number | null
})

// Computed
const modalTitle = computed(() => 
  modalMode.value === 'create' ? 'Add Sub-CPMK' : 'Edit Sub-CPMK'
)

const weekOptions = Array.from({ length: 16 }, (_, i) => ({
  title: `Week ${i + 1}`,
  value: i + 1
}))

// Validation rules
const codeRules = [
  (v: string) => !!v || 'Sub-CPMK code is required'
]

const descriptionRules = [
  (v: string) => !!v || 'Description is required'
]

const indicatorRules = [
  (v: string) => !!v || 'Learning indicator is required'
]

// Methods
const loadSubCpmks = async () => {
  loading.value = true
  try {
    const response = await cpmkAPI.getSubCPMK(props.cpmk.id)
    subCpmks.value = response.data.data
  } catch (error: any) {
    errorMessage.value = 'Failed to load sub-CPMKs'
    showError.value = true
  } finally {
    loading.value = false
  }
}

const openCreateModal = () => {
  modalMode.value = 'create'
  resetForm()
  showModal.value = true
}

const openEditModal = (subCpmk: SubCPMK) => {
  modalMode.value = 'edit'
  selectedSubCpmk.value = subCpmk
  populateForm(subCpmk)
  showModal.value = true
}

const openDeleteDialog = (subCpmk: SubCPMK) => {
  selectedSubCpmk.value = subCpmk
  showDeleteDialog.value = true
}

const resetForm = () => {
  Object.assign(formData, {
    code: '',
    description: '',
    learning_indicator: '',
    week_coverage: [],
    weight_percentage: null
  })
}

const populateForm = (subCpmk: SubCPMK) => {
  Object.assign(formData, {
    code: subCpmk.code,
    description: subCpmk.description,
    learning_indicator: subCpmk.learning_indicator,
    week_coverage: subCpmk.week_coverage || [],
    weight_percentage: subCpmk.weight_percentage
  })
}

const handleSubmit = async () => {
  modalLoading.value = true
  try {
    const data = {
      ...formData,
      cpmk_id: props.cpmk.id
    }
    
    if (modalMode.value === 'create') {
      await cpmkAPI.createSubCPMK(props.cpmk.id, data)
      successMessage.value = 'Sub-CPMK added successfully!'
    } else if (selectedSubCpmk.value) {
      await cpmkAPI.updateSubCPMK(props.cpmk.id, selectedSubCpmk.value.id, data)
      successMessage.value = 'Sub-CPMK updated successfully!'
    }
    
    showSuccess.value = true
    closeModal()
    await loadSubCpmks()
  } catch (error: any) {
    errorMessage.value = error.response?.data?.message || 'Operation failed'
    showError.value = true
  } finally {
    modalLoading.value = false
  }
}

const confirmDelete = async () => {
  if (!selectedSubCpmk.value) return
  
  deleteLoading.value = true
  try {
    await cpmkAPI.deleteSubCPMK(props.cpmk.id, selectedSubCpmk.value.id)
    successMessage.value = 'Sub-CPMK deleted successfully!'
    showSuccess.value = true
    showDeleteDialog.value = false
    await loadSubCpmks()
  } catch (error: any) {
    errorMessage.value = error.response?.data?.message || 'Delete failed'
    showError.value = true
  } finally {
    deleteLoading.value = false
  }
}

const closeModal = () => {
  showModal.value = false
  selectedSubCpmk.value = null
  resetForm()
}

// Lifecycle
onMounted(() => {
  loadSubCpmks()
})
</script>

<style scoped>
.v-card {
  transition: transform 0.2s ease-in-out;
}

.v-card:hover {
  transform: translateY(-1px);
}
</style>
