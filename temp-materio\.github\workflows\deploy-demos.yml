name: 🚀 Deploy Demo

on: [workflow_dispatch]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: ⚙️ Setup pnpm
        uses: pnpm/action-setup@v3
        with:
          version: 8

      - name: ⚙️ Set BRAND_NAME environment variable from repo name
        run: echo BRAND_NAME=${{ github.event.repository.name }} | cut -d '-' -f1 >> $GITHUB_ENV

      - name: ⬇️ Clone current repo under /<brand-name>/vue-free
        uses: actions/checkout@v4
        with:
          path: ${{ env.BRAND_NAME }}/vue-free

      - name: ⬇️ Clone automation scripts repo under /automation-scripts
        uses: actions/checkout@v4
        with:
          repository: themeselection/automation-scripts
          token: ${{ secrets.GH_PAT }}
          path: automation-scripts

      - name: ⬇️ Install packages in automation-scripts dir
        working-directory: automation-scripts/vue
        run: pnpm install

      - name: ⚙️ Prepare free demo
        working-directory: automation-scripts/vue
        run: pnpm tsx src/templates/${{ env.BRAND_NAME }}/scripts/prepareVueFreeDemo.ts

      - name: 📦 Generate demo
        working-directory: ${{ env.BRAND_NAME }}/vue-free/typescript-version
        run: pnpm i && pnpm build --base=/${{ env.BRAND_NAME }}-vuetify-vuejs-admin-template-free/demo/

      - name: 🚀 Upload demo
        uses: appleboy/scp-action@master
        with:
          HOST: ${{ secrets.HOST }}
          USERNAME: ${{ secrets.USERNAME }}
          PORT: ${{ secrets.PORT }}
          KEY: ${{ secrets.SSHKEY }}
          source: ${{ env.BRAND_NAME }}/vue-free/typescript-version/dist
          target: ${{ secrets.PROD_DIR }}/demo
          rm: true
          strip_components: 4
