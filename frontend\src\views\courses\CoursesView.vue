<template>
  <div>
    <!-- <PERSON> Header -->
    <v-row class="mb-6">
      <v-col>
        <h1 class="text-h4 font-weight-bold text-primary">Course Management</h1>
        <p class="text-subtitle-1 text-medium-emphasis">
          Manage courses, references, topics, and learning objectives
        </p>
      </v-col>
    </v-row>

    <!-- Statistics Cards -->
    <v-row class="mb-6">
      <v-col cols="12" sm="6" md="3">
        <v-card color="primary" variant="tonal">
          <v-card-text>
            <div class="d-flex align-center justify-space-between">
              <div>
                <p class="text-subtitle-2 text-medium-emphasis mb-1">Total Courses</p>
                <h2 class="text-h4 font-weight-bold">{{ totalCourses }}</h2>
              </div>
              <v-icon size="48" color="primary">mdi-book-open-page-variant</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card color="success" variant="tonal">
          <v-card-text>
            <div class="d-flex align-center justify-space-between">
              <div>
                <p class="text-subtitle-2 text-medium-emphasis mb-1">Active Courses</p>
                <h2 class="text-h4 font-weight-bold">{{ activeCourses }}</h2>
              </div>
              <v-icon size="48" color="success">mdi-check-circle</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card color="warning" variant="tonal">
          <v-card-text>
            <div class="d-flex align-center justify-space-between">
              <div>
                <p class="text-subtitle-2 text-medium-emphasis mb-1">Mandatory</p>
                <h2 class="text-h4 font-weight-bold">{{ mandatoryCourses }}</h2>
              </div>
              <v-icon size="48" color="warning">mdi-star</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card color="info" variant="tonal">
          <v-card-text>
            <div class="d-flex align-center justify-space-between">
              <div>
                <p class="text-subtitle-2 text-medium-emphasis mb-1">Elective</p>
                <h2 class="text-h4 font-weight-bold">{{ electiveCourses }}</h2>
              </div>
              <v-icon size="48" color="info">mdi-school</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Data Table -->
    <DataTable
      title="Course List"
      icon="mdi-book-open-page-variant"
      item-name="Course"
      :headers="tableHeaders"
      :items="courses"
      :loading="loading"
      :total-items="totalItems"
      :filters="tableFilters"
      @add="openCreateModal"
      @edit="openEditModal"
      @delete="openDeleteDialog"
      @view="openViewModal"
      @refresh="loadCourses"
      @search="handleSearch"
      @filter="handleFilter"
      @update:options="handleTableOptions"
    >
      <!-- Custom slots for specific columns -->
      <template #item.code="{ item }">
        <v-chip
          color="primary"
          variant="tonal"
          size="small"
          class="font-weight-bold"
        >
          {{ item.code }}
        </v-chip>
      </template>

      <template #item.course_type="{ item }">
        <v-chip
          :color="item.course_type === 'wajib' ? 'warning' : 'info'"
          variant="tonal"
          size="small"
        >
          {{ item.course_type === 'wajib' ? 'Mandatory' : 'Elective' }}
        </v-chip>
      </template>

      <template #item.credits="{ item }">
        <div class="d-flex align-center">
          <v-icon size="small" class="mr-1">mdi-star</v-icon>
          <span class="font-weight-medium">{{ item.credits }} SKS</span>
        </div>
      </template>

      <template #item.semester="{ item }">
        <v-chip
          color="secondary"
          variant="outlined"
          size="small"
        >
          Semester {{ item.semester }}
        </v-chip>
      </template>

      <template #item.coordinator="{ item }">
        <div v-if="item.coordinator_name" class="d-flex align-center">
          <v-avatar size="24" class="mr-2">
            <v-icon size="small">mdi-account</v-icon>
          </v-avatar>
          <span>{{ item.coordinator_name }}</span>
        </div>
        <span v-else class="text-medium-emphasis">Not assigned</span>
      </template>

      <template #item.prerequisites="{ item }">
        <div v-if="item.prerequisite_courses && item.prerequisite_courses.length > 0">
          <v-chip
            v-for="prereq in item.prerequisite_courses.slice(0, 2)"
            :key="prereq.id"
            size="x-small"
            variant="outlined"
            class="ma-1"
          >
            {{ prereq.code }}
          </v-chip>
          <v-chip
            v-if="item.prerequisite_courses.length > 2"
            size="x-small"
            variant="text"
            class="ma-1"
          >
            +{{ item.prerequisite_courses.length - 2 }} more
          </v-chip>
        </div>
        <span v-else class="text-medium-emphasis">None</span>
      </template>

      <template #item.actions="{ item }">
        <div class="d-flex align-center gap-1">
          <v-btn
            icon
            size="small"
            variant="text"
            @click="openViewModal(item)"
          >
            <v-icon size="small">mdi-eye</v-icon>
            <v-tooltip activator="parent">View Details</v-tooltip>
          </v-btn>

          <v-btn
            icon
            size="small"
            variant="text"
            @click="openEditModal(item)"
          >
            <v-icon size="small">mdi-pencil</v-icon>
            <v-tooltip activator="parent">Edit Course</v-tooltip>
          </v-btn>

          <v-btn
            icon
            size="small"
            variant="text"
            @click="openReferencesModal(item)"
          >
            <v-icon size="small">mdi-book-multiple</v-icon>
            <v-tooltip activator="parent">Manage References</v-tooltip>
          </v-btn>

          <v-btn
            icon
            size="small"
            variant="text"
            @click="openTopicsModal(item)"
          >
            <v-icon size="small">mdi-format-list-numbered</v-icon>
            <v-tooltip activator="parent">Manage Topics</v-tooltip>
          </v-btn>

          <v-btn
            icon
            size="small"
            variant="text"
            color="error"
            @click="openDeleteDialog(item)"
          >
            <v-icon size="small">mdi-delete</v-icon>
            <v-tooltip activator="parent">Delete Course</v-tooltip>
          </v-btn>
        </div>
      </template>
    </DataTable>

    <!-- Course Form Modal -->
    <FormModal
      v-model="showModal"
      :title="modalTitle"
      :icon="modalIcon"
      :mode="modalMode"
      :loading="modalLoading"
      max-width="900"
      @submit="handleSubmit"
      @close="closeModal"
    >
      <v-row>
        <v-col cols="12" md="6">
          <v-text-field
            v-model="formData.code"
            :rules="codeRules"
            label="Course Code *"
            variant="outlined"
            prepend-inner-icon="mdi-identifier"
            :disabled="modalLoading || modalMode === 'view'"
            placeholder="e.g., TIF101"
          />
        </v-col>

        <v-col cols="12" md="6">
          <v-text-field
            v-model="formData.name"
            :rules="nameRules"
            label="Course Name *"
            variant="outlined"
            prepend-inner-icon="mdi-book-open-page-variant"
            :disabled="modalLoading || modalMode === 'view'"
          />
        </v-col>

        <v-col cols="12" md="6">
          <v-select
            v-model="formData.study_program_id"
            :items="studyProgramOptions"
            :rules="studyProgramRules"
            label="Study Program *"
            variant="outlined"
            prepend-inner-icon="mdi-school"
            :disabled="modalLoading || modalMode === 'view'"
          />
        </v-col>

        <v-col cols="12" md="6">
          <v-select
            v-model="formData.course_type"
            :items="courseTypeOptions"
            :rules="courseTypeRules"
            label="Course Type *"
            variant="outlined"
            prepend-inner-icon="mdi-tag"
            :disabled="modalLoading || modalMode === 'view'"
          />
        </v-col>

        <v-col cols="12" md="4">
          <v-select
            v-model="formData.semester"
            :items="semesterOptions"
            :rules="semesterRules"
            label="Semester *"
            variant="outlined"
            prepend-inner-icon="mdi-calendar"
            :disabled="modalLoading || modalMode === 'view'"
          />
        </v-col>

        <v-col cols="12" md="4">
          <v-select
            v-model="formData.credits"
            :items="creditOptions"
            :rules="creditRules"
            label="Credits (SKS) *"
            variant="outlined"
            prepend-inner-icon="mdi-star"
            :disabled="modalLoading || modalMode === 'view'"
          />
        </v-col>

        <v-col cols="12" md="4">
          <v-select
            v-model="formData.coordinator_id"
            :items="coordinatorOptions"
            label="Course Coordinator"
            variant="outlined"
            prepend-inner-icon="mdi-account-tie"
            clearable
            :disabled="modalLoading || modalMode === 'view'"
          />
        </v-col>

        <v-col cols="12">
          <v-select
            v-model="formData.prerequisite_courses"
            :items="prerequisiteOptions"
            label="Prerequisite Courses"
            variant="outlined"
            prepend-inner-icon="mdi-arrow-left-circle"
            multiple
            chips
            clearable
            :disabled="modalLoading || modalMode === 'view'"
          />
        </v-col>

        <v-col cols="12">
          <v-textarea
            v-model="formData.description"
            label="Course Description"
            variant="outlined"
            prepend-inner-icon="mdi-text"
            rows="3"
            :disabled="modalLoading || modalMode === 'view'"
          />
        </v-col>

        <v-col cols="12">
          <v-textarea
            v-model="formData.learning_objectives"
            label="Learning Objectives"
            variant="outlined"
            prepend-inner-icon="mdi-target"
            rows="3"
            :disabled="modalLoading || modalMode === 'view'"
          />
        </v-col>

        <v-col cols="12" md="6">
          <v-switch
            v-model="formData.is_active"
            label="Active"
            color="primary"
            :disabled="modalLoading || modalMode === 'view'"
          />
        </v-col>
      </v-row>
    </FormModal>

    <!-- Course References Modal -->
    <v-dialog v-model="showReferencesModal" max-width="1000" scrollable>
      <v-card>
        <v-card-title class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <v-icon class="mr-2">mdi-book-multiple</v-icon>
            <span class="text-h6 font-weight-bold">Course References</span>
          </div>
          <v-btn icon variant="text" @click="showReferencesModal = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>

        <v-divider />

        <v-card-text class="pa-0">
          <CourseReferences
            v-if="selectedCourse"
            :course="selectedCourse"
            @close="showReferencesModal = false"
          />
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- Course Topics Modal -->
    <v-dialog v-model="showTopicsModal" max-width="1200" scrollable>
      <v-card>
        <v-card-title class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <v-icon class="mr-2">mdi-format-list-numbered</v-icon>
            <span class="text-h6 font-weight-bold">Course Topics</span>
          </div>
          <v-btn icon variant="text" @click="showTopicsModal = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>

        <v-divider />

        <v-card-text class="pa-0">
          <CourseTopics
            v-if="selectedCourse"
            :course="selectedCourse"
            @close="showTopicsModal = false"
          />
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- Delete Confirmation Dialog -->
    <v-dialog v-model="showDeleteDialog" max-width="400">
      <v-card>
        <v-card-title class="text-h6">Confirm Delete</v-card-title>
        <v-card-text>
          Are you sure you want to delete course "{{ selectedCourse?.name }}"?
          This action cannot be undone and will also delete all related references and topics.
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn @click="showDeleteDialog = false">Cancel</v-btn>
          <v-btn
            color="error"
            :loading="deleteLoading"
            @click="confirmDelete"
          >
            Delete
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Success/Error Snackbars -->
    <v-snackbar
      v-model="showSuccess"
      color="success"
      timeout="3000"
    >
      {{ successMessage }}
      <template v-slot:actions>
        <v-btn @click="showSuccess = false">Close</v-btn>
      </template>
    </v-snackbar>

    <v-snackbar
      v-model="showError"
      color="error"
      timeout="5000"
    >
      {{ errorMessage }}
      <template v-slot:actions>
        <v-btn @click="showError = false">Close</v-btn>
      </template>
    </v-snackbar>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import DataTable from '@/components/common/DataTable.vue'
import FormModal from '@/components/common/FormModal.vue'
import CourseReferences from '@/components/courses/CourseReferences.vue'
import CourseTopics from '@/components/courses/CourseTopics.vue'
import { coursesAPI, studyProgramsAPI, usersAPI } from '@/services/api'
import type { Course, StudyProgram, User, PaginationParams } from '@/types/auth'

// State
const loading = ref(false)
const modalLoading = ref(false)
const deleteLoading = ref(false)
const showModal = ref(false)
const showReferencesModal = ref(false)
const showTopicsModal = ref(false)
const showDeleteDialog = ref(false)
const showSuccess = ref(false)
const showError = ref(false)
const successMessage = ref('')
const errorMessage = ref('')

const courses = ref<Course[]>([])
const studyPrograms = ref<StudyProgram[]>([])
const coordinators = ref<User[]>([])
const totalItems = ref(0)
const selectedCourse = ref<Course | null>(null)
const modalMode = ref<'create' | 'edit' | 'view'>('create')

// Form data
const formData = reactive({
  code: '',
  name: '',
  study_program_id: null as number | null,
  semester: null as number | null,
  credits: null as number | null,
  course_type: 'wajib' as 'wajib' | 'pilihan',
  prerequisite_courses: [] as number[],
  description: '',
  learning_objectives: '',
  coordinator_id: null as number | null,
  is_active: true
})

// Pagination and filtering
const searchQuery = ref('')
const filters = ref<Record<string, any>>({})
const pagination = ref<PaginationParams>({
  page: 1,
  per_page: 20,
  sort_by: 'created_at',
  sort_order: 'desc'
})

// Statistics
const totalCourses = computed(() => courses.value.length)
const activeCourses = computed(() => courses.value.filter(c => c.is_active).length)
const mandatoryCourses = computed(() => courses.value.filter(c => c.course_type === 'wajib').length)
const electiveCourses = computed(() => courses.value.filter(c => c.course_type === 'pilihan').length)

// Table configuration
const tableHeaders = [
  { key: 'code', title: 'Code', sortable: true, type: 'text' as const },
  { key: 'name', title: 'Course Name', sortable: true, type: 'text' as const },
  { key: 'course_type', title: 'Type', sortable: true, type: 'text' as const },
  { key: 'semester', title: 'Semester', sortable: true, type: 'text' as const },
  { key: 'credits', title: 'Credits', sortable: true, type: 'text' as const },
  { key: 'coordinator', title: 'Coordinator', sortable: false, type: 'text' as const },
  { key: 'prerequisites', title: 'Prerequisites', sortable: false, type: 'text' as const },
  { key: 'is_active', title: 'Status', sortable: true, type: 'boolean' as const }
]

const tableFilters = [
  {
    key: 'course_type',
    label: 'Course Type',
    options: [
      { title: 'Mandatory', value: 'wajib' },
      { title: 'Elective', value: 'pilihan' }
    ]
  },
  {
    key: 'semester',
    label: 'Semester',
    options: Array.from({ length: 8 }, (_, i) => ({
      title: `Semester ${i + 1}`,
      value: i + 1
    }))
  },
  {
    key: 'is_active',
    label: 'Status',
    options: [
      { title: 'Active', value: true },
      { title: 'Inactive', value: false }
    ]
  }
]

// Computed options
const modalTitle = computed(() => {
  switch (modalMode.value) {
    case 'create':
      return 'Create New Course'
    case 'edit':
      return 'Edit Course'
    case 'view':
      return 'View Course Details'
    default:
      return 'Course Form'
  }
})

const modalIcon = computed(() => {
  switch (modalMode.value) {
    case 'create':
      return 'mdi-book-plus'
    case 'edit':
      return 'mdi-book-edit'
    case 'view':
      return 'mdi-book-open-page-variant'
    default:
      return 'mdi-book'
  }
})

const studyProgramOptions = computed(() =>
  studyPrograms.value.map(sp => ({
    title: `${sp.name} (${sp.code})`,
    value: sp.id
  }))
)

const coordinatorOptions = computed(() =>
  coordinators.value.map(user => ({
    title: user.full_name,
    value: user.id
  }))
)

const prerequisiteOptions = computed(() => {
  if (!formData.study_program_id || !formData.semester) return []

  return courses.value
    .filter(course =>
      course.study_program_id === formData.study_program_id &&
      course.semester < (formData.semester || 0) &&
      course.is_active
    )
    .map(course => ({
      title: `${course.code} - ${course.name}`,
      value: course.id
    }))
})

const courseTypeOptions = [
  { title: 'Mandatory (Wajib)', value: 'wajib' },
  { title: 'Elective (Pilihan)', value: 'pilihan' }
]

const semesterOptions = Array.from({ length: 8 }, (_, i) => ({
  title: `Semester ${i + 1}`,
  value: i + 1
}))

const creditOptions = Array.from({ length: 6 }, (_, i) => ({
  title: `${i + 1} SKS`,
  value: i + 1
}))

// Validation rules
const codeRules = [
  (v: string) => !!v || 'Course code is required',
  (v: string) => v.length >= 3 || 'Course code must be at least 3 characters',
  (v: string) => /^[A-Z]{2,3}\d{3}$/.test(v) || 'Course code format: ABC123 (2-3 letters + 3 digits)'
]

const nameRules = [
  (v: string) => !!v || 'Course name is required',
  (v: string) => v.length >= 3 || 'Course name must be at least 3 characters'
]

const studyProgramRules = [
  (v: number) => !!v || 'Study program is required'
]

const courseTypeRules = [
  (v: string) => !!v || 'Course type is required'
]

const semesterRules = [
  (v: number) => !!v || 'Semester is required',
  (v: number) => v >= 1 && v <= 8 || 'Semester must be between 1 and 8'
]

const creditRules = [
  (v: number) => !!v || 'Credits is required',
  (v: number) => v >= 1 && v <= 6 || 'Credits must be between 1 and 6'
]

// Methods
const loadCourses = async () => {
  loading.value = true
  try {
    const params = {
      ...pagination.value,
      search: searchQuery.value,
      ...filters.value
    }

    const response = await coursesAPI.getAll(params)
    courses.value = response.data.data
    totalItems.value = response.data.meta?.total || 0
  } catch (error: any) {
    errorMessage.value = 'Failed to load courses'
    showError.value = true
    console.error('Load courses error:', error)
  } finally {
    loading.value = false
  }
}

const loadStudyPrograms = async () => {
  try {
    const response = await studyProgramsAPI.getAll({ per_page: 100 })
    studyPrograms.value = response.data.data
  } catch (error) {
    console.error('Load study programs error:', error)
  }
}

const loadCoordinators = async () => {
  try {
    const response = await usersAPI.getAll({
      per_page: 100,
      role: 'dosen'
    })
    coordinators.value = response.data.data
  } catch (error) {
    console.error('Load coordinators error:', error)
  }
}

const openCreateModal = () => {
  modalMode.value = 'create'
  resetForm()
  showModal.value = true
}

const openEditModal = (course: Course) => {
  modalMode.value = 'edit'
  selectedCourse.value = course
  populateForm(course)
  showModal.value = true
}

const openViewModal = (course: Course) => {
  modalMode.value = 'view'
  selectedCourse.value = course
  populateForm(course)
  showModal.value = true
}

const openReferencesModal = (course: Course) => {
  selectedCourse.value = course
  showReferencesModal.value = true
}

const openTopicsModal = (course: Course) => {
  selectedCourse.value = course
  showTopicsModal.value = true
}

const openDeleteDialog = (course: Course) => {
  selectedCourse.value = course
  showDeleteDialog.value = true
}

const resetForm = () => {
  Object.assign(formData, {
    code: '',
    name: '',
    study_program_id: null,
    semester: null,
    credits: null,
    course_type: 'wajib',
    prerequisite_courses: [],
    description: '',
    learning_objectives: '',
    coordinator_id: null,
    is_active: true
  })
}

const populateForm = (course: Course) => {
  Object.assign(formData, {
    code: course.code,
    name: course.name,
    study_program_id: course.study_program_id,
    semester: course.semester,
    credits: course.credits,
    course_type: course.course_type,
    prerequisite_courses: course.prerequisite_courses || [],
    description: course.description || '',
    learning_objectives: course.learning_objectives || '',
    coordinator_id: course.coordinator_id,
    is_active: course.is_active
  })
}

const handleSubmit = async () => {
  modalLoading.value = true
  try {
    if (modalMode.value === 'create') {
      await coursesAPI.create(formData)
      successMessage.value = 'Course created successfully!'
    } else if (modalMode.value === 'edit' && selectedCourse.value) {
      await coursesAPI.update(selectedCourse.value.id, formData)
      successMessage.value = 'Course updated successfully!'
    }

    showSuccess.value = true
    closeModal()
    await loadCourses()
  } catch (error: any) {
    errorMessage.value = error.response?.data?.message || 'Operation failed'
    showError.value = true
  } finally {
    modalLoading.value = false
  }
}

const confirmDelete = async () => {
  if (!selectedCourse.value) return

  deleteLoading.value = true
  try {
    await coursesAPI.delete(selectedCourse.value.id)
    successMessage.value = 'Course deleted successfully!'
    showSuccess.value = true
    showDeleteDialog.value = false
    await loadCourses()
  } catch (error: any) {
    errorMessage.value = error.response?.data?.message || 'Delete failed'
    showError.value = true
  } finally {
    deleteLoading.value = false
  }
}

const closeModal = () => {
  showModal.value = false
  selectedCourse.value = null
  resetForm()
}

const handleSearch = (query: string) => {
  searchQuery.value = query
  pagination.value.page = 1
  loadCourses()
}

const handleFilter = (filterValues: Record<string, any>) => {
  filters.value = filterValues
  pagination.value.page = 1
  loadCourses()
}

const handleTableOptions = (options: any) => {
  pagination.value = {
    ...pagination.value,
    page: options.page,
    per_page: options.itemsPerPage,
    sort_by: options.sortBy?.[0]?.key || 'created_at',
    sort_order: options.sortBy?.[0]?.order || 'desc'
  }
  loadCourses()
}

// Watch for study program or semester changes to update prerequisites
watch([() => formData.study_program_id, () => formData.semester], () => {
  formData.prerequisite_courses = []
})

// Lifecycle
onMounted(async () => {
  await Promise.all([
    loadCourses(),
    loadStudyPrograms(),
    loadCoordinators()
  ])
})
</script>
