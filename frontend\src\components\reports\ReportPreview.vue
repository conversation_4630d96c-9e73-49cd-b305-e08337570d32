<template>
  <div>
    <div class="text-center py-8">
      <v-icon size="48" color="grey-lighten-2">mdi-eye</v-icon>
      <p class="text-h6 text-medium-emphasis mt-4">Report Preview</p>
      <p class="text-body-2 text-medium-emphasis">
        Live preview of {{ report.name }} with sample data and formatting
      </p>
      <p class="text-caption text-medium-emphasis mt-2">
        This feature will be fully implemented in the next development phase
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  report: any
}

defineProps<Props>()
</script>
