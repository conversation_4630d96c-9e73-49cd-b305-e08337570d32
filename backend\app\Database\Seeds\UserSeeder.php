<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class UserSeeder extends Seeder
{
    public function run()
    {
        // Get role IDs
        $roles = $this->db->table('roles')->get()->getResultArray();
        $roleMap = [];
        foreach ($roles as $role) {
            $roleMap[$role['name']] = $role['id'];
        }

        $data = [
            [
                'username' => 'superadmin',
                'email' => '<EMAIL>',
                'password_hash' => password_hash('admin123', PASSWORD_DEFAULT),
                'full_name' => 'Super Administrator',
                'nip' => '199001010001',
                'role_id' => $roleMap['Super Admin'],
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'username' => 'admin.teknik',
                'email' => '<EMAIL>',
                'password_hash' => password_hash('admin123', PASSWORD_DEFAULT),
                'full_name' => '<PERSON><PERSON>, M.T.',
                'nip' => '198505150001',
                'role_id' => $roleMap['Admin Fakultas'],
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'username' => 'kaprodi.ti',
                'email' => '<EMAIL>',
                'password_hash' => password_hash('kaprodi123', PASSWORD_DEFAULT),
                'full_name' => 'Dr. Budi Santoso, M.Kom.',
                'nip' => '198203120002',
                'role_id' => $roleMap['Koordinator Prodi'],
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'username' => 'dosen.pemrograman',
                'email' => '<EMAIL>',
                'password_hash' => password_hash('dosen123', PASSWORD_DEFAULT),
                'full_name' => 'Sari Wijayanti, M.T.',
                'nip' => '198907250003',
                'role_id' => $roleMap['Dosen'],
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'username' => 'dosen.database',
                'email' => '<EMAIL>',
                'password_hash' => password_hash('dosen123', PASSWORD_DEFAULT),
                'full_name' => 'Andi Kurniawan, M.Kom.',
                'nip' => '199102180004',
                'role_id' => $roleMap['Dosen'],
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'username' => 'dosen.jaringan',
                'email' => '<EMAIL>',
                'password_hash' => password_hash('dosen123', PASSWORD_DEFAULT),
                'full_name' => 'Rina Maharani, M.T.',
                'nip' => '198812050005',
                'role_id' => $roleMap['Dosen'],
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'username' => 'staff.akademik',
                'email' => '<EMAIL>',
                'password_hash' => password_hash('staff123', PASSWORD_DEFAULT),
                'full_name' => 'Dewi Sartika, S.Kom.',
                'nip' => '199506300006',
                'role_id' => $roleMap['Admin Fakultas'],
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'username' => 'mahasiswa.demo',
                'email' => '<EMAIL>',
                'password_hash' => password_hash('mahasiswa123', PASSWORD_DEFAULT),
                'full_name' => 'Muhammad Rizki Pratama',
                'nip' => '20210001',
                'role_id' => $roleMap['Mahasiswa'],
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ];

        // Insert data
        $this->db->table('users')->insertBatch($data);
        
        echo "Users seeded successfully!" . PHP_EOL;
    }
}
