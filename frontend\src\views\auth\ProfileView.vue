<template>
  <div>
    <!-- <PERSON> Header -->
    <v-row class="mb-6">
      <v-col>
        <h1 class="text-h4 font-weight-bold text-primary">User Profile</h1>
        <p class="text-subtitle-1 text-medium-emphasis">
          Manage your account settings and preferences
        </p>
      </v-col>
    </v-row>

    <v-row>
      <!-- Profile Information -->
      <v-col cols="12" md="8">
        <v-card class="mb-6">
          <v-card-title class="text-h6 font-weight-bold">
            <v-icon start>mdi-account-circle</v-icon>
            Profile Information
          </v-card-title>
          
          <v-card-text>
            <v-form ref="profileForm" v-model="profileValid" @submit.prevent="updateProfile">
              <v-row>
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="profileData.full_name"
                    :rules="nameRules"
                    label="Full Name"
                    variant="outlined"
                    prepend-inner-icon="mdi-account"
                    :disabled="profileLoading"
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="profileData.username"
                    :rules="usernameRules"
                    label="Username"
                    variant="outlined"
                    prepend-inner-icon="mdi-at"
                    :disabled="profileLoading"
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="profileData.email"
                    :rules="emailRules"
                    label="Email"
                    type="email"
                    variant="outlined"
                    prepend-inner-icon="mdi-email"
                    :disabled="profileLoading"
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="profileData.phone"
                    label="Phone Number"
                    variant="outlined"
                    prepend-inner-icon="mdi-phone"
                    :disabled="profileLoading"
                  />
                </v-col>
              </v-row>
              
              <v-row>
                <v-col>
                  <v-btn
                    type="submit"
                    color="primary"
                    :loading="profileLoading"
                    :disabled="!profileValid"
                  >
                    <v-icon start>mdi-content-save</v-icon>
                    Update Profile
                  </v-btn>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>

        <!-- Change Password -->
        <v-card>
          <v-card-title class="text-h6 font-weight-bold">
            <v-icon start>mdi-lock</v-icon>
            Change Password
          </v-card-title>
          
          <v-card-text>
            <v-form ref="passwordForm" v-model="passwordValid" @submit.prevent="changePassword">
              <v-row>
                <v-col cols="12">
                  <v-text-field
                    v-model="passwordData.current_password"
                    :rules="currentPasswordRules"
                    :type="showCurrentPassword ? 'text' : 'password'"
                    label="Current Password"
                    variant="outlined"
                    prepend-inner-icon="mdi-lock"
                    :append-inner-icon="showCurrentPassword ? 'mdi-eye' : 'mdi-eye-off'"
                    @click:append-inner="showCurrentPassword = !showCurrentPassword"
                    :disabled="passwordLoading"
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="passwordData.new_password"
                    :rules="newPasswordRules"
                    :type="showNewPassword ? 'text' : 'password'"
                    label="New Password"
                    variant="outlined"
                    prepend-inner-icon="mdi-lock-plus"
                    :append-inner-icon="showNewPassword ? 'mdi-eye' : 'mdi-eye-off'"
                    @click:append-inner="showNewPassword = !showNewPassword"
                    :disabled="passwordLoading"
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="passwordData.confirm_password"
                    :rules="confirmPasswordRules"
                    :type="showConfirmPassword ? 'text' : 'password'"
                    label="Confirm New Password"
                    variant="outlined"
                    prepend-inner-icon="mdi-lock-check"
                    :append-inner-icon="showConfirmPassword ? 'mdi-eye' : 'mdi-eye-off'"
                    @click:append-inner="showConfirmPassword = !showConfirmPassword"
                    :disabled="passwordLoading"
                  />
                </v-col>
              </v-row>
              
              <v-row>
                <v-col>
                  <v-btn
                    type="submit"
                    color="warning"
                    :loading="passwordLoading"
                    :disabled="!passwordValid"
                  >
                    <v-icon start>mdi-key-change</v-icon>
                    Change Password
                  </v-btn>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- Profile Summary -->
      <v-col cols="12" md="4">
        <v-card>
          <v-card-text class="text-center">
            <v-avatar size="120" class="mb-4">
              <v-img
                v-if="authStore.user?.avatar"
                :src="authStore.user.avatar"
                :alt="authStore.user?.full_name"
              />
              <v-icon v-else size="60">mdi-account</v-icon>
            </v-avatar>
            
            <h3 class="text-h6 font-weight-bold mb-2">
              {{ authStore.user?.full_name }}
            </h3>
            
            <p class="text-subtitle-2 text-medium-emphasis mb-4">
              {{ authStore.user?.email }}
            </p>
            
            <v-chip
              v-for="role in authStore.user?.roles"
              :key="role"
              class="ma-1"
              color="primary"
              variant="tonal"
              size="small"
            >
              {{ formatRole(role) }}
            </v-chip>
          </v-card-text>
          
          <v-divider />
          
          <v-card-text>
            <v-list density="compact">
              <v-list-item>
                <v-list-item-prepend>
                  <v-icon>mdi-calendar</v-icon>
                </v-list-item-prepend>
                <v-list-item-title>Member Since</v-list-item-title>
                <v-list-item-subtitle>
                  {{ formatDate(authStore.user?.created_at) }}
                </v-list-item-subtitle>
              </v-list-item>
              
              <v-list-item>
                <v-list-item-prepend>
                  <v-icon>mdi-clock</v-icon>
                </v-list-item-prepend>
                <v-list-item-title>Last Login</v-list-item-title>
                <v-list-item-subtitle>
                  {{ formatDate(authStore.user?.last_login) }}
                </v-list-item-subtitle>
              </v-list-item>
              
              <v-list-item>
                <v-list-item-prepend>
                  <v-icon>mdi-shield-check</v-icon>
                </v-list-item-prepend>
                <v-list-item-title>Account Status</v-list-item-title>
                <v-list-item-subtitle>
                  <v-chip
                    :color="authStore.user?.is_active ? 'success' : 'error'"
                    size="x-small"
                    variant="tonal"
                  >
                    {{ authStore.user?.is_active ? 'Active' : 'Inactive' }}
                  </v-chip>
                </v-list-item-subtitle>
              </v-list-item>
            </v-list>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Success/Error Snackbars -->
    <v-snackbar
      v-model="showSuccess"
      color="success"
      timeout="3000"
    >
      {{ successMessage }}
      <template v-slot:actions>
        <v-btn @click="showSuccess = false">Close</v-btn>
      </template>
    </v-snackbar>

    <v-snackbar
      v-model="showError"
      color="error"
      timeout="5000"
    >
      {{ errorMessage }}
      <template v-slot:actions>
        <v-btn @click="showError = false">Close</v-btn>
      </template>
    </v-snackbar>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

// State
const profileForm = ref()
const passwordForm = ref()
const profileValid = ref(false)
const passwordValid = ref(false)
const profileLoading = ref(false)
const passwordLoading = ref(false)
const showCurrentPassword = ref(false)
const showNewPassword = ref(false)
const showConfirmPassword = ref(false)
const showSuccess = ref(false)
const showError = ref(false)
const successMessage = ref('')
const errorMessage = ref('')

// Profile data
const profileData = reactive({
  full_name: '',
  username: '',
  email: '',
  phone: ''
})

// Password data
const passwordData = reactive({
  current_password: '',
  new_password: '',
  confirm_password: ''
})

// Validation rules
const nameRules = [
  (v: string) => !!v || 'Full name is required',
  (v: string) => v.length >= 2 || 'Name must be at least 2 characters'
]

const usernameRules = [
  (v: string) => !!v || 'Username is required',
  (v: string) => v.length >= 3 || 'Username must be at least 3 characters'
]

const emailRules = [
  (v: string) => !!v || 'Email is required',
  (v: string) => /.+@.+\..+/.test(v) || 'Email must be valid'
]

const currentPasswordRules = [
  (v: string) => !!v || 'Current password is required'
]

const newPasswordRules = [
  (v: string) => !!v || 'New password is required',
  (v: string) => v.length >= 6 || 'Password must be at least 6 characters'
]

const confirmPasswordRules = [
  (v: string) => !!v || 'Please confirm your password',
  (v: string) => v === passwordData.new_password || 'Passwords do not match'
]

// Methods
const updateProfile = async () => {
  if (!profileValid.value) return

  profileLoading.value = true
  try {
    await authStore.updateProfile(profileData)
    successMessage.value = 'Profile updated successfully!'
    showSuccess.value = true
  } catch (error: any) {
    errorMessage.value = error.response?.data?.message || 'Failed to update profile'
    showError.value = true
  } finally {
    profileLoading.value = false
  }
}

const changePassword = async () => {
  if (!passwordValid.value) return

  passwordLoading.value = true
  try {
    await authStore.changePassword(passwordData)
    successMessage.value = 'Password changed successfully!'
    showSuccess.value = true
    
    // Reset form
    passwordData.current_password = ''
    passwordData.new_password = ''
    passwordData.confirm_password = ''
    passwordForm.value?.reset()
  } catch (error: any) {
    errorMessage.value = error.response?.data?.message || 'Failed to change password'
    showError.value = true
  } finally {
    passwordLoading.value = false
  }
}

const formatRole = (role: string) => {
  const roleMap: Record<string, string> = {
    admin: 'Administrator',
    dekan: 'Dekan',
    wakil_dekan: 'Wakil Dekan',
    kepala_prodi: 'Kepala Program Studi',
    sekretaris_prodi: 'Sekretaris Program Studi',
    dosen: 'Dosen'
  }
  return roleMap[role] || role
}

const formatDate = (dateString?: string) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// Lifecycle
onMounted(() => {
  if (authStore.user) {
    profileData.full_name = authStore.user.full_name
    profileData.username = authStore.user.username
    profileData.email = authStore.user.email
    profileData.phone = authStore.user.phone || ''
  }
})
</script>

<style scoped>
.v-avatar {
  border: 3px solid rgb(var(--v-theme-primary));
}

.v-chip {
  text-transform: capitalize;
}
</style>
