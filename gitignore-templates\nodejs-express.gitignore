# =====================================================
# NODE.JS EXPRESS .GITIGNORE TEMPLATE
# =====================================================
# Optimized for Express.js backend development
# =====================================================

# =====================================================
# NODE.JS SPECIFIC
# =====================================================

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# =====================================================
# EXPRESS.JS SPECIFIC
# =====================================================

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production
.env.staging

# Session store
sessions/
*.sess

# Uploads
uploads/
public/uploads/
static/uploads/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
access.log
error.log
combined.log

# =====================================================
# BUILD & DIST
# =====================================================

# Build outputs
dist/
build/
out/

# Webpack
.webpack/

# Parcel
.cache/
.parcel-cache/

# Next.js
.next/

# Nuxt.js
.nuxt/

# Gatsby
.cache/
public/

# Storybook
.out/
.storybook-out/

# =====================================================
# TYPESCRIPT
# =====================================================

# TypeScript
*.tsbuildinfo
lib/
types/

# =====================================================
# TESTING
# =====================================================

# Jest
coverage/
.nyc_output/
junit.xml

# Mocha
test-results.xml

# =====================================================
# DATABASE
# =====================================================

# SQLite
*.sqlite
*.sqlite3
*.db

# MongoDB
*.bson

# Redis
dump.rdb

# =====================================================
# CACHE & TEMPORARY
# =====================================================

# Cache
.cache/
*.cache
tmp/
temp/

# PM2
.pm2/

# =====================================================
# SECURITY
# =====================================================

# SSL Certificates
*.pem
*.key
*.crt
*.csr
*.p12
*.pfx

# API Keys
config/keys.js
config/secrets.js
secrets/

# =====================================================
# DEVELOPMENT TOOLS
# =====================================================

# Nodemon
nodemon.json

# ESLint
.eslintcache

# Prettier
.prettierignore

# VS Code
.vscode/

# WebStorm
.idea/

# =====================================================
# DEPLOYMENT
# =====================================================

# Docker
docker-compose.override.yml
.dockerignore

# Kubernetes
*.kubeconfig

# Heroku
.env.heroku

# Vercel
.vercel

# Netlify
.netlify/

# =====================================================
# MONITORING
# =====================================================

# New Relic
newrelic.js
newrelic_agent.log

# =====================================================
# PACKAGE MANAGERS
# =====================================================

# NPM
package-lock.json
.npm/

# Yarn
yarn.lock
.yarn/
.yarnrc.yml

# PNPM
pnpm-lock.yaml
.pnpm-store/

# =====================================================
# OPERATING SYSTEM
# =====================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~

# =====================================================
# MISC
# =====================================================

# Backup files
*.bak
*.backup
*.old
*.orig

# Archive files
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# =====================================================
# END OF NODE.JS EXPRESS .GITIGNORE
# =====================================================
