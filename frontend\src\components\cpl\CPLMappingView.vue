<template>
  <div class="pa-6">
    <div class="text-center py-8">
      <v-icon size="64" color="grey-lighten-2">mdi-vector-link</v-icon>
      <p class="text-h6 text-medium-emphasis mt-4">CPL Mapping View</p>
      <p class="text-body-2 text-medium-emphasis">
        View CPMK mappings for {{ cpl.code }}
      </p>
      <p class="text-caption text-medium-emphasis mt-2">
        This feature will be fully implemented in the next development phase
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { CPL } from '@/types/auth'

interface Props {
  cpl: CPL
}

defineProps<Props>()
defineEmits<{
  close: []
  updated: []
}>()
</script>
