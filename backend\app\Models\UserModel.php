<?php

namespace App\Models;

use CodeIgniter\Model;

class UserModel extends Model
{
    protected $table = 'users';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;

    protected $allowedFields = [
        'username',
        'email',
        'password_hash',
        'full_name',
        'nip',
        'role_id',
        'is_active',
        'last_login'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = null; // No soft deletes in current schema

    protected $validationRules = [
        'username' => 'required|min_length[3]|max_length[100]|is_unique[users.username,id,{id}]',
        'email' => 'required|valid_email|is_unique[users.email,id,{id}]',
        'password_hash' => 'required|min_length[6]',
        'full_name' => 'required|min_length[2]|max_length[255]',
        'role_id' => 'required|integer',
        'is_active' => 'in_list[0,1]'
    ];

    protected $validationMessages = [
        'username' => [
            'required' => 'Username is required',
            'min_length' => 'Username must be at least 3 characters',
            'max_length' => 'Username cannot exceed 50 characters',
            'is_unique' => 'Username already exists'
        ],
        'email' => [
            'required' => 'Email is required',
            'valid_email' => 'Please provide a valid email address',
            'is_unique' => 'Email already exists'
        ],
        'password' => [
            'required' => 'Password is required',
            'min_length' => 'Password must be at least 6 characters'
        ],
        'full_name' => [
            'required' => 'Full name is required',
            'min_length' => 'Full name must be at least 2 characters',
            'max_length' => 'Full name cannot exceed 100 characters'
        ],
        'role' => [
            'required' => 'Role is required',
            'in_list' => 'Invalid role selected'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    protected $allowCallbacks = true;
    protected $beforeInsert = ['hashPassword'];
    protected $beforeUpdate = ['hashPassword'];

    /**
     * Hash password before saving
     */
    protected function hashPassword(array $data)
    {
        if (isset($data['data']['password_hash'])) {
            $data['data']['password_hash'] = password_hash($data['data']['password_hash'], PASSWORD_DEFAULT);
        }

        return $data;
    }

    /**
     * Get users with their faculty and study program information
     */
    public function getUsersWithRelations($limit = null, $offset = null)
    {
        $builder = $this->db->table($this->table);
        $builder->select('users.*, faculties.name as faculty_name, study_programs.name as study_program_name');
        $builder->join('faculties', 'faculties.id = users.faculty_id', 'left');
        $builder->join('study_programs', 'study_programs.id = users.study_program_id', 'left');
        $builder->where('users.deleted_at', null);

        if ($limit !== null) {
            $builder->limit($limit, $offset);
        }

        return $builder->get()->getResultArray();
    }

    /**
     * Get user by username or email
     */
    public function getUserByUsernameOrEmail(string $identifier)
    {
        return $this->where('username', $identifier)
                   ->orWhere('email', $identifier)
                   ->first();
    }

    /**
     * Get users by role
     */
    public function getUsersByRole(string $role)
    {
        return $this->where('role', $role)
                   ->where('status', 'active')
                   ->findAll();
    }

    /**
     * Get users by faculty
     */
    public function getUsersByFaculty(int $facultyId)
    {
        return $this->where('faculty_id', $facultyId)
                   ->where('status', 'active')
                   ->findAll();
    }

    /**
     * Get users by study program
     */
    public function getUsersByStudyProgram(int $studyProgramId)
    {
        return $this->where('study_program_id', $studyProgramId)
                   ->where('status', 'active')
                   ->findAll();
    }

    /**
     * Update last login timestamp
     */
    public function updateLastLogin(int $userId)
    {
        return $this->update($userId, ['last_login' => date('Y-m-d H:i:s')]);
    }

    /**
     * Activate user account
     */
    public function activateUser(int $userId)
    {
        return $this->update($userId, [
            'status' => 'active',
            'email_verified_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Deactivate user account
     */
    public function deactivateUser(int $userId)
    {
        return $this->update($userId, ['status' => 'inactive']);
    }

    /**
     * Get user statistics
     */
    public function getUserStats()
    {
        $total = $this->countAllResults();
        $active = $this->where('status', 'active')->countAllResults();
        $inactive = $this->where('status', 'inactive')->countAllResults();
        $suspended = $this->where('status', 'suspended')->countAllResults();

        $roleStats = $this->select('role, COUNT(*) as count')
                          ->groupBy('role')
                          ->findAll();

        return [
            'total' => $total,
            'active' => $active,
            'inactive' => $inactive,
            'suspended' => $suspended,
            'by_role' => $roleStats
        ];
    }
}
