/* Materio Admin Dashboard CSS */
/* Based on Bootstrap 5 and Materio Design */

:root {
  --bs-primary: #696cff;
  --bs-secondary: #8592a3;
  --bs-success: #71dd37;
  --bs-info: #03c3ec;
  --bs-warning: #ffab00;
  --bs-danger: #ff3e1d;
  --bs-light: #fcfdfd;
  --bs-dark: #233446;
  --bs-gray: #8592a3;
  --bs-gray-dark: #566a7f;
  --bs-gray-100: #f5f5f9;
  --bs-gray-200: #eee;
  --bs-gray-300: #dee2e6;
  --bs-gray-400: #ced4da;
  --bs-gray-500: #adb5bd;
  --bs-gray-600: #6c757d;
  --bs-gray-700: #495057;
  --bs-gray-800: #343a40;
  --bs-gray-900: #212529;
  --bs-white: #fff;
  --bs-black: #000;
}

/* Layout */
.layout-wrapper {
  height: 100vh;
  overflow: hidden;
}

.layout-container {
  display: flex;
  height: 100%;
}

.layout-menu {
  width: 260px;
  background: #fff;
  border-right: 1px solid #d9dee3;
  transition: all 0.25s ease;
}

.layout-page {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.content-wrapper {
  flex: 1;
  overflow: auto;
  background: #f5f5f9;
}

/* Menu Styles */
.menu {
  padding: 0;
  margin: 0;
  list-style: none;
}

.menu-inner {
  padding: 0;
}

.menu-item {
  position: relative;
}

.menu-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  color: #697a8d;
  text-decoration: none;
  transition: all 0.15s ease;
  border-radius: 0;
}

.menu-link:hover {
  color: var(--bs-primary);
  background: rgba(105, 108, 255, 0.04);
}

.menu-item.active .menu-link {
  color: var(--bs-primary);
  background: rgba(105, 108, 255, 0.08);
  font-weight: 500;
}

.menu-icon {
  margin-right: 0.75rem;
  font-size: 1.125rem;
  flex-shrink: 0;
}

.menu-header {
  padding: 1rem 1.5rem 0.5rem;
  margin-top: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.4px;
  color: #a8b1bb;
}

.menu-header:first-child {
  margin-top: 0;
}

/* App Brand */
.app-brand {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #d9dee3;
}

.app-brand-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #697a8d;
}

.app-brand-text {
  font-size: 1.25rem;
  font-weight: 700;
  margin-left: 0.5rem;
  color: #5d596c;
}

/* Navbar */
.layout-navbar {
  background: #fff !important;
  border-bottom: 1px solid #d9dee3;
  padding: 0 1.5rem;
  height: 60px;
  display: flex;
  align-items: center;
}

.navbar-nav-right {
  margin-left: auto;
}

/* Cards */
.card {
  border: 1px solid #d9dee3;
  border-radius: 0.375rem;
  box-shadow: 0 2px 6px 0 rgba(67, 89, 113, 0.12);
}

.card-header {
  background: transparent;
  border-bottom: 1px solid #d9dee3;
  padding: 1.5rem;
}

.card-body {
  padding: 1.5rem;
}

/* Buttons */
.btn {
  border-radius: 0.375rem;
  font-weight: 500;
  padding: 0.4375rem 1.25rem;
}

.btn-primary {
  background: var(--bs-primary);
  border-color: var(--bs-primary);
}

.btn-primary:hover {
  background: #5f61e6;
  border-color: #5f61e6;
}

/* Forms */
.form-control {
  border: 1px solid #d9dee3;
  border-radius: 0.375rem;
  padding: 0.4375rem 0.875rem;
}

.form-control:focus {
  border-color: var(--bs-primary);
  box-shadow: 0 0 0 0.2rem rgba(105, 108, 255, 0.25);
}

/* Tables */
.table {
  border-color: #d9dee3;
}

.table th {
  border-bottom: 2px solid #d9dee3;
  font-weight: 600;
  color: #5d596c;
}

/* Alerts */
.alert {
  border-radius: 0.375rem;
  border: none;
}

.alert-success {
  background: rgba(113, 221, 55, 0.16);
  color: #71dd37;
}

.alert-danger {
  background: rgba(255, 62, 29, 0.16);
  color: #ff3e1d;
}

.alert-warning {
  background: rgba(255, 171, 0, 0.16);
  color: #ffab00;
}

.alert-info {
  background: rgba(3, 195, 236, 0.16);
  color: #03c3ec;
}

/* Footer */
.content-footer {
  background: #fff;
  border-top: 1px solid #d9dee3;
  padding: 1rem 1.5rem;
  margin-top: auto;
}

/* Responsive */
@media (max-width: 1199.98px) {
  .layout-menu {
    position: fixed;
    top: 0;
    left: -260px;
    height: 100vh;
    z-index: 1000;
  }
  
  .layout-menu.show {
    left: 0;
  }
  
  .layout-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(67, 89, 113, 0.6);
    z-index: 999;
    display: none;
  }
  
  .layout-overlay.show {
    display: block;
  }
}

/* Utilities */
.text-primary {
  color: var(--bs-primary) !important;
}

.bg-primary {
  background-color: var(--bs-primary) !important;
}

.border-primary {
  border-color: var(--bs-primary) !important;
}

/* Custom Components */
.stats-card {
  background: linear-gradient(135deg, var(--bs-primary) 0%, #5f61e6 100%);
  color: white;
  border: none;
}

.stats-card .card-body {
  padding: 2rem;
}

.stats-number {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.stats-label {
  font-size: 0.875rem;
  opacity: 0.8;
}

/* Animation */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --bs-body-bg: #25293c;
    --bs-body-color: #a8b1bb;
  }
}
