<?php

/**
 * Test Runner Utility
 * 
 * Orchestrates the execution of all test categories with visual progress tracking
 */

require_once __DIR__ . '/TestLogger.php';

class TestRunner
{
    private $config;
    private $logger;
    private $testCategories = [];
    private $currentCategory = '';
    private $currentTest = '';

    public function __construct($config)
    {
        $this->config = $config;
        $this->logger = new TestLogger($config);
        $this->initializeTestCategories();
    }

    /**
     * Run all enabled test categories
     */
    public function runAllTests()
    {
        $this->logger->logSection('RPS BACKEND TESTING FRAMEWORK', 'Starting comprehensive backend testing');
        
        $totalCategories = count($this->testCategories);
        $currentCategory = 0;

        foreach ($this->testCategories as $category => $info) {
            if (!$this->config['test_categories'][$category]) {
                $this->logger->logWarning("Skipping disabled category: {$info['name']}");
                continue;
            }

            $currentCategory++;
            $this->logger->logProgress($currentCategory, $totalCategories, "Running {$info['name']}");
            
            $this->runTestCategory($category, $info);
        }

        return $this->logger->generateSummary();
    }

    /**
     * Run a specific test category
     */
    public function runTestCategory($category, $info)
    {
        $this->currentCategory = $category;
        $this->logger->logSection($info['name'], $info['description']);

        $startTime = microtime(true);
        
        try {
            // Check if test file exists
            $testFile = __DIR__ . "/../tests/{$category}/run_{$category}_tests.php";
            
            if (!file_exists($testFile)) {
                $this->logger->logError("Test file not found: {$testFile}");
                return false;
            }

            // Include and run the test
            $testResult = $this->executeTestFile($testFile);
            
            $duration = round((microtime(true) - $startTime) * 1000);
            
            if ($testResult) {
                $this->logger->logSuccess("Category '{$info['name']}' completed successfully ({$duration}ms)");
            } else {
                $this->logger->logError("Category '{$info['name']}' failed ({$duration}ms)");
            }

            return $testResult;

        } catch (Exception $e) {
            $this->logger->logError("Exception in category '{$info['name']}'", $e);
            return false;
        }
    }

    /**
     * Execute a test file and capture results
     */
    private function executeTestFile($testFile)
    {
        try {
            // Capture output
            ob_start();
            
            // Include the test file with access to logger
            $logger = $this->logger;
            $config = $this->config;
            $testRunner = $this;
            
            $result = include $testFile;
            
            $output = ob_get_clean();
            
            // Log any captured output
            if (!empty($output)) {
                $this->logger->log('debug', "Test output: {$output}", 'OUTPUT');
            }

            return $result !== false;

        } catch (Exception $e) {
            ob_end_clean();
            $this->logger->logError("Failed to execute test file: {$testFile}", $e);
            return false;
        }
    }

    /**
     * Helper method for tests to log results
     */
    public function logTestResult($testName, $status, $message = '', $duration = 0, $details = [])
    {
        $this->logger->logTestResult($testName, $status, $message, $duration, $details);
    }

    /**
     * Helper method for tests to log progress
     */
    public function logProgress($current, $total, $message = '')
    {
        $this->logger->logProgress($current, $total, $message);
    }

    /**
     * Helper method for tests to log messages
     */
    public function log($level, $message, $category = 'TEST')
    {
        $this->logger->log($level, $message, $category);
    }

    /**
     * Helper method for tests to log subsections
     */
    public function logSubSection($title, $description = '')
    {
        $this->logger->logSubSection($title, $description);
    }

    /**
     * Helper method for tests to log success
     */
    public function logSuccess($message)
    {
        $this->logger->logSuccess($message);
    }

    /**
     * Helper method for tests to log warnings
     */
    public function logWarning($message)
    {
        $this->logger->logWarning($message);
    }

    /**
     * Helper method for tests to log errors
     */
    public function logError($message, $exception = null)
    {
        $this->logger->logError($message, $exception);
    }

    /**
     * Get configuration
     */
    public function getConfig()
    {
        return $this->config;
    }

    /**
     * Get logger instance
     */
    public function getLogger()
    {
        return $this->logger;
    }

    /**
     * Initialize test categories
     */
    private function initializeTestCategories()
    {
        $this->testCategories = [
            '01_environment' => [
                'name' => 'Environment & Setup Tests',
                'description' => 'Verify PHP version, extensions, CodeIgniter framework, and basic configuration'
            ],
            '02_database' => [
                'name' => 'Database Connectivity Tests',
                'description' => 'Test database connection, migrations, seeders, and table structure'
            ],
            '03_authentication' => [
                'name' => 'Authentication System Tests',
                'description' => 'Test user registration, login, JWT tokens, and role-based access'
            ],
            '04_api_endpoints' => [
                'name' => 'API Endpoint Tests',
                'description' => 'Validate all API routes, request/response formats, and error handling'
            ],
            '05_models' => [
                'name' => 'Model Functionality Tests',
                'description' => 'Test CRUD operations, data validation, relationships, and business logic'
            ],
            '06_integration' => [
                'name' => 'Integration Tests',
                'description' => 'Test frontend-backend communication and cross-module functionality'
            ],
            '07_performance' => [
                'name' => 'Performance Tests',
                'description' => 'Measure response times, memory usage, and concurrent request handling'
            ]
        ];
    }

    /**
     * Utility method to make HTTP requests for API testing
     */
    public function makeHttpRequest($method, $url, $data = null, $headers = [])
    {
        $startTime = microtime(true);
        
        $ch = curl_init();
        
        // Default headers
        $defaultHeaders = $this->config['api']['headers'];
        $headers = array_merge($defaultHeaders, $headers);
        
        // Convert headers to curl format
        $curlHeaders = [];
        foreach ($headers as $key => $value) {
            $curlHeaders[] = "{$key}: {$value}";
        }

        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $this->config['api']['timeout'],
            CURLOPT_HTTPHEADER => $curlHeaders,
            CURLOPT_CUSTOMREQUEST => strtoupper($method),
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_FOLLOWLOCATION => true,
        ]);

        if ($data && in_array(strtoupper($method), ['POST', 'PUT', 'PATCH'])) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, is_array($data) ? json_encode($data) : $data);
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        $duration = round((microtime(true) - $startTime) * 1000);
        
        curl_close($ch);

        if ($error) {
            throw new Exception("cURL Error: {$error}");
        }

        return [
            'status_code' => $httpCode,
            'body' => $response,
            'duration' => $duration,
            'json' => json_decode($response, true)
        ];
    }

    /**
     * Utility method to check if a service is running
     */
    public function isServiceRunning($host, $port)
    {
        $connection = @fsockopen($host, $port, $errno, $errstr, 5);
        if ($connection) {
            fclose($connection);
            return true;
        }
        return false;
    }

    /**
     * Utility method to check file permissions
     */
    public function checkFilePermissions($path, $requiredPermissions = 0755)
    {
        if (!file_exists($path)) {
            return false;
        }

        $currentPermissions = fileperms($path) & 0777;
        return $currentPermissions >= $requiredPermissions;
    }

    /**
     * Utility method to check PHP extension
     */
    public function checkPhpExtension($extension)
    {
        return extension_loaded($extension);
    }

    /**
     * Utility method to check PHP version
     */
    public function checkPhpVersion($requiredVersion)
    {
        return version_compare(PHP_VERSION, $requiredVersion, '>=');
    }

    /**
     * Utility method to measure memory usage
     */
    public function getMemoryUsage()
    {
        return [
            'current' => memory_get_usage(true),
            'peak' => memory_get_peak_usage(true),
            'current_mb' => round(memory_get_usage(true) / 1024 / 1024, 2),
            'peak_mb' => round(memory_get_peak_usage(true) / 1024 / 1024, 2)
        ];
    }
}
