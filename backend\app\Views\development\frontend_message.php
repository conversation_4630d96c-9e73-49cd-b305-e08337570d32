<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= esc($title) ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@mdi/font@6.x/css/materialdesignicons.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 40px;
            max-width: 600px;
            width: 90%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: #1976D2;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
        }
        
        .logo i {
            color: white;
            font-size: 40px;
        }
        
        h1 {
            color: #1976D2;
            font-size: 2.5rem;
            font-weight: 500;
            margin-bottom: 10px;
        }
        
        .subtitle {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 30px;
        }
        
        .status-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        
        .status-card.warning {
            border-left-color: #ffc107;
            background: #fff8e1;
        }
        
        .status-title {
            font-weight: 500;
            font-size: 1.1rem;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .status-description {
            color: #666;
            line-height: 1.6;
        }
        
        .instructions {
            background: #e3f2fd;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        
        .instructions h3 {
            color: #1976D2;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .instructions ol {
            margin-left: 20px;
            line-height: 1.8;
        }
        
        .instructions code {
            background: #f5f5f5;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            color: #d63384;
        }
        
        .api-info {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
            color: #666;
            font-size: 0.9rem;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #1976D2;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            margin: 10px;
            transition: background-color 0.3s;
        }
        
        .btn:hover {
            background: #1565C0;
        }
        
        .btn-outline {
            background: transparent;
            border: 2px solid #1976D2;
            color: #1976D2;
        }
        
        .btn-outline:hover {
            background: #1976D2;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <i class="mdi mdi-school"></i>
        </div>
        
        <h1><?= esc($title) ?></h1>
        <p class="subtitle"><?= esc($message) ?></p>
        
        <div class="status-card warning">
            <div class="status-title">
                <i class="mdi mdi-alert-circle"></i>
                Frontend Not Built
            </div>
            <div class="status-description">
                <?= esc($description) ?>
            </div>
        </div>
        
        <div class="status-card">
            <div class="status-title">
                <i class="mdi mdi-check-circle"></i>
                <?= esc($api_status) ?>
            </div>
            <div class="status-description">
                CodeIgniter 4 backend is running and ready to serve API requests.
            </div>
        </div>
        
        <div class="instructions">
            <h3>
                <i class="mdi mdi-cog"></i>
                Setup Instructions
            </h3>
            <ol>
                <li>Navigate to the frontend directory: <code>cd frontend</code></li>
                <li>Install dependencies: <code>npm install</code></li>
                <li>Build the frontend: <code>npm run build</code></li>
                <li>Refresh this page to see the application</li>
            </ol>
        </div>
        
        <div>
            <a href="<?= base_url('api/v1/auth/login') ?>" class="btn btn-outline">Test API</a>
            <a href="javascript:location.reload()" class="btn">Refresh Page</a>
        </div>
        
        <div class="api-info">
            <strong>API Base URL:</strong> <?= esc($base_url) ?>api/v1/<br>
            <strong>Environment:</strong> Development Mode<br>
            <strong>Framework:</strong> CodeIgniter 4 + Vue.js 3
        </div>
    </div>
</body>
</html>
