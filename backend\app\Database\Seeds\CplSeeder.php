<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class CplSeeder extends Seeder
{
    public function run()
    {
        // Get study program IDs
        $studyPrograms = $this->db->table('study_programs')->get()->getResultArray();
        $studyProgramMap = [];
        foreach ($studyPrograms as $sp) {
            $studyProgramMap[$sp['code']] = $sp['id'];
        }

        // Get user ID for created_by
        $user = $this->db->table('users')
            ->where('username', 'kaprodi.ti')
            ->get()
            ->getRowArray();

        $data = [];

        // CPL untuk Teknik Informatika
        if (isset($studyProgramMap['TI'])) {
            $cplTI = [
                [
                    'code' => 'CPL-S1',
                    'study_program_id' => $studyProgramMap['TI'],
                    'category' => 'sikap',
                    'description' => '<PERSON>ak<PERSON> kep<PERSON> dan mampu menunjukkan sikap religius',
                    'learning_outcome' => 'Mahasiswa mampu menunjukkan sikap religius, bertanggung jawab, dan beretika dalam kehidupan bermasyarakat',
                    'is_active' => 1,
                    'created_by' => $user['id'] ?? null,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ],
                [
                    'code' => 'CPL-S2',
                    'study_program_id' => $studyProgramMap['TI'],
                    'category' => 'sikap',
                    'description' => 'Menjunjung tinggi nilai kemanusiaan dalam menjalankan tugas',
                    'learning_outcome' => 'Mahasiswa mampu menunjukkan sikap menghargai keanekaragaman budaya, pandangan, agama, dan kepercayaan',
                    'is_active' => 1,
                    'created_by' => $user['id'] ?? null,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ],
                [
                    'code' => 'CPL-P1',
                    'study_program_id' => $studyProgramMap['TI'],
                    'category' => 'pengetahuan',
                    'description' => 'Menguasai konsep teoritis bidang pengetahuan Teknik Informatika',
                    'learning_outcome' => 'Mahasiswa menguasai konsep teoritis matematika, algoritma, pemrograman, basis data, jaringan komputer, dan rekayasa perangkat lunak',
                    'is_active' => 1,
                    'created_by' => $user['id'] ?? null,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ],
                [
                    'code' => 'CPL-P2',
                    'study_program_id' => $studyProgramMap['TI'],
                    'category' => 'pengetahuan',
                    'description' => 'Menguasai konsep teoritis tentang struktur data dan algoritma',
                    'learning_outcome' => 'Mahasiswa menguasai konsep struktur data, algoritma, kompleksitas algoritma, dan teknik optimasi',
                    'is_active' => 1,
                    'created_by' => $user['id'] ?? null,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ],
                [
                    'code' => 'CPL-KU1',
                    'study_program_id' => $studyProgramMap['TI'],
                    'category' => 'keterampilan_umum',
                    'description' => 'Mampu menerapkan pemikiran logis, kritis, sistematis, dan inovatif',
                    'learning_outcome' => 'Mahasiswa mampu menerapkan pemikiran logis, kritis, sistematis, dan inovatif dalam konteks pengembangan atau implementasi ilmu pengetahuan dan teknologi',
                    'is_active' => 1,
                    'created_by' => $user['id'] ?? null,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ],
                [
                    'code' => 'CPL-KU2',
                    'study_program_id' => $studyProgramMap['TI'],
                    'category' => 'keterampilan_umum',
                    'description' => 'Mampu mengkaji implikasi pengembangan atau implementasi ilmu pengetahuan teknologi',
                    'learning_outcome' => 'Mahasiswa mampu mengkaji implikasi pengembangan atau implementasi ilmu pengetahuan teknologi yang memperhatikan dan menerapkan nilai humaniora',
                    'is_active' => 1,
                    'created_by' => $user['id'] ?? null,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ],
                [
                    'code' => 'CPL-KK1',
                    'study_program_id' => $studyProgramMap['TI'],
                    'category' => 'keterampilan_khusus',
                    'description' => 'Mampu menganalisis, merancang, dan mengimplementasikan sistem perangkat lunak',
                    'learning_outcome' => 'Mahasiswa mampu menganalisis kebutuhan, merancang, mengimplementasikan, dan menguji sistem perangkat lunak sesuai dengan standar industri',
                    'is_active' => 1,
                    'created_by' => $user['id'] ?? null,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ],
                [
                    'code' => 'CPL-KK2',
                    'study_program_id' => $studyProgramMap['TI'],
                    'category' => 'keterampilan_khusus',
                    'description' => 'Mampu merancang dan mengelola basis data',
                    'learning_outcome' => 'Mahasiswa mampu merancang, mengimplementasikan, dan mengelola basis data yang efisien dan aman',
                    'is_active' => 1,
                    'created_by' => $user['id'] ?? null,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ],
                [
                    'code' => 'CPL-KK3',
                    'study_program_id' => $studyProgramMap['TI'],
                    'category' => 'keterampilan_khusus',
                    'description' => 'Mampu merancang dan mengimplementasikan jaringan komputer',
                    'learning_outcome' => 'Mahasiswa mampu merancang, mengkonfigurasi, dan mengelola jaringan komputer yang aman dan efisien',
                    'is_active' => 1,
                    'created_by' => $user['id'] ?? null,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ],
            ];
            $data = array_merge($data, $cplTI);
        }

        // CPL untuk Sistem Informasi
        if (isset($studyProgramMap['SI'])) {
            $cplSI = [
                [
                    'code' => 'CPL-S1',
                    'study_program_id' => $studyProgramMap['SI'],
                    'category' => 'sikap',
                    'description' => 'Bertakwa kepada Tuhan Yang Maha Esa dan mampu menunjukkan sikap religius',
                    'learning_outcome' => 'Mahasiswa mampu menunjukkan sikap religius, bertanggung jawab, dan beretika dalam kehidupan bermasyarakat',
                    'is_active' => 1,
                    'created_by' => $user['id'] ?? null,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ],
                [
                    'code' => 'CPL-P1',
                    'study_program_id' => $studyProgramMap['SI'],
                    'category' => 'pengetahuan',
                    'description' => 'Menguasai konsep teoritis bidang pengetahuan Sistem Informasi',
                    'learning_outcome' => 'Mahasiswa menguasai konsep teoritis sistem informasi, analisis bisnis, manajemen proyek, dan tata kelola TI',
                    'is_active' => 1,
                    'created_by' => $user['id'] ?? null,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ],
                [
                    'code' => 'CPL-KK1',
                    'study_program_id' => $studyProgramMap['SI'],
                    'category' => 'keterampilan_khusus',
                    'description' => 'Mampu menganalisis dan merancang sistem informasi organisasi',
                    'learning_outcome' => 'Mahasiswa mampu menganalisis kebutuhan bisnis dan merancang sistem informasi yang sesuai dengan kebutuhan organisasi',
                    'is_active' => 1,
                    'created_by' => $user['id'] ?? null,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ],
            ];
            $data = array_merge($data, $cplSI);
        }

        // Insert data
        if (!empty($data)) {
            $this->db->table('cpl')->insertBatch($data);
            echo "CPL seeded successfully! Total records: " . count($data) . PHP_EOL;
        } else {
            echo "No study programs found for CPL seeding." . PHP_EOL;
        }
    }
}
