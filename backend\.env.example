#--------------------------------------------------------------------
# RPS Management System - Backend Environment Configuration
#--------------------------------------------------------------------

#--------------------------------------------------------------------
# ENVIRONMENT
#--------------------------------------------------------------------
CI_ENVIRONMENT = development

#--------------------------------------------------------------------
# APP
#--------------------------------------------------------------------
app.baseURL = 'http://localhost:8080/'
app.indexPage = ''
app.uriProtocol = 'REQUEST_URI'
app.defaultLocale = 'id'
app.negotiateLocale = false
app.supportedLocales = ['id', 'en']
app.appTimezone = 'Asia/Jakarta'
app.charset = 'UTF-8'
app.forceGlobalSecureRequests = false
app.sessionDriver = 'CodeIgniter\Session\Handlers\FileHandler'
app.sessionCookieName = 'rps_session'
app.sessionExpiration = 7200
app.sessionSavePath = null
app.sessionMatchIP = false
app.sessionTimeToUpdate = 300
app.sessionRegenerateDestroy = false
app.cookiePrefix = ''
app.cookieDomain = ''
app.cookiePath = '/'
app.cookieSecure = false
app.cookieHTTPOnly = false
app.cookieSameSite = 'Lax'
app.proxyIPs = ''
app.CSRFTokenName = 'csrf_token_name'
app.CSRFHeaderName = 'X-CSRF-TOKEN'
app.CSRFCookieName = 'csrf_cookie_name'
app.CSRFExpiration = 7200
app.CSRFRegenerate = true
app.CSRFRedirect = true
app.CSRFSameSite = 'Lax'
app.CSPEnabled = false

#--------------------------------------------------------------------
# DATABASE
#--------------------------------------------------------------------
database.default.hostname = localhost
database.default.database = rps_management
database.default.username = rps_user
database.default.password = rps_password
database.default.DBDriver = Postgre
database.default.DBPrefix = 
database.default.port = 5432
database.default.pConnect = false
database.default.DBDebug = true
database.default.charset = utf8
database.default.DBCollat = 
database.default.swapPre = 
database.default.encrypt = false
database.default.compress = false
database.default.strictOn = false
database.default.failover = []

#--------------------------------------------------------------------
# CONTENT SECURITY POLICY
#--------------------------------------------------------------------
contentsecuritypolicy.reportOnly = false
contentsecuritypolicy.defaultSrc = 'none'
contentsecuritypolicy.scriptSrc = 'self'
contentsecuritypolicy.styleSrc = 'self'
contentsecuritypolicy.imageSrc = 'self'
contentsecuritypolicy.baseURI = null
contentsecuritypolicy.childSrc = null
contentsecuritypolicy.connectSrc = 'self'
contentsecuritypolicy.fontSrc = null
contentsecuritypolicy.formAction = null
contentsecuritypolicy.frameAncestors = null
contentsecuritypolicy.frameSrc = null
contentsecuritypolicy.mediaSrc = null
contentsecuritypolicy.objectSrc = null
contentsecuritypolicy.pluginTypes = null
contentsecuritypolicy.reportURI = null
contentsecuritypolicy.sandbox = false
contentsecuritypolicy.upgradeInsecureRequests = false
contentsecuritypolicy.styleNonceTag = '{csp-style-nonce}'
contentsecuritypolicy.scriptNonceTag = '{csp-script-nonce}'
contentsecuritypolicy.autoNonce = true

#--------------------------------------------------------------------
# ENCRYPTION
#--------------------------------------------------------------------
encryption.key = 
encryption.driver = OpenSSL
encryption.blockSize = 16
encryption.digest = SHA512

#--------------------------------------------------------------------
# HONEYPOT
#--------------------------------------------------------------------
honeypot.hidden = 'true'
honeypot.label = 'Fill This Field'
honeypot.name = 'honeypot'
honeypot.template = '<label>{label}</label><input type="text" name="{name}" value=""/>'
honeypot.container = '<div style="display:none">{template}</div>'

#--------------------------------------------------------------------
# SECURITY
#--------------------------------------------------------------------
security.csrfProtection = 'cookie'
security.tokenRandomize = false
security.tokenName = 'csrf_token_name'
security.headerName = 'X-CSRF-TOKEN'
security.cookieName = 'csrf_cookie_name'
security.expires = 7200
security.regenerate = true
security.redirect = true
security.samesite = 'Lax'

#--------------------------------------------------------------------
# LOGGER
#--------------------------------------------------------------------
logger.threshold = 4

#--------------------------------------------------------------------
# CACHE
#--------------------------------------------------------------------
cache.handler = redis
cache.backupHandler = file
cache.storePath = WRITEPATH . 'cache/'
cache.redis.host = localhost
cache.redis.password = rps_redis_password
cache.redis.port = 6379
cache.redis.timeout = 0
cache.redis.database = 0

#--------------------------------------------------------------------
# JWT AUTHENTICATION
#--------------------------------------------------------------------
jwt.secret = your-256-bit-secret-key-here
jwt.algorithm = HS256
jwt.expiration = 3600
jwt.refresh_expiration = 604800

#--------------------------------------------------------------------
# EMAIL
#--------------------------------------------------------------------
email.fromEmail = <EMAIL>
email.fromName = RPS Management System
email.recipients = 
email.userAgent = CodeIgniter
email.protocol = smtp
email.mailPath = /usr/sbin/sendmail
email.SMTPHost = localhost
email.SMTPUser = 
email.SMTPPass = 
email.SMTPPort = 1025
email.SMTPTimeout = 5
email.SMTPKeepAlive = false
email.SMTPCrypto = 
email.wordWrap = true
email.wrapChars = 76
email.mailType = html
email.charset = UTF-8
email.validate = false
email.priority = 3
email.CRLF = \r\n
email.newline = \r\n
email.BCCBatchMode = false
email.BCCBatchSize = 200
email.DSN = false

#--------------------------------------------------------------------
# FILE UPLOAD
#--------------------------------------------------------------------
upload.maxSize = 52428800
upload.allowedTypes = jpg|jpeg|png|gif|pdf|doc|docx|xls|xlsx|ppt|pptx
upload.uploadPath = WRITEPATH . 'uploads/'

#--------------------------------------------------------------------
# API SETTINGS
#--------------------------------------------------------------------
api.version = v1
api.rateLimit = 100
api.rateLimitWindow = 3600
api.cors.allowedOrigins = *
api.cors.allowedMethods = GET,POST,PUT,DELETE,OPTIONS
api.cors.allowedHeaders = Content-Type,Authorization,X-Requested-With

#--------------------------------------------------------------------
# PAGINATION
#--------------------------------------------------------------------
pager.perPage = 20
pager.maxPerPage = 100

#--------------------------------------------------------------------
# CUSTOM SETTINGS
#--------------------------------------------------------------------
app.name = RPS Management System
app.version = 1.0.0
app.author = Professional Software Engineering Team
app.supportEmail = <EMAIL>
app.maintenanceMode = false
app.debugMode = true
