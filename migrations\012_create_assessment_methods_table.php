<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateAssessmentMethodsTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'name' => [
                'type'       => 'VARCHAR',
                'constraint' => 255,
                'null'       => false,
            ],
            'description' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'type' => [
                'type'       => 'ENUM',
                'constraint' => ['formatif', 'sumatif'],
                'null'       => false,
            ],
            'category' => [
                'type'       => 'ENUM',
                'constraint' => ['tugas', 'kuis', 'uts', 'uas', 'praktikum', 'proyek', 'presentasi', 'lainnya'],
                'null'       => false,
            ],
            'is_active' => [
                'type'       => 'TINYINT',
                'constraint' => 1,
                'default'    => 1,
            ],
            'created_by' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
                'null'       => true,
            ],
            'created_at' => [
                'type'    => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
            ],
            'updated_at' => [
                'type'    => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
                'on_update' => 'CURRENT_TIMESTAMP',
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('type');
        $this->forge->addKey('category');
        $this->forge->addKey('is_active');
        $this->forge->addKey('created_by');
        
        $this->forge->addForeignKey('created_by', 'users', 'id', 'SET NULL', 'CASCADE');
        
        $this->forge->createTable('assessment_methods');

        // Insert default assessment methods
        $data = [
            [
                'name' => 'Tugas Individu',
                'description' => 'Tugas yang dikerjakan secara individu',
                'type' => 'formatif',
                'category' => 'tugas',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Tugas Kelompok',
                'description' => 'Tugas yang dikerjakan secara berkelompok',
                'type' => 'formatif',
                'category' => 'tugas',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Kuis Mingguan',
                'description' => 'Kuis singkat setiap minggu',
                'type' => 'formatif',
                'category' => 'kuis',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Ujian Tengah Semester',
                'description' => 'Ujian tengah semester',
                'type' => 'sumatif',
                'category' => 'uts',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Ujian Akhir Semester',
                'description' => 'Ujian akhir semester',
                'type' => 'sumatif',
                'category' => 'uas',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Praktikum',
                'description' => 'Penilaian praktikum laboratorium',
                'type' => 'formatif',
                'category' => 'praktikum',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Proyek Akhir',
                'description' => 'Proyek besar di akhir semester',
                'type' => 'sumatif',
                'category' => 'proyek',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'Presentasi',
                'description' => 'Penilaian presentasi mahasiswa',
                'type' => 'formatif',
                'category' => 'presentasi',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ];

        $this->db->table('assessment_methods')->insertBatch($data);
    }

    public function down()
    {
        $this->forge->dropTable('assessment_methods');
    }
}
