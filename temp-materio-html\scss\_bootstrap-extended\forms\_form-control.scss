/* Form control
******************************************************************************* */

.form-control {
  /*
  ? Form control (all size) padding calc due to border increase on focus */
  padding-block: calc($input-padding-y - $input-border-width);
  padding-inline: calc($input-padding-x - $input-border-width);

  // form input placeholder animation
  &::placeholder,
  &:focus::placeholder {
    @include transition(all ease .2s);
  }

  /* border color on hover state when element not in focus or disabled */
  &:hover {
    &:not(:focus):not(.tagify--focus) {
      border-color: $input-hover-border-color;
    }
  }

  &:focus{
    border-width: $input-focus-border-width;
    padding-block: calc($input-padding-y - $input-focus-border-width);
    padding-inline: calc($input-padding-x - $input-focus-border-width);
    &::file-selector-button {
      box-shadow: $input-border-width 0 0  $input-focus-border-color;
    }
  }
  &.form-control-lg {
    padding-block: calc($input-padding-y-lg - $input-border-width);
    padding-inline: calc($input-padding-x-lg - $input-border-width);
    &:focus {
      padding-block: calc($input-padding-y-lg - $input-focus-border-width);
      padding-inline: calc($input-padding-x-lg - $input-focus-border-width);
    }
    &::file-selector-button {
      margin-block: (-$input-padding-y-lg - .0625rem);
      padding-block: calc($input-padding-y-lg + .0625rem);
    }
  }
  &.form-control-sm {
    padding-block: calc($input-padding-y-sm - $input-border-width);
    padding-inline: calc($input-padding-x-sm - $input-border-width);
    &:focus {
      padding-block: calc($input-padding-y-sm - $input-focus-border-width);
      padding-inline: calc($input-padding-x-sm - $input-focus-border-width);
    }
    &::file-selector-button {
      margin-block: (-$input-padding-y-sm - .0625rem);
      padding-block: calc($input-padding-y-sm + .0625rem);
    }
  }
  &.autosize{
    field-sizing: content;
    min-block-size: $autozise-min-height;
    overflow-x: hidden;
    resize: none;
  }
}
