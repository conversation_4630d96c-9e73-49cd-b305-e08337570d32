<?php

/**
 * Test Logger Utility
 * 
 * Provides comprehensive logging functionality for the testing framework
 */

class TestLogger
{
    private $config;
    private $logFile;
    private $htmlFile;
    private $jsonFile;
    private $startTime;
    private $testResults = [];

    public function __construct($config)
    {
        $this->config = $config;
        $this->startTime = microtime(true);
        
        // Ensure log directory exists
        $logDir = $this->config['logging']['file_path'];
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        // Initialize log files
        $timestamp = date('Y-m-d_H-i-s');
        $this->logFile = $logDir . "test_log_{$timestamp}.txt";
        $this->htmlFile = $logDir . "test_log_{$timestamp}.html";
        $this->jsonFile = $logDir . "test_results_{$timestamp}.json";

        // Initialize HTML log
        $this->initializeHtmlLog();
        
        $this->log('info', 'Testing session started', 'SYSTEM');
    }

    /**
     * Log a message with specified level
     */
    public function log($level, $message, $category = 'TEST')
    {
        $timestamp = date('Y-m-d H:i:s');
        $microtime = sprintf('%.3f', microtime(true) - $this->startTime);
        
        // Console output with colors
        $this->logToConsole($level, $message, $category, $microtime);
        
        // File output
        $this->logToFile($level, $message, $category, $timestamp);
        
        // HTML output
        $this->logToHtml($level, $message, $category, $timestamp);
    }

    /**
     * Log test result
     */
    public function logTestResult($testName, $status, $message = '', $duration = 0, $details = [])
    {
        $result = [
            'test_name' => $testName,
            'status' => $status, // 'PASS', 'FAIL', 'SKIP'
            'message' => $message,
            'duration' => $duration,
            'timestamp' => date('Y-m-d H:i:s'),
            'details' => $details
        ];

        $this->testResults[] = $result;

        // Log to console and files
        $statusIcon = $this->getStatusIcon($status);
        $durationText = $duration > 0 ? " ({$duration}ms)" : "";
        $this->log('info', "{$statusIcon} {$testName}{$durationText}: {$message}", 'RESULT');

        // Save JSON results
        $this->saveJsonResults();
    }

    /**
     * Log progress with visual indicator
     */
    public function logProgress($current, $total, $message = '')
    {
        $percentage = round(($current / $total) * 100);
        $progressBar = $this->createProgressBar($percentage);
        
        $progressMessage = "Progress: {$progressBar} {$percentage}% ({$current}/{$total})";
        if ($message) {
            $progressMessage .= " - {$message}";
        }
        
        $this->log('info', $progressMessage, 'PROGRESS');
    }

    /**
     * Log section header
     */
    public function logSection($title, $description = '')
    {
        $separator = str_repeat('=', 60);
        $this->log('info', $separator, 'SECTION');
        $this->log('info', "🧪 {$title}", 'SECTION');
        if ($description) {
            $this->log('info', $description, 'SECTION');
        }
        $this->log('info', $separator, 'SECTION');
    }

    /**
     * Log subsection header
     */
    public function logSubSection($title, $description = '')
    {
        $separator = str_repeat('-', 40);
        $this->log('info', $separator, 'SUBSECTION');
        $this->log('info', "📋 {$title}", 'SUBSECTION');
        if ($description) {
            $this->log('info', $description, 'SUBSECTION');
        }
    }

    /**
     * Log error with stack trace
     */
    public function logError($message, $exception = null)
    {
        $this->log('error', "❌ ERROR: {$message}", 'ERROR');
        
        if ($exception) {
            $this->log('error', "Exception: " . $exception->getMessage(), 'ERROR');
            $this->log('error', "File: " . $exception->getFile() . ":" . $exception->getLine(), 'ERROR');
            $this->log('debug', "Stack trace:\n" . $exception->getTraceAsString(), 'ERROR');
        }
    }

    /**
     * Log success message
     */
    public function logSuccess($message)
    {
        $this->log('info', "✅ SUCCESS: {$message}", 'SUCCESS');
    }

    /**
     * Log warning message
     */
    public function logWarning($message)
    {
        $this->log('warning', "⚠️ WARNING: {$message}", 'WARNING');
    }

    /**
     * Generate final test summary
     */
    public function generateSummary()
    {
        $totalTests = count($this->testResults);
        $passedTests = count(array_filter($this->testResults, fn($r) => $r['status'] === 'PASS'));
        $failedTests = count(array_filter($this->testResults, fn($r) => $r['status'] === 'FAIL'));
        $skippedTests = count(array_filter($this->testResults, fn($r) => $r['status'] === 'SKIP'));
        
        $totalDuration = array_sum(array_column($this->testResults, 'duration'));
        $sessionDuration = round((microtime(true) - $this->startTime) * 1000);

        $this->logSection('TEST SUMMARY');
        $this->log('info', "Total Tests: {$totalTests}", 'SUMMARY');
        $this->log('info', "✅ Passed: {$passedTests}", 'SUMMARY');
        $this->log('info', "❌ Failed: {$failedTests}", 'SUMMARY');
        $this->log('info', "⏭️ Skipped: {$skippedTests}", 'SUMMARY');
        $this->log('info', "⏱️ Total Test Duration: {$totalDuration}ms", 'SUMMARY');
        $this->log('info', "🕒 Session Duration: {$sessionDuration}ms", 'SUMMARY');
        
        $successRate = $totalTests > 0 ? round(($passedTests / $totalTests) * 100, 2) : 0;
        $this->log('info', "📊 Success Rate: {$successRate}%", 'SUMMARY');

        // Close HTML log
        $this->finalizeHtmlLog();

        return [
            'total' => $totalTests,
            'passed' => $passedTests,
            'failed' => $failedTests,
            'skipped' => $skippedTests,
            'success_rate' => $successRate,
            'total_duration' => $totalDuration,
            'session_duration' => $sessionDuration
        ];
    }

    /**
     * Get test results
     */
    public function getTestResults()
    {
        return $this->testResults;
    }

    /**
     * Private helper methods
     */
    private function logToConsole($level, $message, $category, $microtime)
    {
        $colors = [
            'debug' => "\033[36m",    // Cyan
            'info' => "\033[37m",     // White
            'warning' => "\033[33m",  // Yellow
            'error' => "\033[31m",    // Red
            'reset' => "\033[0m"      // Reset
        ];

        $color = $colors[$level] ?? $colors['info'];
        $reset = $colors['reset'];
        
        $formattedMessage = sprintf(
            "%s[%s] [%s] %s: %s%s\n",
            $color,
            $microtime,
            strtoupper($level),
            $category,
            $message,
            $reset
        );

        echo $formattedMessage;
    }

    private function logToFile($level, $message, $category, $timestamp)
    {
        $formattedMessage = sprintf(
            "[%s] [%s] %s: %s\n",
            $timestamp,
            strtoupper($level),
            $category,
            $message
        );

        file_put_contents($this->logFile, $formattedMessage, FILE_APPEND | LOCK_EX);
    }

    private function logToHtml($level, $message, $category, $timestamp)
    {
        $cssClass = "log-{$level}";
        $formattedMessage = sprintf(
            '<div class="%s"><span class="timestamp">[%s]</span> <span class="level">[%s]</span> <span class="category">%s:</span> <span class="message">%s</span></div>' . "\n",
            $cssClass,
            $timestamp,
            strtoupper($level),
            $category,
            htmlspecialchars($message)
        );

        file_put_contents($this->htmlFile, $formattedMessage, FILE_APPEND | LOCK_EX);
    }

    private function initializeHtmlLog()
    {
        $html = '<!DOCTYPE html>
<html>
<head>
    <title>RPS Backend Test Log</title>
    <style>
        body { font-family: monospace; background: #1e1e1e; color: #d4d4d4; margin: 20px; }
        .log-debug { color: #4fc1ff; }
        .log-info { color: #d4d4d4; }
        .log-warning { color: #ffcc02; }
        .log-error { color: #f44747; }
        .timestamp { color: #808080; }
        .level { font-weight: bold; }
        .category { color: #569cd6; }
        .message { margin-left: 10px; }
        h1 { color: #4fc1ff; border-bottom: 2px solid #4fc1ff; }
    </style>
</head>
<body>
    <h1>RPS Management System - Backend Test Log</h1>
    <div class="log-container">
';
        file_put_contents($this->htmlFile, $html);
    }

    private function finalizeHtmlLog()
    {
        $html = '    </div>
</body>
</html>';
        file_put_contents($this->htmlFile, $html, FILE_APPEND | LOCK_EX);
    }

    private function saveJsonResults()
    {
        $data = [
            'session_info' => [
                'start_time' => date('Y-m-d H:i:s', $this->startTime),
                'duration' => round((microtime(true) - $this->startTime) * 1000),
                'total_tests' => count($this->testResults)
            ],
            'results' => $this->testResults
        ];

        file_put_contents($this->jsonFile, json_encode($data, JSON_PRETTY_PRINT));
    }

    private function getStatusIcon($status)
    {
        return match($status) {
            'PASS' => '✅',
            'FAIL' => '❌',
            'SKIP' => '⏭️',
            default => '❓'
        };
    }

    private function createProgressBar($percentage, $width = 30)
    {
        $filled = round(($percentage / 100) * $width);
        $empty = $width - $filled;
        return '[' . str_repeat('█', $filled) . str_repeat('░', $empty) . ']';
    }
}
