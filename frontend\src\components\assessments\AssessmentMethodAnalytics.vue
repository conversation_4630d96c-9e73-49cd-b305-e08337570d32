<template>
  <div class="pa-6">
    <div class="text-center py-8">
      <v-icon size="64" color="grey-lighten-2">mdi-chart-bar</v-icon>
      <p class="text-h6 text-medium-emphasis mt-4">Assessment Method Analytics</p>
      <p class="text-body-2 text-medium-emphasis">
        Usage analytics and performance metrics for {{ method.name }}
      </p>
      <p class="text-caption text-medium-emphasis mt-2">
        This feature will be fully implemented in the next development phase
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { AssessmentMethod } from '@/types/auth'

interface Props {
  method: AssessmentMethod
}

defineProps<Props>()
defineEmits<{
  close: []
}>()
</script>
