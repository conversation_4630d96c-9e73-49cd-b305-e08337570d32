<template>
  <div>
    <!-- <PERSON> Header -->
    <v-row class="mb-6">
      <v-col>
        <h1 class="text-h4 font-weight-bold text-primary">Reports & Analytics</h1>
        <p class="text-subtitle-1 text-medium-emphasis">
          Comprehensive reporting system with analytics, export functionality, and automated scheduling
        </p>
      </v-col>
    </v-row>

    <!-- Navigation Tabs -->
    <v-tabs
      v-model="activeTab"
      color="primary"
      class="mb-6"
    >
      <v-tab value="dashboard">
        <v-icon start>mdi-view-dashboard</v-icon>
        Dashboard
      </v-tab>

      <v-tab value="prebuilt">
        <v-icon start>mdi-file-chart</v-icon>
        Pre-built Reports
      </v-tab>

      <v-tab value="templates">
        <v-icon start>mdi-file-document-multiple</v-icon>
        Report Templates
      </v-tab>

      <v-tab value="instances">
        <v-icon start>mdi-history</v-icon>
        Generated Reports
      </v-tab>

      <v-tab value="scheduled">
        <v-icon start>mdi-calendar-clock</v-icon>
        Scheduled Reports
      </v-tab>

      <v-tab value="analytics">
        <v-icon start>mdi-chart-line</v-icon>
        Usage Analytics
      </v-tab>
    </v-tabs>

    <!-- Tab Content -->
    <v-tabs-window v-model="activeTab">
      <!-- Dashboard Tab -->
      <v-tabs-window-item value="dashboard">
        <ReportsDashboardTab />
      </v-tabs-window-item>

      <!-- Pre-built Reports Tab -->
      <v-tabs-window-item value="prebuilt">
        <PrebuiltReportsTab />
      </v-tabs-window-item>

      <!-- Report Templates Tab -->
      <v-tabs-window-item value="templates">
        <ReportTemplatesTab />
      </v-tabs-window-item>

      <!-- Generated Reports Tab -->
      <v-tabs-window-item value="instances">
        <GeneratedReportsTab />
      </v-tabs-window-item>

      <!-- Scheduled Reports Tab -->
      <v-tabs-window-item value="scheduled">
        <ScheduledReportsTab />
      </v-tabs-window-item>

      <!-- Usage Analytics Tab -->
      <v-tabs-window-item value="analytics">
        <ReportsAnalyticsTab />
      </v-tabs-window-item>
    </v-tabs-window>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ReportsDashboardTab from '@/components/reports/ReportsDashboardTab.vue'
import PrebuiltReportsTab from '@/components/reports/PrebuiltReportsTab.vue'
import ReportTemplatesTab from '@/components/reports/ReportTemplatesTab.vue'
import GeneratedReportsTab from '@/components/reports/GeneratedReportsTab.vue'
import ScheduledReportsTab from '@/components/reports/ScheduledReportsTab.vue'
import ReportsAnalyticsTab from '@/components/reports/ReportsAnalyticsTab.vue'

// State
const activeTab = ref('dashboard')
</script>
