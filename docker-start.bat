@echo off
echo Starting RPS Management System - Docker Mode
echo.

cd /d "c:\laragon\www\rpswebid"

echo [1/3] Building and starting containers...
docker-compose up -d --build

echo [2/3] Waiting for services to be ready...
timeout /t 10 /nobreak >nul

echo [3/3] Opening applications...
start http://localhost:80
start http://localhost:5050

echo.
echo ✅ Docker services started:
echo    Application: http://localhost:80
echo    pgAdmin:     http://localhost:5050
echo    Redis UI:    http://localhost:8081
echo.
echo To stop: docker-compose down
pause