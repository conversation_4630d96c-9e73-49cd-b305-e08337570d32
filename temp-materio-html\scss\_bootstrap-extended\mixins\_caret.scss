// Carets - for dropdown arrows
// *******************************************************************************

@mixin caret-up($caret-width) {
  border: $caret-border-width solid;
  block-size: $caret-width;
  border-block-end: 0;
  border-inline-start: 0;
  inline-size: $caret-width;
  margin-block-start: .97 * divide($caret-width, 2);
  margin-inline: $caret-spacing 0;
  transform: rotate(-45deg);
}

@mixin caret-down($caret-width) {
  border: $caret-border-width solid;
  block-size: $caret-width;
  border-block-start: 0;
  border-inline-start: 0;
  inline-size: $caret-width;
  margin-block-start: -.5 * divide($caret-width, 2);
  margin-inline: $caret-spacing 0;
  transform: rotate(45deg);
}

@mixin caret-start($caret-width) {
  border: $caret-border-width solid;
  block-size: $caret-width;
  border-block-start: 0;
  border-inline-end: 0;
  inline-size: $caret-width;
  margin-block-start: 0;
  margin-inline: 0 $caret-spacing;
  transform: rotate(45deg);
}

@mixin caret-end($caret-width) {
  border: $caret-border-width solid;
  block-size: $caret-width;
  border-block-start: 0;
  border-inline-start: 0;
  inline-size: $caret-width;
  margin-block-start: 0;
  margin-inline: $caret-spacing 0;
  transform: rotate(-45deg);
}
