{"url": "http://rpswebid.me/index.php/api/v1/auth/login", "method": "POST", "isAJAX": false, "startTime": **********.666136, "totalTime": 50.7, "totalMemory": "5.979", "segmentDuration": 10, "segmentCount": 6, "CI_VERSION": "4.6.1", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.671191, "duration": 0.****************}, {"name": "Required Before Filters", "component": "Timer", "start": **********.691548, "duration": 0.0028848648071289062}, {"name": "Routing", "component": "Timer", "start": **********.694439, "duration": 0.0011131763458251953}, {"name": "Before Filters", "component": "Timer", "start": **********.696148, "duration": 7.319450378417969e-05}, {"name": "Controller", "component": "Timer", "start": **********.69624, "duration": 0.020184040069580078}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.696241, "duration": 0.009786128997802734}, {"name": "After Filters", "component": "Timer", "start": **********.716437, "duration": 3.814697265625e-06}, {"name": "Required After Filters", "component": "Timer", "start": **********.716457, "duration": 0.0004470348358154297}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(0 total Query, 0  unique across 1 Connection)", "display": {"queries": []}, "badgeValue": 0, "isEmpty": true, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": 0, "duration": "0.000000"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": []}, "badgeValue": null, "isEmpty": true, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 0, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": []}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 160 )", "display": {"coreFiles": [{"path": "SYSTEMPATH\\API\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\Autoloader\\Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH\\BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH\\Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH\\Cache\\CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH\\Cache\\CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Cache\\ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH\\CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH\\Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH\\Config\\AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH\\Config\\DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH\\Config\\Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH\\Config\\Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH\\Config\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH\\Cookie\\CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH\\Database\\BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH\\Database\\Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH\\Database\\ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH\\Database\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH\\Debug\\Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH\\Debug\\Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH\\Events\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Filters\\DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH\\Filters\\FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH\\Filters\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Filters\\ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH\\Filters\\PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH\\Filters\\PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH\\Format\\Format.php", "name": "Format.php"}, {"path": "SYSTEMPATH\\Format\\FormatterInterface.php", "name": "FormatterInterface.php"}, {"path": "SYSTEMPATH\\Format\\JSONFormatter.php", "name": "JSONFormatter.php"}, {"path": "SYSTEMPATH\\HTTP\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH\\HTTP\\Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH\\HTTP\\IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH\\HTTP\\URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH\\HTTP\\UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH\\Helpers\\Array\\ArrayHelper.php", "name": "ArrayHelper.php"}, {"path": "SYSTEMPATH\\Helpers\\array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\filesystem_helper.php", "name": "filesystem_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH\\Language\\Language.php", "name": "Language.php"}, {"path": "SYSTEMPATH\\Language\\en\\Validation.php", "name": "Validation.php"}, {"path": "SYSTEMPATH\\Log\\Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH\\Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH\\Modules\\Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH\\RESTful\\BaseResource.php", "name": "BaseResource.php"}, {"path": "SYSTEMPATH\\RESTful\\ResourceController.php", "name": "ResourceController.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH\\Router\\Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH\\Router\\RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH\\Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init.php", "name": "init.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH\\Validation\\CreditCardRules.php", "name": "CreditCardRules.php"}, {"path": "SYSTEMPATH\\Validation\\FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH\\Validation\\Rules.php", "name": "Rules.php"}, {"path": "SYSTEMPATH\\Validation\\StrictRules\\CreditCardRules.php", "name": "CreditCardRules.php"}, {"path": "SYSTEMPATH\\Validation\\StrictRules\\FileRules.php", "name": "FileRules.php"}, {"path": "SYSTEMPATH\\Validation\\StrictRules\\FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH\\Validation\\StrictRules\\Rules.php", "name": "Rules.php"}, {"path": "SYSTEMPATH\\Validation\\Validation.php", "name": "Validation.php"}, {"path": "SYSTEMPATH\\Validation\\ValidationInterface.php", "name": "ValidationInterface.php"}, {"path": "SYSTEMPATH\\View\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\View\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\View\\ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}, {"path": "SYSTEMPATH\\rewrite.php", "name": "rewrite.php"}], "userFiles": [{"path": "APPPATH\\Common.php", "name": "Common.php"}, {"path": "APPPATH\\Config\\App.php", "name": "App.php"}, {"path": "APPPATH\\Config\\Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH\\Config\\Boot\\development.php", "name": "development.php"}, {"path": "APPPATH\\Config\\Cache.php", "name": "Cache.php"}, {"path": "APPPATH\\Config\\Constants.php", "name": "Constants.php"}, {"path": "APPPATH\\Config\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH\\Config\\Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH\\Config\\Database.php", "name": "Database.php"}, {"path": "APPPATH\\Config\\Events.php", "name": "Events.php"}, {"path": "APPPATH\\Config\\Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH\\Config\\Feature.php", "name": "Feature.php"}, {"path": "APPPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "APPPATH\\Config\\Format.php", "name": "Format.php"}, {"path": "APPPATH\\Config\\Kint.php", "name": "Kint.php"}, {"path": "APPPATH\\Config\\Logger.php", "name": "Logger.php"}, {"path": "APPPATH\\Config\\Modules.php", "name": "Modules.php"}, {"path": "APPPATH\\Config\\Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH\\Config\\Paths.php", "name": "Paths.php"}, {"path": "APPPATH\\Config\\Routes.php", "name": "Routes.php"}, {"path": "APPPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "APPPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "APPPATH\\Config\\Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH\\Config\\UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH\\Config\\Validation.php", "name": "Validation.php"}, {"path": "APPPATH\\Config\\View.php", "name": "View.php"}, {"path": "APPPATH\\Controllers\\Api\\AuthController.php", "name": "AuthController.php"}, {"path": "APPPATH\\Controllers\\Api\\BaseApiController.php", "name": "BaseApiController.php"}, {"path": "APPPATH\\Language\\en\\Validation.php", "name": "Validation.php"}, {"path": "APPPATH\\Models\\UserModel.php", "name": "UserModel.php"}, {"path": "FCPATH\\index.php", "name": "index.php"}, {"path": "VENDORPATH\\autoload.php", "name": "autoload.php"}, {"path": "VENDORPATH\\composer\\ClassLoader.php", "name": "ClassLoader.php"}, {"path": "VENDORPATH\\composer\\InstalledVersions.php", "name": "InstalledVersions.php"}, {"path": "VENDORPATH\\composer\\autoload_real.php", "name": "autoload_real.php"}, {"path": "VENDORPATH\\composer\\autoload_static.php", "name": "autoload_static.php"}, {"path": "VENDORPATH\\composer\\installed.php", "name": "installed.php"}, {"path": "VENDORPATH\\composer\\platform_check.php", "name": "platform_check.php"}, {"path": "VENDORPATH\\laminas\\laminas-escaper\\src\\Escaper.php", "name": "Escaper.php"}, {"path": "VENDORPATH\\laminas\\laminas-escaper\\src\\EscaperInterface.php", "name": "EscaperInterface.php"}, {"path": "VENDORPATH\\myclabs\\deep-copy\\src\\DeepCopy\\deep_copy.php", "name": "deep_copy.php"}, {"path": "VENDORPATH\\phpunit\\phpunit\\src\\Framework\\Assert\\Functions.php", "name": "Functions.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LogLevel.php", "name": "LogLevel.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "VENDORPATH\\symfony\\deprecation-contracts\\function.php", "name": "function.php"}]}, "badgeValue": 160, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Api\\AuthController", "method": "login", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "frontend/assets/(.*)", "handler": "\\App\\Controllers\\Frontend::assets/$1"}, {"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Frontend::index"}, {"method": "GET", "route": "login", "handler": "\\App\\Controllers\\Frontend::index"}, {"method": "GET", "route": "dashboard", "handler": "\\App\\Controllers\\Frontend::index"}, {"method": "GET", "route": "courses", "handler": "\\App\\Controllers\\Frontend::index"}, {"method": "GET", "route": "courses/(.*)", "handler": "\\App\\Controllers\\Frontend::index"}, {"method": "GET", "route": "cpmk", "handler": "\\App\\Controllers\\Frontend::index"}, {"method": "GET", "route": "cpl", "handler": "\\App\\Controllers\\Frontend::index"}, {"method": "GET", "route": "assessments", "handler": "\\App\\Controllers\\Frontend::index"}, {"method": "GET", "route": "reports", "handler": "\\App\\Controllers\\Frontend::index"}, {"method": "GET", "route": "users", "handler": "\\App\\Controllers\\Frontend::index"}, {"method": "GET", "route": "faculties", "handler": "\\App\\Controllers\\Frontend::index"}, {"method": "GET", "route": "study-programs", "handler": "\\App\\Controllers\\Frontend::index"}, {"method": "GET", "route": "profile", "handler": "\\App\\Controllers\\Frontend::index"}, {"method": "GET", "route": "api/v1/auth/profile", "handler": "\\App\\Controllers\\Api\\AuthController::profile"}, {"method": "GET", "route": "api/v1/users", "handler": "\\App\\Controllers\\Api\\UserController::index"}, {"method": "GET", "route": "api/v1/users/new", "handler": "\\App\\Controllers\\Api\\UserController::new"}, {"method": "GET", "route": "api/v1/users/(.*)/edit", "handler": "\\App\\Controllers\\Api\\UserController::edit/$1"}, {"method": "GET", "route": "api/v1/users/(.*)", "handler": "\\App\\Controllers\\Api\\UserController::show/$1"}, {"method": "GET", "route": "api/v1/faculties", "handler": "\\App\\Controllers\\Api\\FacultyController::index"}, {"method": "GET", "route": "api/v1/faculties/new", "handler": "\\App\\Controllers\\Api\\FacultyController::new"}, {"method": "GET", "route": "api/v1/faculties/(.*)/edit", "handler": "\\App\\Controllers\\Api\\FacultyController::edit/$1"}, {"method": "GET", "route": "api/v1/faculties/(.*)", "handler": "\\App\\Controllers\\Api\\FacultyController::show/$1"}, {"method": "GET", "route": "api/v1/study-programs", "handler": "\\App\\Controllers\\Api\\StudyProgramController::index"}, {"method": "GET", "route": "api/v1/study-programs/new", "handler": "\\App\\Controllers\\Api\\StudyProgramController::new"}, {"method": "GET", "route": "api/v1/study-programs/(.*)/edit", "handler": "\\App\\Controllers\\Api\\StudyProgramController::edit/$1"}, {"method": "GET", "route": "api/v1/study-programs/(.*)", "handler": "\\App\\Controllers\\Api\\StudyProgramController::show/$1"}, {"method": "GET", "route": "api/v1/courses", "handler": "\\App\\Controllers\\Api\\CourseController::index"}, {"method": "GET", "route": "api/v1/courses/new", "handler": "\\App\\Controllers\\Api\\CourseController::new"}, {"method": "GET", "route": "api/v1/courses/(.*)/edit", "handler": "\\App\\Controllers\\Api\\CourseController::edit/$1"}, {"method": "GET", "route": "api/v1/courses/(.*)", "handler": "\\App\\Controllers\\Api\\CourseController::show/$1"}, {"method": "GET", "route": "api/v1/courses/([0-9]+)/references", "handler": "\\App\\Controllers\\Api\\CourseController::getReferences/$1"}, {"method": "GET", "route": "api/v1/courses/([0-9]+)/topics", "handler": "\\App\\Controllers\\Api\\CourseController::getTopics/$1"}, {"method": "GET", "route": "api/v1/cpmk", "handler": "\\App\\Controllers\\Api\\CpmkController::index"}, {"method": "GET", "route": "api/v1/cpmk/new", "handler": "\\App\\Controllers\\Api\\CpmkController::new"}, {"method": "GET", "route": "api/v1/cpmk/(.*)/edit", "handler": "\\App\\Controllers\\Api\\CpmkController::edit/$1"}, {"method": "GET", "route": "api/v1/cpmk/(.*)", "handler": "\\App\\Controllers\\Api\\CpmkController::show/$1"}, {"method": "GET", "route": "api/v1/cpmk/([0-9]+)/sub-cpmk", "handler": "\\App\\Controllers\\Api\\CpmkController::getSubCpmk/$1"}, {"method": "GET", "route": "api/v1/cpmk/([0-9]+)/cpl-relations", "handler": "\\App\\Controllers\\Api\\CpmkController::getCplRelations/$1"}, {"method": "GET", "route": "api/v1/cpl", "handler": "\\App\\Controllers\\Api\\CplController::index"}, {"method": "GET", "route": "api/v1/cpl/new", "handler": "\\App\\Controllers\\Api\\CplController::new"}, {"method": "GET", "route": "api/v1/cpl/(.*)/edit", "handler": "\\App\\Controllers\\Api\\CplController::edit/$1"}, {"method": "GET", "route": "api/v1/cpl/(.*)", "handler": "\\App\\Controllers\\Api\\CplController::show/$1"}, {"method": "GET", "route": "api/v1/assessments/methods", "handler": "\\App\\Controllers\\Api\\AssessmentMethodController::index"}, {"method": "GET", "route": "api/v1/assessments/methods/new", "handler": "\\App\\Controllers\\Api\\AssessmentMethodController::new"}, {"method": "GET", "route": "api/v1/assessments/methods/(.*)/edit", "handler": "\\App\\Controllers\\Api\\AssessmentMethodController::edit/$1"}, {"method": "GET", "route": "api/v1/assessments/methods/(.*)", "handler": "\\App\\Controllers\\Api\\AssessmentMethodController::show/$1"}, {"method": "GET", "route": "api/v1/assessments/plans", "handler": "\\App\\Controllers\\Api\\AssessmentPlanController::index"}, {"method": "GET", "route": "api/v1/assessments/plans/new", "handler": "\\App\\Controllers\\Api\\AssessmentPlanController::new"}, {"method": "GET", "route": "api/v1/assessments/plans/(.*)/edit", "handler": "\\App\\Controllers\\Api\\AssessmentPlanController::edit/$1"}, {"method": "GET", "route": "api/v1/assessments/plans/(.*)", "handler": "\\App\\Controllers\\Api\\AssessmentPlanController::show/$1"}, {"method": "GET", "route": "api/v1/reports/cpmk-achievement", "handler": "\\App\\Controllers\\Api\\ReportController::cpmkAchievement"}, {"method": "GET", "route": "api/v1/reports/cpl-mapping", "handler": "\\App\\Controllers\\Api\\ReportController::cplMapping"}, {"method": "GET", "route": "api/v1/reports/dashboard", "handler": "\\App\\Controllers\\Api\\ReportController::dashboard"}, {"method": "GET", "route": "api/v1/dashboard/stats", "handler": "\\App\\Controllers\\Api\\DashboardController::getStats"}, {"method": "GET", "route": "(.*)", "handler": "\\App\\Controllers\\Frontend::index"}, {"method": "POST", "route": "api/v1/auth/login", "handler": "\\App\\Controllers\\Api\\AuthController::login"}, {"method": "POST", "route": "api/v1/auth/logout", "handler": "\\App\\Controllers\\Api\\AuthController::logout"}, {"method": "POST", "route": "api/v1/auth/refresh", "handler": "\\App\\Controllers\\Api\\AuthController::refresh"}, {"method": "POST", "route": "api/v1/users", "handler": "\\App\\Controllers\\Api\\UserController::create"}, {"method": "POST", "route": "api/v1/faculties", "handler": "\\App\\Controllers\\Api\\FacultyController::create"}, {"method": "POST", "route": "api/v1/study-programs", "handler": "\\App\\Controllers\\Api\\StudyProgramController::create"}, {"method": "POST", "route": "api/v1/courses", "handler": "\\App\\Controllers\\Api\\CourseController::create"}, {"method": "POST", "route": "api/v1/courses/([0-9]+)/references", "handler": "\\App\\Controllers\\Api\\CourseController::addReference/$1"}, {"method": "POST", "route": "api/v1/courses/([0-9]+)/topics", "handler": "\\App\\Controllers\\Api\\CourseController::addTopic/$1"}, {"method": "POST", "route": "api/v1/cpmk", "handler": "\\App\\Controllers\\Api\\CpmkController::create"}, {"method": "POST", "route": "api/v1/cpmk/([0-9]+)/sub-cpmk", "handler": "\\App\\Controllers\\Api\\CpmkController::addSubCpmk/$1"}, {"method": "POST", "route": "api/v1/cpmk/([0-9]+)/cpl-relations", "handler": "\\App\\Controllers\\Api\\CpmkController::addCplRelation/$1"}, {"method": "POST", "route": "api/v1/cpl", "handler": "\\App\\Controllers\\Api\\CplController::create"}, {"method": "POST", "route": "api/v1/assessments/methods", "handler": "\\App\\Controllers\\Api\\AssessmentMethodController::create"}, {"method": "POST", "route": "api/v1/assessments/plans", "handler": "\\App\\Controllers\\Api\\AssessmentPlanController::create"}, {"method": "PATCH", "route": "api/v1/users/(.*)", "handler": "\\App\\Controllers\\Api\\UserController::update/$1"}, {"method": "PATCH", "route": "api/v1/faculties/(.*)", "handler": "\\App\\Controllers\\Api\\FacultyController::update/$1"}, {"method": "PATCH", "route": "api/v1/study-programs/(.*)", "handler": "\\App\\Controllers\\Api\\StudyProgramController::update/$1"}, {"method": "PATCH", "route": "api/v1/courses/(.*)", "handler": "\\App\\Controllers\\Api\\CourseController::update/$1"}, {"method": "PATCH", "route": "api/v1/cpmk/(.*)", "handler": "\\App\\Controllers\\Api\\CpmkController::update/$1"}, {"method": "PATCH", "route": "api/v1/cpl/(.*)", "handler": "\\App\\Controllers\\Api\\CplController::update/$1"}, {"method": "PATCH", "route": "api/v1/assessments/methods/(.*)", "handler": "\\App\\Controllers\\Api\\AssessmentMethodController::update/$1"}, {"method": "PATCH", "route": "api/v1/assessments/plans/(.*)", "handler": "\\App\\Controllers\\Api\\AssessmentPlanController::update/$1"}, {"method": "PUT", "route": "api/v1/users/(.*)", "handler": "\\App\\Controllers\\Api\\UserController::update/$1"}, {"method": "PUT", "route": "api/v1/faculties/(.*)", "handler": "\\App\\Controllers\\Api\\FacultyController::update/$1"}, {"method": "PUT", "route": "api/v1/study-programs/(.*)", "handler": "\\App\\Controllers\\Api\\StudyProgramController::update/$1"}, {"method": "PUT", "route": "api/v1/courses/(.*)", "handler": "\\App\\Controllers\\Api\\CourseController::update/$1"}, {"method": "PUT", "route": "api/v1/cpmk/(.*)", "handler": "\\App\\Controllers\\Api\\CpmkController::update/$1"}, {"method": "PUT", "route": "api/v1/cpl/(.*)", "handler": "\\App\\Controllers\\Api\\CplController::update/$1"}, {"method": "PUT", "route": "api/v1/assessments/methods/(.*)", "handler": "\\App\\Controllers\\Api\\AssessmentMethodController::update/$1"}, {"method": "PUT", "route": "api/v1/assessments/plans/(.*)", "handler": "\\App\\Controllers\\Api\\AssessmentPlanController::update/$1"}, {"method": "DELETE", "route": "api/v1/users/(.*)", "handler": "\\App\\Controllers\\Api\\UserController::delete/$1"}, {"method": "DELETE", "route": "api/v1/faculties/(.*)", "handler": "\\App\\Controllers\\Api\\FacultyController::delete/$1"}, {"method": "DELETE", "route": "api/v1/study-programs/(.*)", "handler": "\\App\\Controllers\\Api\\StudyProgramController::delete/$1"}, {"method": "DELETE", "route": "api/v1/courses/(.*)", "handler": "\\App\\Controllers\\Api\\CourseController::delete/$1"}, {"method": "DELETE", "route": "api/v1/cpmk/(.*)", "handler": "\\App\\Controllers\\Api\\CpmkController::delete/$1"}, {"method": "DELETE", "route": "api/v1/cpl/(.*)", "handler": "\\App\\Controllers\\Api\\CplController::delete/$1"}, {"method": "DELETE", "route": "api/v1/assessments/methods/(.*)", "handler": "\\App\\Controllers\\Api\\AssessmentMethodController::delete/$1"}, {"method": "DELETE", "route": "api/v1/assessments/plans/(.*)", "handler": "\\App\\Controllers\\Api\\AssessmentPlanController::delete/$1"}]}, "badgeValue": 15, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "7.95", "count": 1}}}, "badgeValue": 1, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.683588, "duration": 0.007951021194458008}]}], "vars": {"varData": {"View Data": []}, "headers": {"Content-Type": "application/json", "Host": "localhost:8080", "Accept": "application/json", "Content-Length": "37"}, "request": "HTTP/1.1", "response": {"statusCode": 422, "reason": "Unprocessable Content", "contentType": "application/json; charset=UTF-8", "headers": {"Cache-Control": "no-store, max-age=0, no-cache", "Content-Type": "application/json; charset=UTF-8"}}}, "config": {"ciVersion": "4.6.1", "phpVersion": "8.1.10", "phpSAPI": "cli-server", "environment": "development", "baseURL": "http://rpswebid.me/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}