<?php

namespace App\Controllers;

use CodeIgniter\Controller;

class Frontend extends Controller
{
    /**
     * Serve the Vue.js frontend application
     * This method handles all frontend routes and serves the SPA
     */
    public function index()
    {
        // Check if we're in development mode and frontend dist exists
        $frontendDistPath = ROOTPATH . '../frontend/dist';
        $frontendIndexPath = $frontendDistPath . '/index.html';
        
        // If frontend dist doesn't exist, show development message
        if (!file_exists($frontendIndexPath)) {
            return $this->showDevelopmentMessage();
        }
        
        // Serve the Vue.js application
        $indexContent = file_get_contents($frontendIndexPath);
        
        // Update asset paths to work with CodeIgniter
        $baseURL = base_url();
        $indexContent = str_replace(
            ['/assets/', 'href="/favicon.ico"'],
            [$baseURL . 'frontend/assets/', 'href="' . $baseURL . 'frontend/favicon.ico"'],
            $indexContent
        );
        
        // Set proper content type
        $this->response->setContentType('text/html');
        
        return $this->response->setBody($indexContent);
    }
    
    /**
     * Show development message when frontend is not built
     */
    private function showDevelopmentMessage()
    {
        $data = [
            'title' => 'RPS Management System',
            'message' => 'Frontend Development Mode',
            'description' => 'The Vue.js frontend needs to be built. Please run: npm run build in the frontend directory.',
            'api_status' => 'Backend API is running',
            'base_url' => base_url(),
        ];
        
        return view('development/frontend_message', $data);
    }
    
    /**
     * Serve frontend assets (CSS, JS, images)
     */
    public function assets($path = '')
    {
        $frontendDistPath = ROOTPATH . '../frontend/dist';
        $assetPath = $frontendDistPath . '/assets/' . $path;
        
        if (!file_exists($assetPath)) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }
        
        // Determine content type based on file extension
        $extension = pathinfo($assetPath, PATHINFO_EXTENSION);
        $contentTypes = [
            'css' => 'text/css',
            'js' => 'application/javascript',
            'png' => 'image/png',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'gif' => 'image/gif',
            'svg' => 'image/svg+xml',
            'ico' => 'image/x-icon',
            'woff' => 'font/woff',
            'woff2' => 'font/woff2',
            'ttf' => 'font/ttf',
            'eot' => 'application/vnd.ms-fontobject',
        ];
        
        $contentType = $contentTypes[$extension] ?? 'application/octet-stream';
        
        // Set appropriate headers
        $this->response->setContentType($contentType);
        $this->response->setHeader('Cache-Control', 'public, max-age=31536000'); // 1 year cache
        
        return $this->response->setBody(file_get_contents($assetPath));
    }
}
