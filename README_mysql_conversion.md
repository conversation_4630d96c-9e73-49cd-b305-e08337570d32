# RPS Management System - MySQL Conversion

## 📋 Overview

File `database_design_mysql_new.sql` adalah konversi dari `database_design.sql` (PostgreSQL) ke MySQL dengan struktur yang identik dan optimisasi MySQL-specific.

## 🔄 Conversion Summary

| Aspect | PostgreSQL Original | MySQL Converted |
|--------|-------------------|-----------------|
| **File Size** | 302 lines | 506 lines |
| **Tables** | 11 core tables | 11 core tables |
| **Views** | 0 | 3 views |
| **Data Seeding** | Basic | Enhanced |
| **Indexes** | Separate statements | Inline definitions |

## 🏗️ **Database Structure (11 Core Tables)**

### **1. Master Data Tables (4 tables)**
- `roles` - User roles dengan JSON permissions
- `users` - User accounts dan profiles
- `faculties` - Faculty information
- `study_programs` - Study program details

### **2. Curriculum Tables (3 tables)**
- `cpl` - Graduate Learning Outcomes (Capaian <PERSON><PERSON>)
- `courses` - Course information dengan prerequisites
- `course_references` - Course bibliography
- `course_topics` - Weekly course topics

### **3. CPMK System (3 tables)**
- `cpmk` - Course Learning Outcomes
- `cpmk_cpl_relations` - CPMK-CPL mappings
- `sub_cpmk` - Sub-course learning outcomes

### **4. Assessment System (2 tables)**
- `assessment_methods` - Assessment types library
- `assessment_plans` - Comprehensive assessment planning

## 🔧 **Key MySQL Conversions**

### **1. Data Types**
```sql
-- PostgreSQL
id SERIAL PRIMARY KEY
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP

-- MySQL
`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY
`created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
```

### **2. Identifiers**
```sql
-- PostgreSQL
CREATE TABLE users (
    username VARCHAR(100) NOT NULL UNIQUE
);

-- MySQL
CREATE TABLE `users` (
    `username` VARCHAR(100) NOT NULL UNIQUE
);
```

### **3. Indexes**
```sql
-- PostgreSQL (separate statements)
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);

-- MySQL (inline definitions)
INDEX `idx_users_username` (`username`),
INDEX `idx_users_email` (`email`),
```

### **4. Foreign Keys**
```sql
-- PostgreSQL
CONSTRAINT fk_users_role 
    FOREIGN KEY (role_id) REFERENCES roles(id) 
    ON DELETE SET NULL

-- MySQL
CONSTRAINT `fk_users_role` 
    FOREIGN KEY (`role_id`) REFERENCES `roles`(`id`) 
    ON DELETE SET NULL ON UPDATE CASCADE
```

### **5. Engine & Charset**
```sql
-- MySQL specific
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## 📊 **Enhanced Features in MySQL Version**

### **1. Comprehensive Indexing**
- **Primary keys**: All tables dengan AUTO_INCREMENT
- **Foreign key indexes**: Semua foreign keys ter-index
- **Business logic indexes**: Semester, course_type, cognitive_level, dll
- **Composite unique keys**: Untuk business constraints

### **2. Data Validation**
```sql
-- Check constraints untuk data integrity
CHECK (`semester` BETWEEN 1 AND 8)
CHECK (`credits` > 0)
CHECK (`weight_percentage` > 0 AND `weight_percentage` <= 100)
CHECK (`week_number` BETWEEN 1 AND 16)
```

### **3. JSON Support**
```sql
-- Flexible data structures
`prerequisite_courses` JSON NULL COMMENT 'Array of course IDs'
`week_coverage` JSON NULL COMMENT 'Array of week numbers'
`rubric_criteria` JSON NULL COMMENT 'Assessment rubric details'
`permissions` JSON NULL
```

### **4. Enhanced Data Seeding**
```sql
-- Default roles
INSERT INTO `roles` (`name`, `description`, `permissions`) VALUES
('Super Admin', 'Full system access', JSON_OBJECT('all', true)),
('Admin Fakultas', 'Faculty level administration', JSON_OBJECT('faculty', true, 'study_programs', true)),
('Koordinator Prodi', 'Study program coordination', JSON_OBJECT('study_program', true, 'courses', true)),
('Dosen', 'Course management and teaching', JSON_OBJECT('courses', true, 'assessments', true)),
('Mahasiswa', 'Student access for viewing', JSON_OBJECT('view_only', true));

-- Assessment methods
INSERT INTO `assessment_methods` (`name`, `description`, `type`, `category`) VALUES
('Tugas Individu', 'Tugas yang dikerjakan secara individu', 'formatif', 'tugas'),
('Kuis Mingguan', 'Kuis singkat setiap minggu', 'formatif', 'kuis'),
('Ujian Tengah Semester', 'Ujian tengah semester', 'sumatif', 'uts'),
('Ujian Akhir Semester', 'Ujian akhir semester', 'sumatif', 'uas'),
-- ... dan lainnya
```

### **5. Useful Views**
```sql
-- Course details dengan study program info
CREATE VIEW `v_course_details` AS
SELECT c.`id`, c.`code`, c.`name`, sp.`name` as `study_program_name`,
       f.`name` as `faculty_name`, u.`full_name` as `coordinator_name`
FROM `courses` c
JOIN `study_programs` sp ON c.`study_program_id` = sp.`id`
JOIN `faculties` f ON sp.`faculty_id` = f.`id`
LEFT JOIN `users` u ON c.`coordinator_id` = u.`id`;

-- CPMK-CPL relationship details
CREATE VIEW `v_cpmk_cpl_details` AS
SELECT cpmk.`code` as `cpmk_code`, cpl.`code` as `cpl_code`,
       ccr.`contribution_level`, ccr.`weight_percentage`
FROM `cpmk`
LEFT JOIN `cpmk_cpl_relations` ccr ON cpmk.`id` = ccr.`cpmk_id`
LEFT JOIN `cpl` ON ccr.`cpl_id` = cpl.`id`;

-- Assessment plan details
CREATE VIEW `v_assessment_plan_details` AS
SELECT ap.`assessment_title`, cpmk.`code` as `cpmk_code`,
       am.`name` as `assessment_method`, c.`name` as `course_name`
FROM `assessment_plans` ap
JOIN `cpmk` ON ap.`cpmk_id` = cpmk.`id`
JOIN `assessment_methods` am ON ap.`assessment_method_id` = am.`id`
JOIN `courses` c ON cpmk.`course_id` = c.`id`;
```

## 🚀 **Installation & Usage**

### **Prerequisites**
- MySQL 8.0+ (untuk JSON support dan CHECK constraints)
- Minimum 100MB storage space
- UTF8MB4 character set support

### **Installation**
```bash
# Install MySQL
sudo apt-get install mysql-server

# Create database dan run schema
mysql -u root -p < database_design_mysql_new.sql
```

### **Verification**
```sql
-- Check tables
SHOW TABLES;

-- Check views
SHOW FULL TABLES WHERE Table_type = 'VIEW';

-- Check data seeding
SELECT * FROM roles;
SELECT * FROM assessment_methods;

-- Test views
SELECT * FROM v_course_details LIMIT 5;
```

## 📈 **Performance Optimizations**

### **1. Index Strategy**
- **Primary keys**: AUTO_INCREMENT untuk performance
- **Foreign keys**: Semua FK ter-index untuk JOIN performance
- **Business queries**: Index pada kolom yang sering di-query
- **Composite indexes**: Untuk unique constraints

### **2. Storage Engine**
- **InnoDB**: ACID compliance, foreign key support, row-level locking
- **UTF8MB4**: Full Unicode support termasuk emoji
- **Collation**: Case-insensitive untuk user-friendly queries

### **3. Data Types**
- **INT UNSIGNED**: Untuk ID fields (4 billion max)
- **DECIMAL(5,2)**: Untuk percentage fields (precision)
- **ENUM**: Untuk fixed value lists (performance)
- **JSON**: Untuk flexible data structures

## 🎯 **Business Logic Features**

### **1. Academic Structure**
- **Hierarchical organization**: University → Faculty → Study Program → Course
- **Role-based access**: 5 distinct user roles dengan permissions
- **Academic integrity**: Proper constraints dan validations

### **2. Curriculum Management**
- **CPL management**: Graduate learning outcomes dengan categories
- **Course planning**: Comprehensive course information dengan prerequisites
- **Learning materials**: Course references dan weekly topics

### **3. CPMK System**
- **Learning outcomes**: Course-level learning outcomes dengan cognitive levels
- **CPL mapping**: CPMK-CPL relationships dengan contribution levels
- **Sub-CPMK**: Detailed learning indicators dengan week coverage

### **4. Assessment Framework**
- **Assessment methods**: Flexible assessment types dan categories
- **Assessment planning**: Comprehensive planning dengan rubrics
- **Weight management**: Percentage-based weight distribution

## 🔒 **Security Features**

### **1. Data Integrity**
- **Foreign key constraints**: Referential integrity
- **Check constraints**: Data validation
- **Unique constraints**: Business rule enforcement
- **NOT NULL constraints**: Required field validation

### **2. Access Control**
- **Role-based permissions**: JSON-based permission system
- **User management**: Comprehensive user profiles
- **Audit trail**: created_by dan timestamp tracking
- **Soft delete**: is_active flags untuk data preservation

## 🎯 **Next Steps**

1. **Test the schema** dengan sample data
2. **Create additional views** untuk reporting needs
3. **Add stored procedures** untuk complex calculations
4. **Implement triggers** untuk audit trail
5. **Setup backup strategy** untuk production

---

**Note:** File ini adalah konversi langsung dari PostgreSQL version dengan optimisasi MySQL-specific. Semua fungsionalitas core RPS Management System tetap dipertahankan dengan performance improvements untuk MySQL environment.
