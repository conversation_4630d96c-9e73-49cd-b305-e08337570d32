<?php
/**
 * Test script for Admin Dashboard
 * Run this script to test the admin dashboard functionality
 */

// Set the base URL for your CodeIgniter application
$baseUrl = 'http://localhost/rpswebid/backend/public';

echo "=== RPS Admin Dashboard Test ===\n\n";

// Test admin routes
$routes = [
    '/admin/login' => 'Admin Login Page',
    '/admin' => 'Admin Dashboard (requires auth)',
    '/admin/users' => 'Users Management',
    '/admin/courses' => 'Courses Management',
    '/admin/cpmk' => 'CPMK Management',
    '/admin/cpl' => 'CPL Management',
    '/admin/reports' => 'Reports Page',
    '/admin/settings' => 'Settings Page'
];

foreach ($routes as $route => $description) {
    echo "Testing: $description\n";
    echo "URL: $baseUrl$route\n";
    
    // Test if the route is accessible
    $headers = @get_headers($baseUrl . $route);
    if ($headers) {
        $status = $headers[0];
        echo "Status: $status\n";
        
        if (strpos($status, '200') !== false) {
            echo "✅ SUCCESS: Route is accessible\n";
        } elseif (strpos($status, '302') !== false) {
            echo "🔄 REDIRECT: Route redirects (expected for protected routes)\n";
        } else {
            echo "❌ ERROR: Route not accessible\n";
        }
    } else {
        echo "❌ ERROR: Could not connect to server\n";
    }
    echo "\n";
}

echo "=== File Structure Test ===\n\n";

// Test if required files exist
$requiredFiles = [
    'backend/app/Controllers/AdminController.php' => 'Admin Controller',
    'backend/app/Views/admin/layouts/main.php' => 'Main Layout',
    'backend/app/Views/admin/layouts/blank.php' => 'Blank Layout',
    'backend/app/Views/admin/components/sidebar.php' => 'Sidebar Component',
    'backend/app/Views/admin/components/navbar.php' => 'Navbar Component',
    'backend/app/Views/admin/components/footer.php' => 'Footer Component',
    'backend/app/Views/admin/dashboard/index.php' => 'Dashboard Page',
    'backend/app/Views/admin/auth/login.php' => 'Login Page',
    'backend/app/Views/admin/users/index.php' => 'Users Page',
    'backend/app/Views/admin/courses/index.php' => 'Courses Page',
    'backend/public/admin/assets/css/materio-vue.css' => 'Materio Vue CSS',
    'backend/public/admin/assets/css/materio-admin.css' => 'Custom Admin CSS',
    'backend/public/admin/assets/js/materio-admin.js' => 'Custom Admin JS'
];

foreach ($requiredFiles as $file => $description) {
    echo "Checking: $description\n";
    echo "File: $file\n";
    
    if (file_exists($file)) {
        echo "✅ EXISTS: File found\n";
        echo "Size: " . formatBytes(filesize($file)) . "\n";
    } else {
        echo "❌ MISSING: File not found\n";
    }
    echo "\n";
}

echo "=== Configuration Test ===\n\n";

// Test if routes are configured
$routesFile = 'backend/app/Config/Routes.php';
if (file_exists($routesFile)) {
    $routesContent = file_get_contents($routesFile);
    
    echo "Checking Routes Configuration:\n";
    if (strpos($routesContent, "routes->group('admin'") !== false) {
        echo "✅ Admin routes group found\n";
    } else {
        echo "❌ Admin routes group not found\n";
    }
    
    if (strpos($routesContent, 'AdminController') !== false) {
        echo "✅ AdminController referenced in routes\n";
    } else {
        echo "❌ AdminController not referenced in routes\n";
    }
} else {
    echo "❌ Routes.php file not found\n";
}

echo "\n=== Assets Test ===\n\n";

// Test if assets are accessible
$assetUrls = [
    '/admin/assets/css/materio-vue.css' => 'Materio Vue CSS',
    '/admin/assets/css/materio-admin.css' => 'Custom Admin CSS',
    '/admin/assets/js/materio-admin.js' => 'Custom Admin JS'
];

foreach ($assetUrls as $asset => $description) {
    echo "Testing: $description\n";
    echo "URL: $baseUrl$asset\n";
    
    $headers = @get_headers($baseUrl . $asset);
    if ($headers) {
        $status = $headers[0];
        echo "Status: $status\n";
        
        if (strpos($status, '200') !== false) {
            echo "✅ SUCCESS: Asset is accessible\n";
        } else {
            echo "❌ ERROR: Asset not accessible\n";
        }
    } else {
        echo "❌ ERROR: Could not connect to server\n";
    }
    echo "\n";
}

echo "=== Summary ===\n\n";
echo "Admin Dashboard Implementation Complete!\n\n";
echo "Next Steps:\n";
echo "1. Start your web server (Apache/Nginx)\n";
echo "2. Visit: $baseUrl/admin/login\n";
echo "3. Test the login functionality\n";
echo "4. Explore the admin dashboard features\n\n";
echo "Note: Some routes may redirect to login if authentication is required.\n";
echo "This is expected behavior for protected admin routes.\n\n";

function formatBytes($size, $precision = 2) {
    $base = log($size, 1024);
    $suffixes = array('B', 'KB', 'MB', 'GB', 'TB');
    return round(pow(1024, $base - floor($base)), $precision) . ' ' . $suffixes[floor($base)];
}

echo "Test completed at: " . date('Y-m-d H:i:s') . "\n";
?>
