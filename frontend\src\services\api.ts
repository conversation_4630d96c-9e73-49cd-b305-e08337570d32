import axios, { type AxiosInstance, type AxiosResponse, type AxiosError } from 'axios'
import { useAuthStore } from '@/stores/auth'

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api/v1',
  timeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || 30000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('rps_auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // Add request timestamp for debugging
    if (import.meta.env.VITE_DEBUG_MODE === 'true') {
      console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, config.data)
    }
    
    return config
  },
  (error) => {
    console.error('[API Request Error]', error)
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response: AxiosResponse) => {
    // Log successful responses in debug mode
    if (import.meta.env.VITE_DEBUG_MODE === 'true') {
      console.log(`[API Response] ${response.config.method?.toUpperCase()} ${response.config.url}`, response.data)
    }
    
    return response
  },
  async (error: AxiosError) => {
    const authStore = useAuthStore()
    
    // Handle 401 Unauthorized
    if (error.response?.status === 401) {
      // Try to refresh token
      try {
        await authStore.refreshAuthToken()
        // Retry the original request
        if (error.config) {
          return api.request(error.config)
        }
      } catch (refreshError) {
        // Refresh failed, logout user
        console.error('Token refresh failed:', refreshError)
        await authStore.logout()
        window.location.href = '/login'
      }
    }
    
    // Handle 403 Forbidden
    if (error.response?.status === 403) {
      console.error('[API] Access forbidden:', error.response.data)
      // Could show a toast notification here
    }
    
    // Handle 500 Server Error
    if (error.response?.status === 500) {
      console.error('[API] Server error:', error.response.data)
      // Could show a toast notification here
    }
    
    // Log errors in debug mode
    if (import.meta.env.VITE_DEBUG_MODE === 'true') {
      console.error('[API Response Error]', error.response?.data || error.message)
    }
    
    return Promise.reject(error)
  }
)

// API service methods
export const apiService = {
  // Generic CRUD operations
  get: <T = any>(url: string, params?: any): Promise<AxiosResponse<T>> => 
    api.get(url, { params }),
    
  post: <T = any>(url: string, data?: any): Promise<AxiosResponse<T>> => 
    api.post(url, data),
    
  put: <T = any>(url: string, data?: any): Promise<AxiosResponse<T>> => 
    api.put(url, data),
    
  patch: <T = any>(url: string, data?: any): Promise<AxiosResponse<T>> => 
    api.patch(url, data),
    
  delete: <T = any>(url: string): Promise<AxiosResponse<T>> => 
    api.delete(url),

  // File upload
  upload: <T = any>(url: string, formData: FormData): Promise<AxiosResponse<T>> => 
    api.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }),

  // Download file
  download: (url: string, filename?: string): Promise<void> => 
    api.get(url, { responseType: 'blob' }).then(response => {
      const blob = new Blob([response.data])
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = filename || 'download'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
    }),
}

// Specific API endpoints
export const authAPI = {
  login: (credentials: any) => apiService.post('/auth/login', credentials),
  logout: () => apiService.post('/auth/logout'),
  refresh: (refreshToken: string) => apiService.post('/auth/refresh', { refresh_token: refreshToken }),
  profile: () => apiService.get('/auth/profile'),
  updateProfile: (data: any) => apiService.put('/auth/profile', data),
  changePassword: (data: any) => apiService.put('/auth/change-password', data),
}

export const dashboardAPI = {
  getStats: () => apiService.get('/dashboard/stats'),
  getChartData: (type: string) => apiService.get(`/dashboard/charts/${type}`),
  getRecentActivities: () => apiService.get('/dashboard/activities'),
}

export const usersAPI = {
  getAll: (params?: any) => apiService.get('/users', params),
  getById: (id: number) => apiService.get(`/users/${id}`),
  create: (data: any) => apiService.post('/users', data),
  update: (id: number, data: any) => apiService.put(`/users/${id}`, data),
  delete: (id: number) => apiService.delete(`/users/${id}`),
}

export const facultiesAPI = {
  getAll: (params?: any) => apiService.get('/faculties', params),
  getById: (id: number) => apiService.get(`/faculties/${id}`),
  create: (data: any) => apiService.post('/faculties', data),
  update: (id: number, data: any) => apiService.put(`/faculties/${id}`, data),
  delete: (id: number) => apiService.delete(`/faculties/${id}`),
}

export const studyProgramsAPI = {
  getAll: (params?: any) => apiService.get('/study-programs', params),
  getById: (id: number) => apiService.get(`/study-programs/${id}`),
  create: (data: any) => apiService.post('/study-programs', data),
  update: (id: number, data: any) => apiService.put(`/study-programs/${id}`, data),
  delete: (id: number) => apiService.delete(`/study-programs/${id}`),
}

export const coursesAPI = {
  getAll: (params?: any) => apiService.get('/courses', params),
  getById: (id: number) => apiService.get(`/courses/${id}`),
  create: (data: any) => apiService.post('/courses', data),
  update: (id: number, data: any) => apiService.put(`/courses/${id}`, data),
  delete: (id: number) => apiService.delete(`/courses/${id}`),

  // References
  getReferences: (id: number) => apiService.get(`/courses/${id}/references`),
  createReference: (courseId: number, data: any) => apiService.post(`/courses/${courseId}/references`, data),
  updateReference: (courseId: number, refId: number, data: any) => apiService.put(`/courses/${courseId}/references/${refId}`, data),
  deleteReference: (courseId: number, refId: number) => apiService.delete(`/courses/${courseId}/references/${refId}`),

  // Topics
  getTopics: (id: number) => apiService.get(`/courses/${id}/topics`),
  createTopic: (courseId: number, data: any) => apiService.post(`/courses/${courseId}/topics`, data),
  updateTopic: (courseId: number, topicId: number, data: any) => apiService.put(`/courses/${courseId}/topics/${topicId}`, data),
  deleteTopic: (courseId: number, topicId: number) => apiService.delete(`/courses/${courseId}/topics/${topicId}`),

  // CPMK
  getCPMK: (id: number) => apiService.get(`/courses/${id}/cpmk`),
}

export const cplAPI = {
  getAll: (params?: any) => apiService.get('/cpl', params),
  getById: (id: number) => apiService.get(`/cpl/${id}`),
  create: (data: any) => apiService.post('/cpl', data),
  update: (id: number, data: any) => apiService.put(`/cpl/${id}`, data),
  delete: (id: number) => apiService.delete(`/cpl/${id}`),
  getByStudyProgram: (studyProgramId: number) => apiService.get(`/study-programs/${studyProgramId}/cpl`),
  getMappingMatrix: (studyProgramId: number) => apiService.get(`/study-programs/${studyProgramId}/cpl-mapping-matrix`),
  getAchievementReport: (studyProgramId: number, params?: any) => apiService.get(`/study-programs/${studyProgramId}/cpl-achievement`, params),
}

export const cpmkAPI = {
  getAll: (params?: any) => apiService.get('/cpmk', params),
  getById: (id: number) => apiService.get(`/cpmk/${id}`),
  create: (data: any) => apiService.post('/cpmk', data),
  update: (id: number, data: any) => apiService.put(`/cpmk/${id}`, data),
  delete: (id: number) => apiService.delete(`/cpmk/${id}`),
  getByCourse: (courseId: number) => apiService.get(`/courses/${courseId}/cpmk`),

  // Sub-CPMK management
  getSubCPMK: (id: number) => apiService.get(`/cpmk/${id}/sub-cpmk`),
  createSubCPMK: (cpmkId: number, data: any) => apiService.post(`/cpmk/${cpmkId}/sub-cpmk`, data),
  updateSubCPMK: (cpmkId: number, subId: number, data: any) => apiService.put(`/cpmk/${cpmkId}/sub-cpmk/${subId}`, data),
  deleteSubCPMK: (cpmkId: number, subId: number) => apiService.delete(`/cpmk/${cpmkId}/sub-cpmk/${subId}`),

  // CPL Relations
  getCPLRelations: (id: number) => apiService.get(`/cpmk/${id}/cpl-relations`),
  createCPLRelation: (cpmkId: number, data: any) => apiService.post(`/cpmk/${cpmkId}/cpl-relations`, data),
  updateCPLRelation: (cpmkId: number, relationId: number, data: any) => apiService.put(`/cpmk/${cpmkId}/cpl-relations/${relationId}`, data),
  deleteCPLRelation: (cpmkId: number, relationId: number) => apiService.delete(`/cpmk/${cpmkId}/cpl-relations/${relationId}`),

  // Bulk operations
  bulkCreateCPLRelations: (cpmkId: number, data: any[]) => apiService.post(`/cpmk/${cpmkId}/cpl-relations/bulk`, { relations: data }),
  validateWeights: (courseId: number) => apiService.get(`/courses/${courseId}/cpmk/validate-weights`),
}

export const assessmentMethodsAPI = {
  getAll: (params?: any) => apiService.get('/assessment-methods', params),
  getById: (id: number) => apiService.get(`/assessment-methods/${id}`),
  create: (data: any) => apiService.post('/assessment-methods', data),
  update: (id: number, data: any) => apiService.put(`/assessment-methods/${id}`, data),
  delete: (id: number) => apiService.delete(`/assessment-methods/${id}`),
  getByType: (type: 'formatif' | 'sumatif') => apiService.get(`/assessment-methods/type/${type}`),
  getByCategory: (category: string) => apiService.get(`/assessment-methods/category/${category}`),
  getTemplates: () => apiService.get('/assessment-methods/templates'),
  getUsageAnalytics: (id: number) => apiService.get(`/assessment-methods/${id}/analytics`),
}

export const assessmentPlansAPI = {
  getAll: (params?: any) => apiService.get('/assessment-plans', params),
  getById: (id: number) => apiService.get(`/assessment-plans/${id}`),
  create: (data: any) => apiService.post('/assessment-plans', data),
  update: (id: number, data: any) => apiService.put(`/assessment-plans/${id}`, data),
  delete: (id: number) => apiService.delete(`/assessment-plans/${id}`),

  // Course-specific operations
  getByCourse: (courseId: number, params?: any) => apiService.get(`/courses/${courseId}/assessment-plans`, params),
  createForCourse: (courseId: number, data: any) => apiService.post(`/courses/${courseId}/assessment-plans`, data),

  // CPMK-specific operations
  getByCPMK: (cpmkId: number) => apiService.get(`/cpmk/${cpmkId}/assessment-plans`),
  createForCPMK: (cpmkId: number, data: any) => apiService.post(`/cpmk/${cpmkId}/assessment-plans`, data),

  // Week-specific operations
  getByWeek: (courseId: number, weekNumber: number) => apiService.get(`/courses/${courseId}/assessment-plans/week/${weekNumber}`),

  // Validation and analytics
  validateWeights: (courseId: number) => apiService.get(`/courses/${courseId}/assessment-plans/validate-weights`),
  validateCoverage: (courseId: number) => apiService.get(`/courses/${courseId}/assessment-plans/validate-coverage`),
  getCalendar: (courseId: number, params?: any) => apiService.get(`/courses/${courseId}/assessment-plans/calendar`, params),
  getWeightDistribution: (courseId: number) => apiService.get(`/courses/${courseId}/assessment-plans/weight-distribution`),

  // Bulk operations
  bulkCreate: (courseId: number, data: any[]) => apiService.post(`/courses/${courseId}/assessment-plans/bulk`, { plans: data }),
  bulkUpdate: (courseId: number, data: any[]) => apiService.put(`/courses/${courseId}/assessment-plans/bulk`, { plans: data }),
  bulkDelete: (courseId: number, ids: number[]) => apiService.post(`/courses/${courseId}/assessment-plans/bulk-delete`, { ids }),

  // Rubric management
  updateRubric: (id: number, rubric: any) => apiService.put(`/assessment-plans/${id}/rubric`, rubric),
  getRubricTemplates: () => apiService.get('/assessment-plans/rubric-templates'),
  validateRubric: (rubric: any) => apiService.post('/assessment-plans/validate-rubric', rubric),
}

export const assessmentResultsAPI = {
  getAll: (params?: any) => apiService.get('/assessment-results', params),
  getById: (id: number) => apiService.get(`/assessment-results/${id}`),
  create: (data: any) => apiService.post('/assessment-results', data),
  update: (id: number, data: any) => apiService.put(`/assessment-results/${id}`, data),
  delete: (id: number) => apiService.delete(`/assessment-results/${id}`),

  // Assessment plan specific
  getByAssessmentPlan: (planId: number, params?: any) => apiService.get(`/assessment-plans/${planId}/results`, params),
  createForAssessmentPlan: (planId: number, data: any) => apiService.post(`/assessment-plans/${planId}/results`, data),

  // Student specific
  getByStudent: (studentId: number, params?: any) => apiService.get(`/students/${studentId}/assessment-results`, params),
  getStudentProgress: (studentId: number, courseId: number) => apiService.get(`/students/${studentId}/courses/${courseId}/progress`),

  // Course analytics
  getCourseAnalytics: (courseId: number, params?: any) => apiService.get(`/courses/${courseId}/assessment-results/analytics`, params),
  getGradeDistribution: (planId: number) => apiService.get(`/assessment-plans/${planId}/grade-distribution`),

  // Bulk operations
  bulkGrade: (planId: number, data: any[]) => apiService.post(`/assessment-plans/${planId}/bulk-grade`, { results: data }),
  exportResults: (planId: number, format: 'excel' | 'pdf') => apiService.get(`/assessment-plans/${planId}/export/${format}`, { responseType: 'blob' }),
}

export const assessmentsAPI = {
  getMethods: (params?: any) => apiService.get('/assessments/methods', params),
  getPlans: (params?: any) => apiService.get('/assessments/plans', params),
  createMethod: (data: any) => apiService.post('/assessments/methods', data),
  createPlan: (data: any) => apiService.post('/assessments/plans', data),
  updateMethod: (id: number, data: any) => apiService.put(`/assessments/methods/${id}`, data),
  updatePlan: (id: number, data: any) => apiService.put(`/assessments/plans/${id}`, data),
  deleteMethod: (id: number) => apiService.delete(`/assessments/methods/${id}`),
  deletePlan: (id: number) => apiService.delete(`/assessments/plans/${id}`),
}

export const reportsAPI = {
  // Report Templates
  getTemplates: (params?: any) => apiService.get('/reports/templates', params),
  getTemplateById: (id: number) => apiService.get(`/reports/templates/${id}`),
  createTemplate: (data: any) => apiService.post('/reports/templates', data),
  updateTemplate: (id: number, data: any) => apiService.put(`/reports/templates/${id}`, data),
  deleteTemplate: (id: number) => apiService.delete(`/reports/templates/${id}`),
  duplicateTemplate: (id: number, name: string) => apiService.post(`/reports/templates/${id}/duplicate`, { name }),

  // Report Generation
  generateReport: (templateId: number, params: any) => apiService.post(`/reports/templates/${templateId}/generate`, params),
  getReportInstances: (params?: any) => apiService.get('/reports/instances', params),
  getReportInstance: (id: number) => apiService.get(`/reports/instances/${id}`),
  downloadReport: (id: number) => apiService.get(`/reports/instances/${id}/download`, { responseType: 'blob' }),
  deleteReportInstance: (id: number) => apiService.delete(`/reports/instances/${id}`),

  // Scheduled Reports
  scheduleReport: (templateId: number, config: any) => apiService.post(`/reports/templates/${templateId}/schedule`, config),
  getScheduledReports: (params?: any) => apiService.get('/reports/scheduled', params),
  updateSchedule: (id: number, config: any) => apiService.put(`/reports/scheduled/${id}`, config),
  deleteSchedule: (id: number) => apiService.delete(`/reports/scheduled/${id}`),

  // Pre-built Reports
  getCPMKAchievement: (params?: any) => apiService.get('/reports/cpmk-achievement', params),
  getCPLMapping: (params?: any) => apiService.get('/reports/cpl-mapping', params),
  getAssessmentAnalytics: (params?: any) => apiService.get('/reports/assessment-analytics', params),
  getCoursePerformance: (params?: any) => apiService.get('/reports/course-performance', params),
  getStudentProgress: (params?: any) => apiService.get('/reports/student-progress', params),
  getFacultyOverview: (params?: any) => apiService.get('/reports/faculty-overview', params),
  getComplianceReport: (params?: any) => apiService.get('/reports/compliance', params),

  // Dashboard & Analytics
  getDashboard: (params?: any) => apiService.get('/reports/dashboard', params),
  getDashboardWidgets: () => apiService.get('/reports/dashboard/widgets'),
  updateDashboardLayout: (widgets: any[]) => apiService.put('/reports/dashboard/layout', { widgets }),
  getAnalyticsSummary: (params?: any) => apiService.get('/reports/analytics/summary', params),
  getTrendAnalysis: (params?: any) => apiService.get('/reports/analytics/trends', params),

  // Export Functions
  exportPDF: (type: string, params?: any) => apiService.get(`/reports/export/pdf/${type}`, { ...params, responseType: 'blob' }),
  exportExcel: (type: string, params?: any) => apiService.get(`/reports/export/excel/${type}`, { ...params, responseType: 'blob' }),
  exportCSV: (type: string, params?: any) => apiService.get(`/reports/export/csv/${type}`, { ...params, responseType: 'blob' }),
  exportJSON: (type: string, params?: any) => apiService.get(`/reports/export/json/${type}`, params),

  // Bulk Export
  bulkExport: (reportIds: number[], format: string) => api.post('/reports/bulk-export', { report_ids: reportIds, format }, { responseType: 'blob' }),

  // Report Sharing
  shareReport: (id: number, config: any) => apiService.post(`/reports/instances/${id}/share`, config),
  getSharedReports: (params?: any) => apiService.get('/reports/shared', params),
  revokeShare: (id: number, shareId: string) => apiService.delete(`/reports/instances/${id}/share/${shareId}`),

  // Report Analytics
  getUsageAnalytics: (params?: any) => apiService.get('/reports/analytics/usage', params),
  getPopularReports: (params?: any) => apiService.get('/reports/analytics/popular', params),
  getPerformanceMetrics: (params?: any) => apiService.get('/reports/analytics/performance', params),
}

export default api
