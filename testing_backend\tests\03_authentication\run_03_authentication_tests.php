<?php

/**
 * Authentication System Tests
 * 
 * Tests user registration, login, JWT tokens, and role-based access
 */

// Ensure we have access to the test runner
if (!isset($testRunner)) {
    die("This test must be run through the TestRunner\n");
}

$testRunner->logSubSection('Authentication System Tests');

$tests = [
    'backend_server' => 'Backend Server Availability',
    'auth_endpoints' => 'Authentication Endpoints',
    'user_login' => 'User Login Process',
    'jwt_token_validation' => 'JWT Token Validation',
    'protected_routes' => 'Protected Route Access',
    'role_based_access' => 'Role-Based Access Control',
    'token_refresh' => 'Token Refresh Mechanism',
    'logout_process' => 'Logout Process'
];

$totalTests = count($tests);
$currentTest = 0;
$passedTests = 0;
$authToken = null;
$refreshToken = null;

foreach ($tests as $testKey => $testName) {
    $currentTest++;
    $testRunner->logProgress($currentTest, $totalTests, "Running {$testName}");
    
    $startTime = microtime(true);
    $testPassed = false;
    $message = '';
    $details = [];

    try {
        $baseUrl = $config['api']['base_url'];
        
        switch ($testKey) {
            case 'backend_server':
                // Check if backend server is running
                $url = str_replace('/api/v1', '', $baseUrl);
                
                try {
                    $response = $testRunner->makeHttpRequest('GET', $url);
                    $testPassed = $response['status_code'] === 200;
                    $message = $testPassed 
                        ? "Backend server is running (HTTP {$response['status_code']})"
                        : "Backend server returned HTTP {$response['status_code']}";
                    $details = [
                        'url' => $url,
                        'status_code' => $response['status_code'],
                        'response_time_ms' => $response['duration']
                    ];
                } catch (Exception $e) {
                    $testPassed = false;
                    $message = "Backend server is not accessible: " . $e->getMessage();
                    $details = ['error' => $e->getMessage()];
                }
                break;

            case 'auth_endpoints':
                // Check if authentication endpoints are accessible
                $authEndpoints = $config['api']['test_endpoints']['auth'];
                $endpointResults = [];
                $accessibleEndpoints = 0;
                
                foreach ($authEndpoints as $name => $endpoint) {
                    $url = $baseUrl . $endpoint;
                    
                    try {
                        // For login endpoint, expect 400/422 (missing credentials)
                        // For others, expect 401 (unauthorized)
                        $response = $testRunner->makeHttpRequest('POST', $url);
                        $expectedCodes = $name === 'login' ? [400, 422] : [401];
                        $isAccessible = in_array($response['status_code'], $expectedCodes);
                        
                        if ($isAccessible) {
                            $accessibleEndpoints++;
                        }
                        
                        $endpointResults[$name] = [
                            'url' => $url,
                            'status_code' => $response['status_code'],
                            'accessible' => $isAccessible,
                            'response_time_ms' => $response['duration']
                        ];
                        
                    } catch (Exception $e) {
                        $endpointResults[$name] = [
                            'url' => $url,
                            'accessible' => false,
                            'error' => $e->getMessage()
                        ];
                    }
                }
                
                $testPassed = $accessibleEndpoints === count($authEndpoints);
                $message = $testPassed 
                    ? "All authentication endpoints are accessible ({$accessibleEndpoints}/{count($authEndpoints)})"
                    : "Some authentication endpoints are not accessible ({$accessibleEndpoints}/{count($authEndpoints)})";
                $details = ['endpoints' => $endpointResults];
                break;

            case 'user_login':
                // Test user login with test credentials
                $loginUrl = $baseUrl . '/auth/login';
                $testUser = $config['test_data']['users']['admin'];
                
                $loginData = [
                    'username' => $testUser['username'],
                    'password' => $testUser['password']
                ];
                
                try {
                    $response = $testRunner->makeHttpRequest('POST', $loginUrl, $loginData);
                    $responseData = $response['json'];
                    
                    $testPassed = $response['status_code'] === 200 && isset($responseData['data']['token']);
                    
                    if ($testPassed) {
                        $authToken = $responseData['data']['token'];
                        $refreshToken = $responseData['data']['refresh_token'] ?? null;
                        $message = "User login successful";
                        $details = [
                            'status_code' => $response['status_code'],
                            'has_token' => !empty($authToken),
                            'has_refresh_token' => !empty($refreshToken),
                            'response_time_ms' => $response['duration'],
                            'user_data' => $responseData['data']['user'] ?? null
                        ];
                    } else {
                        $message = "User login failed: " . ($responseData['message'] ?? 'Unknown error');
                        $details = [
                            'status_code' => $response['status_code'],
                            'response' => $responseData,
                            'login_data' => $loginData
                        ];
                    }
                    
                } catch (Exception $e) {
                    $testPassed = false;
                    $message = "Login request failed: " . $e->getMessage();
                    $details = ['error' => $e->getMessage()];
                }
                break;

            case 'jwt_token_validation':
                // Validate JWT token structure and content
                if (!$authToken) {
                    $testPassed = false;
                    $message = "No auth token available for validation";
                    $details = ['auth_token' => null];
                    break;
                }
                
                try {
                    // Basic JWT structure validation (header.payload.signature)
                    $tokenParts = explode('.', $authToken);
                    $hasValidStructure = count($tokenParts) === 3;
                    
                    if ($hasValidStructure) {
                        // Decode payload (without verification for testing)
                        $payload = json_decode(base64_decode($tokenParts[1]), true);
                        $hasValidPayload = is_array($payload) && isset($payload['exp']);
                        
                        $testPassed = $hasValidStructure && $hasValidPayload;
                        $message = $testPassed 
                            ? "JWT token has valid structure and payload"
                            : "JWT token structure is invalid";
                        $details = [
                            'token_parts_count' => count($tokenParts),
                            'has_valid_structure' => $hasValidStructure,
                            'has_valid_payload' => $hasValidPayload,
                            'payload_keys' => $hasValidPayload ? array_keys($payload) : null,
                            'expires_at' => $hasValidPayload ? date('Y-m-d H:i:s', $payload['exp']) : null
                        ];
                    } else {
                        $testPassed = false;
                        $message = "JWT token does not have valid structure";
                        $details = [
                            'token_parts_count' => count($tokenParts),
                            'expected_parts' => 3
                        ];
                    }
                    
                } catch (Exception $e) {
                    $testPassed = false;
                    $message = "JWT token validation failed: " . $e->getMessage();
                    $details = ['error' => $e->getMessage()];
                }
                break;

            case 'protected_routes':
                // Test access to protected routes with and without token
                $profileUrl = $baseUrl . '/auth/profile';
                
                try {
                    // Test without token (should fail)
                    $responseWithoutToken = $testRunner->makeHttpRequest('GET', $profileUrl);
                    $unauthorizedCorrectly = $responseWithoutToken['status_code'] === 401;
                    
                    // Test with token (should succeed)
                    $responseWithToken = null;
                    $authorizedCorrectly = false;
                    
                    if ($authToken) {
                        $headers = ['Authorization' => 'Bearer ' . $authToken];
                        $responseWithToken = $testRunner->makeHttpRequest('GET', $profileUrl, null, $headers);
                        $authorizedCorrectly = $responseWithToken['status_code'] === 200;
                    }
                    
                    $testPassed = $unauthorizedCorrectly && $authorizedCorrectly;
                    $message = $testPassed 
                        ? "Protected routes work correctly (unauthorized without token, authorized with token)"
                        : "Protected routes not working correctly";
                    $details = [
                        'without_token' => [
                            'status_code' => $responseWithoutToken['status_code'],
                            'unauthorized_correctly' => $unauthorizedCorrectly
                        ],
                        'with_token' => [
                            'status_code' => $responseWithToken['status_code'] ?? null,
                            'authorized_correctly' => $authorizedCorrectly,
                            'has_auth_token' => !empty($authToken)
                        ]
                    ];
                    
                } catch (Exception $e) {
                    $testPassed = false;
                    $message = "Protected route test failed: " . $e->getMessage();
                    $details = ['error' => $e->getMessage()];
                }
                break;

            case 'role_based_access':
                // Test role-based access control
                if (!$authToken) {
                    $testPassed = false;
                    $message = "No auth token available for role testing";
                    break;
                }
                
                try {
                    // Test access to admin-only endpoint (users list)
                    $usersUrl = $baseUrl . '/users';
                    $headers = ['Authorization' => 'Bearer ' . $authToken];
                    $response = $testRunner->makeHttpRequest('GET', $usersUrl, null, $headers);
                    
                    // Admin user should have access
                    $testPassed = in_array($response['status_code'], [200, 404]); // 404 if endpoint not implemented yet
                    $message = $testPassed 
                        ? "Role-based access working (admin can access user management)"
                        : "Role-based access failed (HTTP {$response['status_code']})";
                    $details = [
                        'endpoint' => $usersUrl,
                        'status_code' => $response['status_code'],
                        'response_time_ms' => $response['duration'],
                        'user_role' => 'admin'
                    ];
                    
                } catch (Exception $e) {
                    $testPassed = false;
                    $message = "Role-based access test failed: " . $e->getMessage();
                    $details = ['error' => $e->getMessage()];
                }
                break;

            case 'token_refresh':
                // Test token refresh mechanism
                if (!$refreshToken) {
                    $testPassed = false;
                    $message = "No refresh token available for testing";
                    $details = ['refresh_token' => null];
                    break;
                }
                
                try {
                    $refreshUrl = $baseUrl . '/auth/refresh';
                    $refreshData = ['refresh_token' => $refreshToken];
                    
                    $response = $testRunner->makeHttpRequest('POST', $refreshUrl, $refreshData);
                    $responseData = $response['json'];
                    
                    $testPassed = $response['status_code'] === 200 && isset($responseData['data']['token']);
                    $message = $testPassed 
                        ? "Token refresh successful"
                        : "Token refresh failed: " . ($responseData['message'] ?? 'Unknown error');
                    $details = [
                        'status_code' => $response['status_code'],
                        'has_new_token' => isset($responseData['data']['token']),
                        'response_time_ms' => $response['duration']
                    ];
                    
                } catch (Exception $e) {
                    $testPassed = false;
                    $message = "Token refresh request failed: " . $e->getMessage();
                    $details = ['error' => $e->getMessage()];
                }
                break;

            case 'logout_process':
                // Test logout process
                if (!$authToken) {
                    $testPassed = false;
                    $message = "No auth token available for logout testing";
                    break;
                }
                
                try {
                    $logoutUrl = $baseUrl . '/auth/logout';
                    $headers = ['Authorization' => 'Bearer ' . $authToken];
                    
                    $response = $testRunner->makeHttpRequest('POST', $logoutUrl, null, $headers);
                    
                    $testPassed = in_array($response['status_code'], [200, 204]);
                    $message = $testPassed 
                        ? "Logout successful"
                        : "Logout failed (HTTP {$response['status_code']})";
                    $details = [
                        'status_code' => $response['status_code'],
                        'response_time_ms' => $response['duration']
                    ];
                    
                } catch (Exception $e) {
                    $testPassed = false;
                    $message = "Logout request failed: " . $e->getMessage();
                    $details = ['error' => $e->getMessage()];
                }
                break;
        }

    } catch (Exception $e) {
        $testPassed = false;
        $message = "Exception: " . $e->getMessage();
        $details = [
            'exception' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ];
    }

    $duration = round((microtime(true) - $startTime) * 1000);
    $status = $testPassed ? 'PASS' : 'FAIL';
    
    $testRunner->logTestResult($testName, $status, $message, $duration, $details);
    
    if ($testPassed) {
        $passedTests++;
    }
}

// Summary for this test category
$testRunner->logSubSection('Authentication Tests Summary');
$testRunner->log('info', "Passed: {$passedTests}/{$totalTests} tests", 'SUMMARY');

if ($passedTests === $totalTests) {
    $testRunner->logSuccess("All authentication tests passed! Authentication system is working correctly.");
    return true;
} else {
    $failedTests = $totalTests - $passedTests;
    $testRunner->logError("Authentication tests failed! {$failedTests} test(s) need attention.");
    return false;
}
